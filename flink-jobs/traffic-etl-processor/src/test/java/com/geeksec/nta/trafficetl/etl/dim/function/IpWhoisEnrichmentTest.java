package com.geeksec.nta.traffic.etl.etl.dim.function;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

// import com.geeksec.flink.common.utils.knowledgebase.IpWhoisManager;

/**
 * IP WHOIS Enrichment功能测试类
 * 测试IP维度表中WHOIS信息的获取和处理
 *
 * <AUTHOR>
 */
@DisplayName("IP WHOIS Enrichment功能测试")
public class IpWhoisEnrichmentTest {

    private IpWhoisManager ipWhoisManager;

    @BeforeEach
    public void setUp() {
        ipWhoisManager = IpWhoisManager.getInstance();
    }

    @Test
    @DisplayName("测试获取IP WHOIS信息")
    public void testGetIpWhoisInfo() {
        // 测试示例IP地址（根据您的RIR Data调整）
        String testIp = "*************";
        IpWhoisManager.IpWhoisInfo whoisInfo = ipWhoisManager.getIpWhoisInfo(testIp);

        if (whoisInfo != null) {
            assertNotNull(whoisInfo.getNetname(), "网络名称不应为null");
            assertNotNull(whoisInfo.getCountry(), "国家信息不应为null");

            System.out.println("IP: " + testIp);
            System.out.println("网络名称: " + whoisInfo.getNetname());
            System.out.println("描述: " + whoisInfo.getDescription());
            System.out.println("国家: " + whoisInfo.getCountry());
            System.out.println("状态: " + whoisInfo.getStatus());
            System.out.println("组织摘要: " + whoisInfo.getOrganizationSummary());
        } else {
            System.out.println("未找到IP " + testIp + " 的WHOIS信息");
            System.out.println("请确保RIR Data JSONL文件已正确配置并包含此IP段");
        }
    }

    @Test
    @DisplayName("测试IP组织信息获取")
    public void testGetIpOrganization() {
        String testIp = "*************";
        String organization = ipWhoisManager.getIpOrganization(testIp);

        assertNotNull(organization, "组织信息不应为null");
        System.out.println("IP " + testIp + " 的组织信息: " + organization);
    }

    @Test
    @DisplayName("测试IP国家信息获取")
    public void testGetIpCountry() {
        String testIp = "*************";
        String country = ipWhoisManager.getIpCountry(testIp);

        assertNotNull(country, "国家信息不应为null");
        System.out.println("IP " + testIp + " 的国家信息: " + country);
    }

    @Test
    @DisplayName("测试无效IP处理")
    public void testInvalidIpHandling() {
        String[] invalidIps = {
            null,
            "",
            "invalid.ip",
            "999.999.999.999",
            "192.168.1"
        };

        for (String invalidIp : invalidIps) {
            IpWhoisManager.IpWhoisInfo whoisInfo = ipWhoisManager.getIpWhoisInfo(invalidIp);
            assertNull(whoisInfo, "无效IP " + invalidIp + " 应该返回null");
        }
    }

    @Test
    @DisplayName("测试内网IP处理")
    public void testInternalIpHandling() {
        String[] internalIps = {
            "***********",
            "********",
            "**********",
            "127.0.0.1"
        };

        for (String internalIp : internalIps) {
            IpWhoisManager.IpWhoisInfo whoisInfo = ipWhoisManager.getIpWhoisInfo(internalIp);
            // 内网IP可能没有WHOIS信息，这是正常的
            System.out.println("内网IP " + internalIp + " 的WHOIS信息: " +
                (whoisInfo != null ? whoisInfo.getOrganizationSummary() : "无"));
        }
    }

    @Test
    @DisplayName("测试批量IP查询性能")
    public void testBulkIpQuery() {
        String[] testIps = {
            "*******",
            "*******",
            "**************",
            "*******",
            "***************"
        };

        long startTime = System.currentTimeMillis();

        for (String ip : testIps) {
            IpWhoisManager.IpWhoisInfo whoisInfo = ipWhoisManager.getIpWhoisInfo(ip);
            System.out.println("IP: " + ip + " -> " +
                (whoisInfo != null ? whoisInfo.getOrganizationSummary() : "无WHOIS信息"));
        }

        long duration = System.currentTimeMillis() - startTime;
        System.out.println("查询 " + testIps.length + " 个IP的WHOIS信息耗时: " + duration + "ms");

        // 性能断言：平均每个IP查询不应超过100ms
        assertTrue(duration / testIps.length < 100,
            "平均每个IP查询时间不应超过100ms，实际: " + (duration / testIps.length) + "ms");
    }

    @Test
    @DisplayName("测试缓存机制")
    public void testCachingMechanism() {
        String testIp = "*******";

        // 第一次查询
        long startTime1 = System.currentTimeMillis();
        IpWhoisManager.IpWhoisInfo whoisInfo1 = ipWhoisManager.getIpWhoisInfo(testIp);
        long duration1 = System.currentTimeMillis() - startTime1;

        // 第二次查询（应该从缓存获取）
        long startTime2 = System.currentTimeMillis();
        IpWhoisManager.IpWhoisInfo whoisInfo2 = ipWhoisManager.getIpWhoisInfo(testIp);
        long duration2 = System.currentTimeMillis() - startTime2;

        // 验证结果一致性
        if (whoisInfo1 != null && whoisInfo2 != null) {
            assertEquals(whoisInfo1.getNetname(), whoisInfo2.getNetname(),
                "两次查询应该返回相同的网络名称");
            assertEquals(whoisInfo1.getCountry(), whoisInfo2.getCountry(),
                "两次查询应该返回相同的国家信息");
        }

        System.out.println("第一次查询耗时: " + duration1 + "ms");
        System.out.println("第二次查询耗时: " + duration2 + "ms");

        // 缓存查询通常应该更快
        if (duration1 > 10) { // 只有在第一次查询时间足够长时才验证缓存效果
            assertTrue(duration2 <= duration1, "缓存查询应该不慢于首次查询");
        }
    }

    @Test
    @DisplayName("测试统计信息")
    public void testStatistics() {
        String statistics = ipWhoisManager.getStatistics();
        assertNotNull(statistics, "统计信息不应为null");
        assertTrue(statistics.contains("IP段数量"), "统计信息应包含IP段数量");
        assertTrue(statistics.contains("缓存大小"), "统计信息应包含缓存大小");

        System.out.println("IP WHOIS管理器统计信息: " + statistics);
    }

    @Test
    @DisplayName("测试数据刷新功能")
    public void testRefreshFunction() {
        String testIp = "*******";

        // 查询一次以填充缓存
        IpWhoisManager.IpWhoisInfo whoisInfo1 = ipWhoisManager.getIpWhoisInfo(testIp);

        // 刷新数据
        ipWhoisManager.refresh();

        // 再次查询
        IpWhoisManager.IpWhoisInfo whoisInfo2 = ipWhoisManager.getIpWhoisInfo(testIp);

        // 验证刷新后的结果
        if (whoisInfo1 != null && whoisInfo2 != null) {
            assertEquals(whoisInfo1.getNetname(), whoisInfo2.getNetname(),
                "刷新前后的WHOIS信息应该一致");
        }

        System.out.println("数据刷新测试完成");
    }

    @Test
    @DisplayName("测试IPv6地址处理")
    public void testIpv6Handling() {
        String[] ipv6Addresses = {
            "2001:4860:4860::8888",  // Google DNS
            "2606:4700:4700::1111",  // Cloudflare DNS
            "::1",                   // localhost
            "fe80::1"                // link-local
        };

        for (String ipv6 : ipv6Addresses) {
            IpWhoisManager.IpWhoisInfo whoisInfo = ipWhoisManager.getIpWhoisInfo(ipv6);
            System.out.println("IPv6 " + ipv6 + " 的WHOIS信息: " +
                (whoisInfo != null ? whoisInfo.getOrganizationSummary() : "无或不支持"));
        }
    }

    @Test
    @DisplayName("测试特殊IP地址")
    public void testSpecialIpAddresses() {
        String[] specialIps = {
            "0.0.0.0",           // 全零地址
            "***************",   // 广播地址
            "*********",         // 组播地址
            "***********",       // 链路本地地址
            "************",      // 测试网络
            "***********"        // 文档用途
        };

        for (String specialIp : specialIps) {
            try {
                IpWhoisManager.IpWhoisInfo whoisInfo = ipWhoisManager.getIpWhoisInfo(specialIp);
                System.out.println("特殊IP " + specialIp + " 的WHOIS信息: " +
                    (whoisInfo != null ? whoisInfo.getOrganizationSummary() : "无"));
            } catch (Exception e) {
                System.out.println("处理特殊IP " + specialIp + " 时出现异常: " + e.getMessage());
            }
        }
    }
}
