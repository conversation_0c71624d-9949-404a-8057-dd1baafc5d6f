package com.geeksec.nta.traffic.etl.etl.ods.converter.protocol;

import com.geeksec.flink.common.utils.time.TimeUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.traffic.etl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * L2TP协议转换器
 * 将L2TP协议的protobuf消息转换为Doris ods_l2tp_protocol_metadata表格式
 *
 * <AUTHOR>
 */
@Slf4j
public class L2tpConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasL2Tp()) {
            log.warn("JKNmsg does not contain L2tp message");
            return null;
        }
        Row row = Row.withNames();
        ZMPNMsg.l2tp_msg l2tpMsg = msg.getL2Tp();
        if (l2tpMsg.hasCommMsg()){
            // 设置通用字段
            enrichComMsg(row, l2tpMsg.getCommMsg());
        }

        // 设置L2TP特定字段（需要根据实际protobuf定义实现）
        // TODO: 根据实际的L2TP protobuf消息结构实现字段映射
        row.setField(FieldConstants.PROTOCOL_FAMILY, l2tpMsg.getProtocolFamily());
        row.setField(FieldConstants.COMMUNICATION_RATE, l2tpMsg.getCommunicationRate());
        row.setField(FieldConstants.DIRECTION, l2tpMsg.getDirection());
        row.setField(FieldConstants.PROTOCOL_VERSION, l2tpMsg.getProtocolVersion());
        row.setField(FieldConstants.FRAMING_CAPABILITIES, l2tpMsg.getFramingCapabilities());
        row.setField(FieldConstants.BEARER_CAPABILITIES, l2tpMsg.getBearerCapabilities());
        row.setField(FieldConstants.SERVER_HOSTNAME, l2tpMsg.getServerHostname());
        row.setField(FieldConstants.CLIENT_HOSTNAME, l2tpMsg.getClientHostname());
        row.setField(FieldConstants.SERVER_VENDORNAME, l2tpMsg.getServerVendorname());
        row.setField(FieldConstants.CLIENT_VENDORNAME, l2tpMsg.getClientVendorname());
        row.setField(FieldConstants.CALLING_NUMBER, l2tpMsg.getCallingNumber());
        row.setField(FieldConstants.PROXY_AUTHEN_TYPE, l2tpMsg.getProxyAuthenType());
        row.setField(FieldConstants.PROXY_AUTHEN_NAME, l2tpMsg.getProxyAuthenName());
        row.setField(FieldConstants.IS_NEGOTIATE_SUCCESS, l2tpMsg.getIsNegotiateSuccess());
        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.L2TP_STREAM;
    }
}
