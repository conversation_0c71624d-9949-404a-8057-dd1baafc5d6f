package com.geeksec.nta.traffic.etl.etl.graph.extractor.session.edge;

import com.geeksec.flink.common.utils.crypto.HashUtils;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
public class OrgOwnsIPEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.ORG_OWNS_IP_TAG;
    }

    /**
     * 组织拥有IP (IP -> ORG)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        Set<String> domainSet = getSessionDomain(value);
        List<Row> edgeList = new ArrayList<>();
        // 处理锚域名MD5
        for (String domain : domainSet) {
            if (domain.length() > 200) {
                domain = HashUtils.md5(domain);
            }
            // todo 知识库读取方法待定
            // DomainWhoisManager
            String orgName = "orgName";
            Row edgeRow = Row.of(domain,
                    orgName,
                    0 // rank暂定0
            );
            edgeList.add(edgeRow);
        }
        return edgeList;
    }
}
