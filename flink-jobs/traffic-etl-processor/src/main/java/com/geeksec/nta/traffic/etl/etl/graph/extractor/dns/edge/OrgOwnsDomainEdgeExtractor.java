package com.geeksec.nta.traffic.etl.etl.graph.extractor.dns.edge;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.flink.common.network.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.dim.function.DomainDimensionTableFunction;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
public class OrgOwnsDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.ORG_OWNS_DOMAIN_TAG;
    }

    /**
     * 组织拥有域名 (DOMAIN -> ORG)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        Set<String> domainSet = new HashSet<>();
        DomainDimensionTableFunction.getDnsDomain(value, domainSet);
        List<Row> edgeList = new ArrayList<>();
        // 处理域名MD5
        for (String domain : domainSet) {
            domain = DomainUtils.formatDomain(domain);

            // todo 知识库读取方法待定
            // DomainWhoisManager
            String orgName = "orgName";
            Row edgeRow = Row.of(domain, orgName,
                    0 // rank暂定0
                    );
            edgeList.add(edgeRow);
        }
        return edgeList;
    }
}
