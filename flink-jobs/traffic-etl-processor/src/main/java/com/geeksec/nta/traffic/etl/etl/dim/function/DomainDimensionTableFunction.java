package com.geeksec.nta.traffic.etl.etl.dim.function;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.geeksec.flink.common.network.DomainUtils;
import com.geeksec.flink.common.network.NetworkUtils;
import com.geeksec.flink.common.utils.crypto.HashUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.proto.ZMPNMsg;

import lombok.extern.slf4j.Slf4j;

// import com.geeksec.flink.common.utils.knowledgebase.DomainWhoisManager;
// import com.geeksec.flink.common.utils.knowledgebase.MaliciousDomainManager;
// import com.geeksec.flink.common.utils.knowledgebase.PublicSuffixManager;
// import com.geeksec.flink.common.utils.knowledgebase.TrancoRankingManager;


/**
 * 域名维度表处理函数
 * 专门用于生成符合dim_domain和dim_registrable_domain表结构的维度数据
 * 使用Flink Backend State管理域名信息缓存，并集成Public Suffix List进行域名解析。
 *
 * <AUTHOR>
 */
@Slf4j
public class DomainDimensionTableFunction extends ProcessFunction<Row, Row> {

    /**
     * 域名维度侧输出标签 (for dim_domain)
     */
    public static final OutputTag<Row> DOMAIN_DIM_TAG = new OutputTag<Row>("domain_dim") {};
    public static final OutputTag<Row> DOMAIN_VERTEX_TAG = new OutputTag<Row>("domain_tag") {};
    /**
     * 可注册域名维度侧输出标签 (for dim_registrable_domain)
     */
    public static final OutputTag<Row> REGISTRABLE_DOMAIN_DIM_TAG = new OutputTag<Row>("reg_domain_dim") {};
    public static final OutputTag<Row> REGISTRABLE_DOMAIN_VERTEX_TAG = new OutputTag<Row>("reg_domain_tag") {};
    // 缓存相关
    private transient MapState<String, Map<String, Object>> domainInfoCache;
    private final Duration cacheTtl = Duration.ofHours(24); // 缓存24小时

    // 域名相关管理器
    private transient TrancoRankingManager trancoRankingManager;
    private transient DomainWhoisManager domainWhoisManager;
    private transient MaliciousDomainManager maliciousDomainManager;
    // 定义Map中用于存储域名属性的Key常量
    private static final String KEY_ORIGINAL_DOMAIN = "original_domain";
    private static final String KEY_NORMALIZED_DOMAIN = "normalized_domain";
    private static final String KEY_REGISTRABLE_DOMAIN = "registrable_domain";
    private static final String KEY_PUBLIC_SUFFIX = "public_suffix";
    private static final String KEY_SUBDOMAIN_PART = "subdomain_part";
    private static final String KEY_IS_VALID_DOMAIN = "is_valid_domain";
    private static final String KEY_IS_MALICIOUS = "is_malicious";
    private static final String KEY_DOMAIN_RANK = "domain_rank";
    private static final String KEY_REGISTRAR_NAME = "registrar_name";
    private static final String KEY_CREATED_DATE = "created_date";
    private static final String KEY_UPDATED_DATE = "updated_date";
    private static final String KEY_EXPIRES_DATE = "expires_date";
    private static final String KEY_REGISTRANT_ORG = "registrant_org";
    private static final String KEY_REGISTRANT_COUNTRY = "registrant_country";
    /**
     * 域名端口分隔符
     */
    private static final String DOMAIN_PORT_SEPARATOR = ":";
    // 元数据类型
    private String metaDataType;
    public DomainDimensionTableFunction(String metaDataType) {
        this.metaDataType = metaDataType;
    }
    public DomainDimensionTableFunction() {}

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(cacheTtl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        MapStateDescriptor<String, Map<String, Object>> descriptor = new MapStateDescriptor<>(
                "domain-dimension-cache",
                TypeInformation.of(new TypeHint<String>() {}), TypeInformation.of(new TypeHint<Map<String, Object>>() {})
        );
        descriptor.enableTimeToLive(ttlConfig);
        domainInfoCache = getRuntimeContext().getMapState(descriptor);

        // 初始化各种管理器
        trancoRankingManager = TrancoRankingManager.getInstance();
        domainWhoisManager = DomainWhoisManager.getInstance();
        maliciousDomainManager = MaliciousDomainManager.getInstance();

        log.info("域名维度表函数已初始化，恶意域名总数: {}", maliciousDomainManager.getMaliciousDomainCount());
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        Set<String> domains = new HashSet<>();
        // dns查询次数
        Integer totalRequest = 0;
        switch (metaDataType) {
            case "ssl":
                getSSLDomain(value, domains);
                break;
            case "dns":
                getDnsDomain(value, domains);
                totalRequest = (Integer) value.getField(FieldConstants.FIELD_DNS_QUE);
                break;
            case "http":
                getHttpDomain(value, domains);
                break;
            case "session":
                getSessionDomain(value, domains);
                break;
            default:
                log.error("未知的元数据类型: {}", metaDataType);
                break;
        }

        for (String originalDomain : domains) {
            LocalDateTime processingTime = LocalDateTime.now(); // Capture current time for consistent timestamps
            Map<String, Object> enrichedInfo = getEnrichedDomainInfo(originalDomain, processingTime);

            if (enrichedInfo.get(KEY_IS_VALID_DOMAIN) == Boolean.TRUE) {
                Row domainDimRow = createDomainDimensionRow(enrichedInfo, totalRequest);
                if (domainDimRow != null) {
                    ctx.output(DOMAIN_DIM_TAG, domainDimRow);
                }

                Row domainVertexRow = createDomainVertexRow(enrichedInfo);
                if (domainVertexRow != null) {
                    ctx.output(DOMAIN_VERTEX_TAG, domainVertexRow);
                }

                Row registrableDomainDimRow = createRegistrableDomainDimensionRow(enrichedInfo);
                if (registrableDomainDimRow != null) {
                    ctx.output(REGISTRABLE_DOMAIN_DIM_TAG, registrableDomainDimRow);
                }

                Row registrableDomainVertexRow = createRegistrableDomainVertexRow(enrichedInfo);
                if (registrableDomainVertexRow != null) {
                    ctx.output(REGISTRABLE_DOMAIN_VERTEX_TAG, registrableDomainDimRow);
                }
            }
        }
        out.collect(value);
    }

    private void getSessionDomain(Row value, Set<String> domains) {
        // 获取HTTP信息列表
        List<ZMPNMsg.single_http> httpInfoList = (List<ZMPNMsg.single_http>) value.getField(FieldConstants.SESSION_HTTP_LIST);
        // 获取DNS信息列表
        List<ZMPNMsg.single_dns> dnsInfoList = (List<ZMPNMsg.single_dns>) value.getField(FieldConstants.SESSION_DNS_LIST);
        // 获取SSL信息列表
        List<ZMPNMsg.single_ssl> sslInfoList = (List<ZMPNMsg.single_ssl>) value.getField(FieldConstants.SESSION_SSL_LIST);

        // 处理HTTP中的域名
        if (CollectionUtils.isNotEmpty(httpInfoList)) {
            for (ZMPNMsg.single_http singleHttp : httpInfoList) {
                try {
                    if (singleHttp.hasResponse()) {
                        String httpDomainAddr = singleHttp.getHost();
                        if (DomainUtils.isValidDomain(httpDomainAddr)) {
                            if (httpDomainAddr.contains(DOMAIN_PORT_SEPARATOR)) {
                                httpDomainAddr = httpDomainAddr.split(DOMAIN_PORT_SEPARATOR)[0];
                            }
                            domains.add(httpDomainAddr);
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理HTTP域名信息失败: {}", e.getMessage());
                }
            }
        }

        // 处理DNS中的域名
        if (CollectionUtils.isNotEmpty(dnsInfoList)) {
            for (ZMPNMsg.single_dns singleDns : dnsInfoList) {
                String dnsDomainAddr = singleDns.getDomain();
                if (DomainUtils.isValidDomain(dnsDomainAddr)) {
                    domains.add(dnsDomainAddr);
                }
            }
        }

        // 处理SSL中的域名
        if (CollectionUtils.isNotEmpty(sslInfoList)) {
            ZMPNMsg.single_ssl singleSsl = sslInfoList.get(0);
            String sslDomainAddr = singleSsl.getChServerName();
            if (DomainUtils.isValidDomain(sslDomainAddr)) {
                if (sslDomainAddr.contains(DOMAIN_PORT_SEPARATOR)) {
                    sslDomainAddr = sslDomainAddr.split(DOMAIN_PORT_SEPARATOR)[0];
                }
                domains.add(sslDomainAddr);
            }
        }
    }

    public static void getDnsDomain(Row value, Set<String> domains) {
        // 从dns协议元数据中获取域名
        // 1.Domain字段
        String domain = value.getField(FieldConstants.FIELD_DNS_DOMAIN).toString();
        if (!StringUtil.isNullOrEmpty(domain) && DomainUtils.isValidDomain(domain)) {
            domains.add(domain.length() > 200 ? HashUtils.md5(domain) : domain);
        }

        // 2.CNAME 字段
        Integer ansNum = Integer.valueOf(value.getField(FieldConstants.FIELD_DNS_ANS).toString());
        if (ansNum != 0) {
            List<Map<String, Object>> answerMapList = JSON.parseObject(
                    value.getField(FieldConstants.FIELD_DNS_ANS).toString(),
                    new TypeReference<List<Map<String, Object>>>() {});
            for (Map<String, Object> answerMap : answerMapList) {
                Integer type = (Integer) answerMap.get("type");
                String domainAddr = StringUtils.EMPTY;
                // type 为 5时解析出CNAME
                if (type == 5) {
                    domainAddr = (String) answerMap.get("value");
                } else if (type == 1 || type == 28) {
                    // 对于解析完成后的结果，怕会有遗漏，在name字段获取域名的信息
                    domainAddr = (String) answerMap.get("name");
                }
                if (DomainUtils.isValidDomain(domainAddr)) {
                    domains.add(DomainUtils.formatDomain(domain));
                }
            }
        }
    }

    private static void getHttpDomain(Row value, Set<String> domains) {
        // 从http协议元数据中获取Host为域名的情况
        String host = value.getField(FieldConstants.FIELD_HTTP_HOST).toString();
        if (!StringUtils.isEmpty(host) && DomainUtils.isValidDomain(host)) {
            // 处理域名中可能包含的端口
            if (host.contains(":")) {
                host = host.split(":")[0];
            }
            domains.add(DomainUtils.formatDomain(host));
        }
    }

    private static void getSSLDomain(Row value, Set<String> domains) {
        // 从ssl协议元数据中获取SNI为域名的情况
        String sni = value.getField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME).toString();
        if (!StringUtil.isNullOrEmpty(sni) && DomainUtils.isValidDomain(sni)) {
            // 处理域名中可能包含的端口
            if (sni.contains(":")) {
                sni = sni.split(":")[0];
            }
            domains.add(sni);
        }
    }

    private Map<String, Object> getEnrichedDomainInfo(String originalDomain, LocalDateTime processingTime) throws Exception {
        Map<String, Object> cachedInfo = domainInfoCache.get(originalDomain);
        if (cachedInfo != null) {
            cachedInfo.put(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, processingTime.format(DateTimeFormatterUtils.DORIS_DATETIME_FORMATTER));
            domainInfoCache.put(originalDomain, cachedInfo); // Update cache with new timestamp format
            return cachedInfo;
        }

        Map<String, Object> enrichedAttributes = enrichDomainInfoInternal(originalDomain, processingTime);
        if (enrichedAttributes.get(KEY_IS_VALID_DOMAIN) == Boolean.TRUE) {
            domainInfoCache.put(originalDomain, enrichedAttributes);
        }
        return enrichedAttributes;
    }

    private Map<String, Object> enrichDomainInfoInternal(String originalDomain, LocalDateTime processingTime) {
        Map<String, Object> attributes = new HashMap<>(18);
        String formattedProcessingTime = processingTime.format(DateTimeFormatterUtils.DORIS_DATETIME_FORMATTER);
        attributes.put(FieldConstants.KEY_DW_CREATION_TIMESTAMP, formattedProcessingTime);
        attributes.put(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, formattedProcessingTime);
        attributes.put(KEY_ORIGINAL_DOMAIN, originalDomain);
        // 默认设置为非恶意域名，除非在检查后确认为恶意
        attributes.put(KEY_IS_MALICIOUS, false);

        String normalizedDomain = normalizeDomain(originalDomain);
        attributes.put(KEY_NORMALIZED_DOMAIN, normalizedDomain);

        if (StringUtils.isBlank(normalizedDomain) || NetworkUtils.isValidIp(normalizedDomain)) {
            log.debug("Domain '{}' (normalized: '{}') is blank, an IP address, or invalid for PSL processing.", originalDomain, normalizedDomain);
            attributes.put(KEY_IS_VALID_DOMAIN, false);
            attributes.put(KEY_REGISTRABLE_DOMAIN, normalizedDomain);
            return attributes;
        }
        attributes.put(KEY_IS_VALID_DOMAIN, false); // Default to invalid

        PublicSuffixManager psm = PublicSuffixManager.getInstance();
        if (psm.isPslAvailable() && normalizedDomain != null && !NetworkUtils.isValidIp(normalizedDomain)) {
            try {
                String registrableDomain = psm.getRegistrableDomain(normalizedDomain);
                String publicSuffix = psm.getPublicSuffix(normalizedDomain);
                String subdomainPart = null;

                if (registrableDomain != null) {
                    if (normalizedDomain.equals(registrableDomain)) {
                        subdomainPart = "";
                    } else if (normalizedDomain.endsWith("." + registrableDomain)) {
                        int rdLength = registrableDomain.length();
                        int fdLength = normalizedDomain.length();
                        if (fdLength > rdLength + 1) {
                            subdomainPart = normalizedDomain.substring(0, fdLength - (rdLength + 1));
                        }
                    }
                    attributes.put(KEY_REGISTRABLE_DOMAIN, registrableDomain);
                    attributes.put(KEY_PUBLIC_SUFFIX, publicSuffix);
                    attributes.put(KEY_SUBDOMAIN_PART, subdomainPart);
                    if (psm.isDomain(normalizedDomain)) {
                        attributes.put(KEY_IS_VALID_DOMAIN, true);
                    } else {
                        log.debug("Domain '{}' (normalized: '{}') returned registrable parts but isDomain() is false.", originalDomain, normalizedDomain);
                        attributes.put(KEY_IS_VALID_DOMAIN, false);
                    }
                } else {
                    log.debug("Could not determine registrable domain for '{}' (normalized: '{}').", originalDomain, normalizedDomain);
                    attributes.put(KEY_IS_VALID_DOMAIN, false);
                }
            } catch (Exception e) {
                log.warn("Error processing domain '{}' (normalized: '{}') with PublicSuffixManager: {}", originalDomain, normalizedDomain, e.getMessage());
                attributes.put(KEY_IS_VALID_DOMAIN, false);
            }
        } else {
            log.debug("PSL not available or domain '{}' (normalized: '{}') is an IP or invalid for processing.", originalDomain, normalizedDomain);
            attributes.put(KEY_IS_VALID_DOMAIN, false);
        }

        if (attributes.get(KEY_IS_VALID_DOMAIN) == Boolean.TRUE) {
            String domainToCheck = attributes.get(KEY_NORMALIZED_DOMAIN).toString();
            // 检查是否为恶意域名
            if (maliciousDomainManager.isMaliciousDomain(domainToCheck)) {
                attributes.put(KEY_IS_MALICIOUS, true);
                log.debug("检测到恶意域名: {}", domainToCheck);
            }

            // 如果注册域名为空，则使用规范化域名进行查询
            String domainForLookup = attributes.containsKey(KEY_REGISTRABLE_DOMAIN) ?
                    attributes.get(KEY_REGISTRABLE_DOMAIN).toString() : normalizedDomain;

            enrichWithTranco(domainForLookup, attributes);
            enrichWithWhois(domainForLookup, attributes, false);
        }
        return attributes;
    }

    private String normalizeDomain(String domain) {
        return PublicSuffixManager.getInstance().normalizeDomain(domain);
    }

    private void enrichWithTranco(String domainForLookups, Map<String, Object> attributes) {
        if (StringUtils.isBlank(domainForLookups) || trancoRankingManager == null) return;
        try {
            int rank = trancoRankingManager.getDomainRank(domainForLookups);
            if (rank > 0) attributes.put(KEY_DOMAIN_RANK, rank);
        } catch (Exception e) {
            log.warn("Error fetching Tranco rank for '{}': {}", domainForLookups, e.getMessage());
        }
    }

    private void enrichWithWhois(String domainForLookups, Map<String, Object> attributes, boolean isIp) {
        if (StringUtils.isBlank(domainForLookups) || domainWhoisManager == null) return;
        try {
            DomainWhoisManager.DomainWhoisInfo whoisInfo = domainWhoisManager.getDomainWhoisInfo(domainForLookups);
            if (whoisInfo != null) {
                attributes.put(KEY_REGISTRAR_NAME, whoisInfo.getRegistrarName());
                attributes.put(KEY_CREATED_DATE, formatDateForDoris(whoisInfo.getCreatedDate()));
                attributes.put(KEY_UPDATED_DATE, formatDateForDoris(whoisInfo.getUpdatedDate()));
                attributes.put(KEY_EXPIRES_DATE, formatDateForDoris(whoisInfo.getExpiresDate()));
                attributes.put(KEY_REGISTRANT_ORG, whoisInfo.getRegistrantOrganization());
                attributes.put(KEY_REGISTRANT_COUNTRY, whoisInfo.getRegistrantCountry());
            }
        } catch (Exception e) {
            log.warn("Error fetching Whois info for '{}': {}", domainForLookups, e.getMessage());
        }
    }

    /**
     * 将WHOIS数据库中的日期字符串格式化为Doris数据库所需的格式
     * 输入格式示例："04-Aug-2008 22:57:11 UTC"
     * 输出格式："yyyy-MM-dd HH:mm:ss"
     *
     * @param dateStr WHOIS数据库中的日期字符串
     * @return 格式化后的日期时间字符串，如果解析失败则返回null
     */
    private String formatDateForDoris(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }

        try {
            // 解析WHOIS CSV格式（如"04-Aug-2008 22:57:11 UTC"）
            DateTimeFormatter whoisFormatter = DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm:ss z", Locale.ENGLISH);
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateStr, whoisFormatter);
            return zonedDateTime.withZoneSameInstant(ZoneId.systemDefault())
                    .toLocalDateTime()
                    .format(DateTimeFormatterUtils.DORIS_DATETIME_FORMATTER);
        } catch (DateTimeParseException e) {
            log.warn("无法解析WHOIS日期字符串'{}'，期望格式：'dd-MMM-yyyy HH:mm:ss z'（如'04-Aug-2008 22:57:11 UTC'）", dateStr);
            return null;
        }
    }

    private String formatTimestampStringForDoris(Object timestampObject) {
        if (timestampObject == null) return null;
        try {
            return LocalDateTime.parse(timestampObject.toString(), DateTimeFormatter.ISO_LOCAL_DATE_TIME).format(DateTimeFormatterUtils.DORIS_DATETIME_FORMATTER);
        } catch (Exception e) {
            log.warn("Could not parse timestamp string {} to reformat for Doris.", timestampObject);
            return null;
        }
    }

    private Row createDomainDimensionRow(Map<String, Object> enrichedInfo, Integer totalRequest) {
        if (enrichedInfo == null || enrichedInfo.get(KEY_NORMALIZED_DOMAIN) == null) {
            log.warn("Cannot create domain dimension row, essential domain information is missing.");
            return null;
        }
        try {
            // Fields for dim_domain:
            // domain, registrable_domain_name, is_malicious, threat_score, trust_score, remark, 
            // total_bytes, total_queries, unique_client_ips, 
            // first_seen, last_seen, create_time, update_time
            String creationTimeStr = formatTimestampStringForDoris(enrichedInfo.get(FieldConstants.KEY_DW_CREATION_TIMESTAMP));
            String lastUpdatedTimeStr = formatTimestampStringForDoris(enrichedInfo.get(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP));

            // 获取是否为恶意域名的标记，如果没有则默认为false
            boolean isMalicious = Boolean.TRUE.equals(enrichedInfo.get(KEY_IS_MALICIOUS));

            // 根据是否为恶意域名设置威胁分数和信任分数
            Short threatScore = isMalicious ? (short) 100 : null;  // 恶意域名的威胁分数设为100
            Short trustScore = isMalicious ? (short) 0 : null;     // 恶意域名的信任分数设为0

            // 设置备注信息，如果是恶意域名则添加标记
            String remark = isMalicious ? "Known malicious domain" : null;

            return Row.of(
                    enrichedInfo.get(KEY_NORMALIZED_DOMAIN),    // domain (VARCHAR)
                    enrichedInfo.get(KEY_REGISTRABLE_DOMAIN),   // registrable_domain_name (VARCHAR)
                    isMalicious,                               // is_malicious (BOOLEAN)
                    threatScore,                               // threat_score (TINYINT)
                    trustScore,                                // trust_score (TINYINT)
                    remark,                                    // remark (STRING)
                    null,                                      // total_bytes (BIGINT SUM) - Doris handles aggregation
                    totalRequest,                                      // total_queries (BIGINT SUM) - Doris handles aggregation
                    null,                                      // unique_client_ips (HLL HLL_UNION) - Doris handles aggregation
                    creationTimeStr,                           // first_seen (DATETIME MIN)
                    lastUpdatedTimeStr,                        // last_seen (DATETIME MAX)
                    creationTimeStr,                           // create_time (DATETIME MIN)
                    lastUpdatedTimeStr                         // update_time (DATETIME REPLACE)
            );
        } catch (Exception e) {
            log.error("Error creating domain dimension row from enriched info: {}. Error: {}", enrichedInfo, e.getMessage(), e);
            return null;
        }
    }

    private Row createDomainVertexRow(Map<String, Object> enrichedInfo) {
        if (enrichedInfo == null || enrichedInfo.get(KEY_NORMALIZED_DOMAIN) == null) {
            log.warn("Cannot create domain dimension row, essential domain information is missing.");
            return null;
        }
        try {
            boolean isMalicious = Boolean.TRUE.equals(enrichedInfo.get(KEY_IS_MALICIOUS));
            Short threatScore = isMalicious ? (short) 100 : 0;  // 恶意域名的威胁分数设为100
            Short trustScore = isMalicious ? (short) 0 : 100;     // 恶意域名的信任分数设为0
            String remark = isMalicious ? "Known malicious domain" : "";

            return Row.of(
                    enrichedInfo.get(KEY_NORMALIZED_DOMAIN),    // vid
                    enrichedInfo.get(KEY_NORMALIZED_DOMAIN), enrichedInfo.get(KEY_DOMAIN_RANK),
                    isMalicious, threatScore, trustScore,
                    remark, enrichedInfo.get(KEY_REGISTRANT_ORG)
            );
        } catch (Exception e) {
            log.error("Error creating domain vertex row from enriched info: {}. Error: {}", enrichedInfo, e.getMessage(), e);
            return null;
        }
    }

    private Row createRegistrableDomainDimensionRow(Map<String, Object> enrichedInfo) {
        if (enrichedInfo == null || enrichedInfo.get(KEY_REGISTRABLE_DOMAIN) == null) {
            log.warn("Cannot create registrable_domain dimension row, registrable_domain is missing.");
            return null;
        }
        try {
            // Fields for dim_registrable_domain:
            // registrable_domain, domain_rank, registrar_name, created_date, updated_date, expires_date,
            // registrant_org, registrant_country, name_servers, whois_status, whois_raw,
            // threat_score, trust_score, first_seen, last_seen, create_time, update_time
            String creationTimeStr = formatTimestampStringForDoris(enrichedInfo.get(FieldConstants.KEY_DW_CREATION_TIMESTAMP));
            String lastUpdatedTimeStr = formatTimestampStringForDoris(enrichedInfo.get(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP));

            // 获取是否为恶意域名的标记，如果没有则默认为false
            boolean isMalicious = Boolean.TRUE.equals(enrichedInfo.get(KEY_IS_MALICIOUS));

            // 根据是否为恶意域名设置威胁分数和信任分数
            Short threatScore = isMalicious ? (short) 100 : null;  // 恶意域名的威胁分数设为100
            Short trustScore = isMalicious ? (short) 0 : null;     // 恶意域名的信任分数设为0

            // 设置WHOIS状态和备注信息
            String whoisStatus = isMalicious ? "malicious" : null;
            String whoisRaw = isMalicious ? "{\"is_malicious\":true}" : null;

            return Row.of(
                    enrichedInfo.get(KEY_REGISTRABLE_DOMAIN),      // registrable_domain (VARCHAR)
                    enrichedInfo.get(KEY_DOMAIN_RANK),              // domain_rank (INT)
                    enrichedInfo.get(KEY_REGISTRAR_NAME),           // registrar_name (VARCHAR)
                    enrichedInfo.get(KEY_CREATED_DATE),             // created_date (DATETIME)
                    enrichedInfo.get(KEY_UPDATED_DATE),             // updated_date (DATETIME)
                    enrichedInfo.get(KEY_EXPIRES_DATE),             // expires_date (DATETIME)
                    enrichedInfo.get(KEY_REGISTRANT_ORG),           // registrant_org (VARCHAR)
                    enrichedInfo.get(KEY_REGISTRANT_COUNTRY),       // registrant_country (VARCHAR)
                    null,                                           // name_servers (ARRAY<STRING>)
                    whoisStatus,                                    // whois_status (VARCHAR)
                    whoisRaw,                                       // whois_raw (JSON)
                    threatScore,                                    // threat_score (TINYINT)
                    trustScore,                                     // trust_score (TINYINT)
                    creationTimeStr,                                // first_seen (DATETIME MIN)
                    lastUpdatedTimeStr,                             // last_seen (DATETIME MAX)
                    creationTimeStr,                                // create_time (DATETIME MIN)
                    lastUpdatedTimeStr                              // update_time (DATETIME REPLACE)
            );
        } catch (Exception e) {
            log.error("Error creating registrable_domain dimension row from enriched info: {}. Error: {}", enrichedInfo, e.getMessage(), e);
            return null;
        }
    }

    private Row createRegistrableDomainVertexRow(Map<String, Object> enrichedInfo) {
        if (enrichedInfo == null || enrichedInfo.get(KEY_REGISTRABLE_DOMAIN) == null) {
            log.warn("Cannot create registrable_domain dimension row, registrable_domain is missing.");
            return null;
        }
        try {
            boolean isMalicious = Boolean.TRUE.equals(enrichedInfo.get(KEY_IS_MALICIOUS));
            Short threatScore = isMalicious ? (short) 100 : null;  // 恶意域名的威胁分数设为100
            Short trustScore = isMalicious ? (short) 0 : null;     // 恶意域名的信任分数设为0

            return Row.of(
                    enrichedInfo.get(KEY_REGISTRABLE_DOMAIN), // vid
                    enrichedInfo.get(KEY_REGISTRABLE_DOMAIN),
                    threatScore, trustScore
            );
        } catch (Exception e) {
            log.error("Error creating registrable_domain dimension row from enriched info: {}. Error: {}", enrichedInfo, e.getMessage(), e);
            return null;
        }
    }
}
