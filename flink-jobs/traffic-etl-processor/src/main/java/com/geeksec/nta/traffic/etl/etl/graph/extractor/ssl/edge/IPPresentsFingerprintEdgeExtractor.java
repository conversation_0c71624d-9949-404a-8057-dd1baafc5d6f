package com.geeksec.nta.traffic.etl.etl.graph.extractor.ssl.edge;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.network.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
@Slf4j
public class IPPresentsFingerprintEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.IP_PRESENTS_FINGERPRINT_TAG;
    }

    /**
     * IP呈现特定TLS指纹特征，role可为client或server (IP -> CERT)
     * client 源IP -> 客户端TLS指纹， server 目的IP -> 服务端TLS指纹
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        List<Row> edges = new ArrayList<>();
        String sIP = value.getField(FieldConstants.FIELD_SRC_IP).toString();
        String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
        String sSSLFinger = value.getField(FieldConstants.FIELD_SSL_S_FINGER).toString();
        String dSSLFinger = value.getField(FieldConstants.FIELD_SSL_C_FINGER).toString();

        if (StringUtils.isNotEmpty(sSSLFinger) && !"0".equals(sSSLFinger)) {
            edges.add(Row.of(sIP, sSSLFinger,
                    0, // rank暂定0
                    "client"));
        }

        if (StringUtils.isNotEmpty(dSSLFinger) && !"0".equals(dSSLFinger)) {
            edges.add(Row.of(dIP, dSSLFinger,
                    0, // rank暂定0
                    "server"));
        }

        return edges;
    }
}
