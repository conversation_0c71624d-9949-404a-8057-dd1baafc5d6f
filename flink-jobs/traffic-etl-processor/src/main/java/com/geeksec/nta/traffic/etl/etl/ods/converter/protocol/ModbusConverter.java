package com.geeksec.nta.traffic.etl.etl.ods.converter.protocol;

import com.geeksec.flink.common.utils.time.TimeUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.traffic.etl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Modbus协议转换器
 * 将Modbus协议的protobuf消息转换为Doris ods_modbus_protocol_metadata表格式
 *
 * <AUTHOR>
 */
@Slf4j
public class ModbusConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasModbus()) {
            log.warn("JKNmsg does not contain Modbus message");
            return null;
        }
        Row row = Row.withNames();

        ZMPNMsg.modbus_msg modbusMsg = msg.getModbus();
        if (modbusMsg.hasCommMsg()){
            // 设置通用字段
            enrichComMsg(row, modbusMsg.getCommMsg());
        }

        // 设置Modbus特定字段（需要根据实际protobuf定义实现）
        // TODO: 根据实际的Modbus protobuf消息结构实现字段映射
        row.setField(FieldConstants.TRANS_ID,modbusMsg.getTransId());
        row.setField(FieldConstants.PROTOCOL_ID,modbusMsg.getProtocolId());
        row.setField(FieldConstants.SLAVE_ID,modbusMsg.getSlaveId());
        row.setField(FieldConstants.FUNC_CODE,modbusMsg.getFuncCode());
        row.setField(FieldConstants.PACKET_C2S,modbusMsg.getPacketC2S());

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.MODBUS_STREAM;
    }
}
