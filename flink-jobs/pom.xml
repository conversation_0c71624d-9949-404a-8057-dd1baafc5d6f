<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>nta-platform</artifactId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>flink-jobs</artifactId>
    <packaging>pom</packaging>
    <name>flink-jobs</name>
    <description>Flink Jobs for Network Traffic Analysis</description>

    <properties>
        <!-- Flink 相关版本 -->
        <flink.version>1.20.1</flink.version>
        <flink.connector.kafka.version>3.3.0-1.20</flink.connector.kafka.version>
        <flink.connector.jdbc.version>3.3.0-1.20</flink.connector.jdbc.version>
        <flink.connector.doris.version>25.1.0</flink.connector.doris.version>
        <flink-cdc.version>3.2.1</flink-cdc.version>
        <nebula-flink-connector.version>3.8.0</nebula-flink-connector.version>
        <hadoop.aws.version>3.4.1</hadoop.aws.version>

        <!-- JSON 序列化相关 -->
        <jackson.version>2.18.2</jackson.version>
        <fastjson2.version>2.0.53</fastjson2.version>
        <protobuf.version>4.29.2</protobuf.version>
        <kryo.version>5.6.2</kryo.version>

        <!-- 日志相关 -->
        <slf4j.version>2.0.16</slf4j.version>
        <logback.version>1.5.12</logback.version>
        <logstash.logback.encoder.version>8.0</logstash.logback.encoder.version>

        <!-- 工具类库 -->
        <commons.lang3.version>3.17.0</commons.lang3.version>
        <commons.io.version>2.18.0</commons.io.version>
        <commons.codec.version>1.17.1</commons.codec.version>
        <commons.validator.version>1.9.0</commons.validator.version>
        <commons.collections4.version>4.5.0-M2</commons.collections4.version>
        <commons.pool.version>1.6</commons.pool.version>
        <commons.text.version>1.12.0</commons.text.version>
        <guava.version>33.3.1-jre</guava.version>

        <!-- 数据库和缓存 -->
        <mysql.connector.version>8.0.40</mysql.connector.version>
        <postgresql.version>42.7.4</postgresql.version>
        <jedis.version>5.2.0</jedis.version>
        <elasticsearch.version>7.17.26</elasticsearch.version>
        <caffeine.version>3.1.8</caffeine.version>

        <!-- 消息队列 -->
        <kafka.clients.version>3.9.0</kafka.clients.version>

        <!-- 存储相关 -->
        <minio.version>8.5.14</minio.version>

        <!-- 图数据库 -->
        <nebula.client.version>3.8.0</nebula.client.version>

        <!-- HTTP 客户端 -->
        <okhttp.version>4.12.0</okhttp.version>

        <!-- 加密和安全 -->
        <bouncycastle.version>1.79</bouncycastle.version>

        <!-- 证书和网络分析工具 -->
        <certificate.transparency.version>0.1.0</certificate.transparency.version>
        <jchardet.version>1.1</jchardet.version>
        <geoip2.version>4.2.1</geoip2.version>
        <icu4j.version>76.1</icu4j.version>
        <public.suffix.list.version>2.2.0</public.suffix.list.version>
        <ua.parse.version>7.28.0</ua.parse.version>

        <!-- NLP 工具 -->
        <opennlp.version>2.5.0</opennlp.version>

        <!-- Thrift 相关 -->
        <chill.thrift.version>0.10.0</chill.thrift.version>
        <thrift.version>0.21.0</thrift.version>

        <!-- 邮件支持 -->
        <spring.version>6.2.1</spring.version>
        <jakarta.mail.version>2.1.3</jakarta.mail.version>
        <angus.mail.version>2.0.3</angus.mail.version>

        <!-- 开发工具 -->
        <lombok.version>1.18.36</lombok.version>

        <!-- 测试相关 -->
        <junit.version>5.11.3</junit.version>
        <mockito.version>5.14.2</mockito.version>
        <assertj.version>3.26.3</assertj.version>

        <!-- 构建插件版本 -->
        <maven.shade.plugin.version>3.5.0</maven.shade.plugin.version>
        <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
        <maven.jar.plugin.version>3.3.0</maven.jar.plugin.version>
        <surefire.plugin.version>3.0.0-M9</surefire.plugin.version>
        <docker.maven.plugin.version>0.44.0</docker.maven.plugin.version>
        <os.maven.plugin.version>1.7.1</os.maven.plugin.version>
        <protobuf.maven.plugin.version>0.6.1</protobuf.maven.plugin.version>
        <build.helper.maven.plugin.version>3.4.0</build.helper.maven.plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Common Dependencies -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash.logback.encoder.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            
            <!-- Apache Commons Validator -->
            <dependency>
                <groupId>commons-validator</groupId>
                <artifactId>commons-validator</artifactId>
                <version>${commons.validator.version}</version>
            </dependency>
            
            <!-- Apache Commons Collections4 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons.collections4.version}</version>
            </dependency>
            
            <!-- Apache Commons Codec -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons.codec.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.clients.version}</version>
            </dependency>

            <!-- Jackson JSON Processing -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- FastJSON2 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- 数据库驱动 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>

            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!-- Redis Client -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <!-- Apache Commons Pool -->
            <dependency>
                <groupId>commons-pool</groupId>
                <artifactId>commons-pool</artifactId>
                <version>${commons.pool.version}</version>
            </dependency>

            <!-- Apache Commons Text -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons.text.version}</version>
            </dependency>

            <!-- BouncyCastle -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <!-- Elasticsearch -->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!-- MinIO -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- HTTP 客户端 - 统一使用 OkHttp (最主流的现代HTTP客户端) -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <!-- Caffeine Cache -->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <!-- Kryo Serialization -->
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>${kryo.version}</version>
            </dependency>

            <!-- ICU4J -->
            <dependency>
                <groupId>com.ibm.icu</groupId>
                <artifactId>icu4j</artifactId>
                <version>${icu4j.version}</version>
            </dependency>
            <!-- Flink Core Dependencies (provided scope as they are included in Flink distribution) -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-core</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-runtime</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-java</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-clients</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-api-java-bridge</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-runtime</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-statebackend-rocksdb</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Flink Connectors -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-base</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-kafka</artifactId>
                <version>${flink.connector.kafka.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-json</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-postgres-cdc</artifactId>
                <version>${flink-cdc.version}</version>
            </dependency>

            <!-- Nebula Flink Connector -->
            <dependency>
                <groupId>com.vesoft</groupId>
                <artifactId>nebula-flink-connector</artifactId>
                <version>${nebula-flink-connector.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-jdbc</artifactId>
                <version>${flink.connector.jdbc.version}</version>
            </dependency>

            <!-- Doris Flink Connector -->
            <dependency>
                <groupId>org.apache.doris</groupId>
                <artifactId>flink-doris-connector-1.20</artifactId>
                <version>${flink.connector.doris.version}</version>
            </dependency>

            <!-- Flink CDC Connectors -->
            <dependency>
                <groupId>com.ververica</groupId>
                <artifactId>flink-connector-postgres-cdc</artifactId>
                <version>${flink-cdc.version}</version>
            </dependency>

            <!-- GeoIP2 -->
            <dependency>
                <groupId>com.maxmind.geoip2</groupId>
                <artifactId>geoip2</artifactId>
                <version>${geoip2.version}</version>
            </dependency>

            <!-- Flink Test Dependencies -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-test-utils</artifactId>
                <version>${flink.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-runtime</artifactId>
                <version>${flink.version}</version>
                <type>test-jar</type>
                <classifier>tests</classifier>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java</artifactId>
                <version>${flink.version}</version>
                <classifier>tests</classifier>
                <scope>test</scope>
            </dependency>

            <!-- S3 文件系统依赖 -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-s3-fs-hadoop</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <!-- Hadoop AWS 依赖，用于S3兼容存储 -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-aws</artifactId>
                <version>${hadoop.aws.version}</version>
            </dependency>

            <!-- 测试相关依赖 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
                <scope>test</scope>
            </dependency>




            <!-- 网络分析工具 -->
            <dependency>
                <groupId>org.certificate-transparency</groupId>
                <artifactId>ctlog</artifactId>
                <version>${certificate.transparency.version}</version>
            </dependency>

            <dependency>
                <groupId>net.sourceforge.jchardet</groupId>
                <artifactId>jchardet</artifactId>
                <version>${jchardet.version}</version>
            </dependency>

            <dependency>
                <groupId>de.malkusch.whois-server-list</groupId>
                <artifactId>public-suffix-list</artifactId>
                <version>${public.suffix.list.version}</version>
            </dependency>

            <dependency>
                <groupId>nl.basjes.parse.useragent</groupId>
                <artifactId>yauaa</artifactId>
                <version>${ua.parse.version}</version>
            </dependency>

            <!-- NLP 工具 -->
            <dependency>
                <groupId>org.apache.opennlp</groupId>
                <artifactId>opennlp-tools</artifactId>
                <version>${opennlp.version}</version>
            </dependency>

            <!-- Thrift 相关 -->
            <dependency>
                <groupId>com.twitter</groupId>
                <artifactId>chill-thrift</artifactId>
                <version>${chill.thrift.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.esotericsoftware.kryo</groupId>
                        <artifactId>kryo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>${thrift.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>



            <!-- Flink 连接器 -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-files</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <!-- Nebula Graph Client -->
            <dependency>
                <groupId>com.vesoft</groupId>
                <artifactId>client</artifactId>
                <version>${nebula.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vesoft</groupId>
                <artifactId>nebula-java</artifactId>
                <version>${nebula.client.version}</version>
            </dependency>

            <!-- 邮件支持 -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.mail</groupId>
                <artifactId>jakarta.mail-api</artifactId>
                <version>${jakarta.mail.version}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse.angus</groupId>
                <artifactId>jakarta.mail</artifactId>
                <version>${angus.mail.version}</version>
            </dependency>



            <!-- 测试依赖 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>mockwebserver</artifactId>
                <version>${okhttp.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.geeksec</groupId>
                <artifactId>flink-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.geeksec</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <modules>
        <module>common</module>
        <module>session-threat-detector</module>
        <module>certificate-analyzer</module>
        <module>traffic-etl-processor</module>
        <module>alarm-processor</module>
        <module>alarm-notification</module>
        <module>alarm-cdc-sync</module>
        <module>cert-labels-cdc-sync</module>
        <module>session-labels-cdc-sync</module>
    </modules>

    <!-- 公共依赖，适用于大多数子模块 -->
    <!-- 依赖管理，子模块按需引入 -->

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${surefire.plugin.version}</version>
                    <configuration>
                        <useSystemClassLoader>false</useSystemClassLoader>
                        <includes>
                            <include>**/*Test.java</include>
                            <include>**/*Test*.java</include>
                        </includes>
                    </configuration>
                    <dependencies>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.maven.plugin.version}</version>
                    <configuration>
                        <images>
                            <image>
                                <name>
                                    ${docker.registry}/${docker.image.prefix}/${project.artifactId}:${project.version}</name>
                                <build>
                                    <args>
                                        <FLINK_VERSION>${flink.version}</FLINK_VERSION>
                                    </args>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven.jar.plugin.version}</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                                <classpathPrefix>lib/</classpathPrefix>
                                <mainClass>${main.class}</mainClass>
                                <useUniqueVersions>false</useUniqueVersions>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven.shade.plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <configuration>
                                <createDependencyReducedPom>false</createDependencyReducedPom>
                                <transformers>
                                    <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                        <mainClass>${main.class}</mainClass>
                                    </transformer>
                                    <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                                </transformers>
                                <filters>
                                    <filter>
                                        <artifact>*:*</artifact>
                                        <excludes>
                                            <exclude>META-INF/*.SF</exclude>
                                            <exclude>META-INF/*.DSA</exclude>
                                            <exclude>META-INF/*.RSA</exclude>
                                        </excludes>
                                    </filter>
                                </filters>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler.plugin.version}</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>



                <plugin>
                    <groupId>org.xolstice.maven.plugins</groupId>
                    <artifactId>protobuf-maven-plugin</artifactId>
                    <version>${protobuf.maven.plugin.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build.helper.maven.plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>