package com.geeksec.session.threat.detector.config;

import com.geeksec.flink.common.constants.ConfigConstants;
import com.geeksec.flink.common.config.ConfigurationManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;

/**
 * 威胁检测器配置类
 * 提供威胁检测器专用的配置项和配置获取方法
 * 
 * <AUTHOR>
 */
@Slf4j
public final class ThreatDetectorConfig {
    
    private ThreatDetectorConfig() {
        // 防止实例化
    }
    
    // ==================== 威胁检测器专用配置常量 ====================
    
    /**
     * 威胁检测器专用Kafka主题配置
     */
    public static final class Topics {
        /** 会话元数据主题 */
        public static final String SESSION_METADATA = "kafka.topic.session.metadata";
        /** 协议元数据主题 */
        public static final String PROTOCOL_METADATA = "kafka.topic.protocol.metadata";
        /** 威胁检测配置主题 */
        public static final String THREAT_CONFIG = "kafka.topic.threat.config";
        /** 威胁告警主题 */
        public static final String THREAT_ALARMS = "kafka.topic.threat.alarms";
        /** 威胁通知主题 */
        public static final String THREAT_NOTIFICATIONS = "kafka.topic.threat.notifications";
        
        private Topics() {}
    }
    
    /**
     * 威胁检测器配置
     */
    public static final class Detection {
        /** 检测器并行度 */
        public static final String DETECTOR_PARALLELISM = "threat.detector.parallelism";
        /** 检测器缓冲区大小 */
        public static final String DETECTOR_BUFFER_SIZE = "threat.detector.buffer.size";
        /** 检测器超时时间（毫秒） */
        public static final String DETECTOR_TIMEOUT_MS = "threat.detector.timeout.ms";
        /** 是否启用调试模式 */
        public static final String DEBUG_ENABLED = "threat.detector.debug.enabled";

        private Detection() {}
    }

    /**
     * 检测器开关配置
     */
    public static final class DetectorSwitch {
        /** 检测器开关配置主题 */
        public static final String SWITCH_CONFIG_TOPIC = "threat.detector.switch.topic";
        /** 检测器开关配置文件路径 */
        public static final String SWITCH_CONFIG_FILE = "threat.detector.switch.config.file";
        /** 是否启用动态开关控制 */
        public static final String DYNAMIC_SWITCH_ENABLED = "threat.detector.switch.dynamic.enabled";
        /** 开关状态检查间隔（秒） */
        public static final String SWITCH_CHECK_INTERVAL_SECONDS = "threat.detector.switch.check.interval.seconds";

        private DetectorSwitch() {}
    }
    
    /**
     * 输出配置
     */
    public static final class Output {
        /** 会话标签输出并行度 */
        public static final String SESSION_LABEL_PARALLELISM = "threat.output.session.label.parallelism";
        /** 资产标签输出并行度 */
        public static final String ASSET_LABEL_PARALLELISM = "threat.output.asset.label.parallelism";
        /** 告警输出并行度 */
        public static final String ALARM_PARALLELISM = "threat.output.alarm.parallelism";
        /** 通知输出并行度 */
        public static final String NOTIFICATION_PARALLELISM = "threat.output.notification.parallelism";
        
        private Output() {}
    }
    
    // ==================== 配置获取方法 ====================
    
    /**
     * 获取威胁检测器配置
     * 
     * @return 配置参数工具
     */
    public static ParameterTool getConfig() {
        return ConfigurationManager.getConfig();
    }
    
    /**
     * 获取Kafka Bootstrap Servers
     * 
     * @return Kafka服务器地址
     */
    public static String getKafkaBootstrapServers() {
        return getConfig().get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS, "localhost:9092");
    }
    
    /**
     * 获取Kafka Group ID
     * 
     * @return Kafka消费者组ID
     */
    public static String getKafkaGroupId() {
        return getConfig().get(ConfigConstants.KAFKA_GROUP_ID, "threat-detector-group");
    }
    
    /**
     * 获取会话元数据主题
     * 
     * @return 会话元数据主题名称
     */
    public static String getSessionMetadataTopic() {
        return getConfig().get(ConfigConstants.KAFKA_TOPIC_CONNECT, "session-metadata");
    }
    
    /**
     * 获取HTTP协议主题
     * 
     * @return HTTP协议主题名称
     */
    public static String getHttpTopic() {
        return getConfig().get(ConfigConstants.KAFKA_TOPIC_HTTP, "http-metadata");
    }
    
    /**
     * 获取DNS协议主题
     * 
     * @return DNS协议主题名称
     */
    public static String getDnsTopic() {
        return getConfig().get(ConfigConstants.KAFKA_TOPIC_DNS, "dns-metadata");
    }
    
    /**
     * 获取SSL协议主题
     * 
     * @return SSL协议主题名称
     */
    public static String getSslTopic() {
        return getConfig().get(ConfigConstants.KAFKA_TOPIC_SSL, "ssl-metadata");
    }
    
    /**
     * 获取威胁通知主题
     * 
     * @return 威胁通知主题名称
     */
    public static String getThreatNotificationTopic() {
        return getConfig().get(Topics.THREAT_NOTIFICATIONS, "threat-notifications");
    }
    
    /**
     * 获取检测器并行度
     * 
     * @return 检测器并行度
     */
    public static int getDetectorParallelism() {
        return getConfig().getInt(Detection.DETECTOR_PARALLELISM, 4);
    }
    
    /**
     * 获取检测器缓冲区大小
     * 
     * @return 缓冲区大小
     */
    public static int getDetectorBufferSize() {
        return getConfig().getInt(Detection.DETECTOR_BUFFER_SIZE, 1000);
    }
    
    /**
     * 获取检测器超时时间
     * 
     * @return 超时时间（毫秒）
     */
    public static long getDetectorTimeoutMs() {
        return getConfig().getLong(Detection.DETECTOR_TIMEOUT_MS, 30000L);
    }
    
    /**
     * 是否启用调试模式
     * 
     * @return 是否启用调试模式
     */
    public static boolean isDebugEnabled() {
        return getConfig().getBoolean(Detection.DEBUG_ENABLED, false);
    }
    
    /**
     * 获取Doris JDBC URL
     *
     * @return Doris JDBC URL
     */
    public static String getDorisJdbcUrl() {
        return getConfig().get("doris.jdbc.url",
                "*************************************************************************");
    }

    /**
     * 获取Doris用户名
     *
     * @return Doris用户名
     */
    public static String getDorisUsername() {
        return getConfig().get("doris.jdbc.username", "root");
    }

    /**
     * 获取Doris密码
     *
     * @return Doris密码
     */
    public static String getDorisPassword() {
        return getConfig().get("doris.jdbc.password", "");
    }
    
    /**
     * 获取Nebula图数据库地址
     *
     * @return Nebula图数据库地址
     */
    public static String getNebulaGraphAddr() {
        return getConfig().get("nebula.graph.addr", "127.0.0.1:9669");
    }

    /**
     * 获取Nebula空间名称
     *
     * @return Nebula空间名称
     */
    public static String getNebulaSpaceName() {
        return getConfig().get("nebula.space.name", "nta");
    }

    // ==================== 检测器开关配置获取方法 ====================

    /**
     * 获取检测器开关配置主题
     *
     * @return 检测器开关配置主题名称
     */
    public static String getDetectorSwitchTopic() {
        return getConfig().get(DetectorSwitch.SWITCH_CONFIG_TOPIC, "detector-switch-config");
    }

    /**
     * 获取检测器开关配置文件路径
     *
     * @return 配置文件路径
     */
    public static String getDetectorSwitchConfigFile() {
        return getConfig().get(DetectorSwitch.SWITCH_CONFIG_FILE, "detector_switch_config.csv");
    }

    /**
     * 是否启用动态开关控制
     *
     * @return 是否启用动态开关控制
     */
    public static boolean isDynamicSwitchEnabled() {
        return getConfig().getBoolean(DetectorSwitch.DYNAMIC_SWITCH_ENABLED, true);
    }

    /**
     * 获取开关状态检查间隔
     *
     * @return 检查间隔（秒）
     */
    public static int getSwitchCheckIntervalSeconds() {
        return getConfig().getInt(DetectorSwitch.SWITCH_CHECK_INTERVAL_SECONDS, 60);
    }
    
    /**
     * 打印配置信息（用于调试）
     */
    public static void printConfig() {
        if (isDebugEnabled()) {
            log.info("威胁检测器配置信息:");
            log.info("  Kafka Bootstrap Servers: {}", getKafkaBootstrapServers());
            log.info("  Kafka Group ID: {}", getKafkaGroupId());
            log.info("  Session Topic: {}", getSessionMetadataTopic());
            log.info("  HTTP Topic: {}", getHttpTopic());
            log.info("  DNS Topic: {}", getDnsTopic());
            log.info("  SSL Topic: {}", getSslTopic());
            log.info("  Notification Topic: {}", getThreatNotificationTopic());
            log.info("  Detector Parallelism: {}", getDetectorParallelism());
            log.info("  Debug Enabled: {}", isDebugEnabled());
            log.info("检测器开关配置:");
            log.info("  Switch Topic: {}", getDetectorSwitchTopic());
            log.info("  Switch Config File: {}", getDetectorSwitchConfigFile());
            log.info("  Dynamic Switch Enabled: {}", isDynamicSwitchEnabled());
            log.info("  Switch Check Interval: {}s", getSwitchCheckIntervalSeconds());
        }
    }
}
