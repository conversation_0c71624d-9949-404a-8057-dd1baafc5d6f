package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 挖矿病毒告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class MiningVirusProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.MINING_VIRUS;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        // 挖矿地址通讯检测
        if (alarm.getDstIp() != null) {
            reasons.add(createDetectionReason(
                    "挖矿地址通讯",
                    "检测到与已知挖矿池地址的通信",
                    "正常业务通信",
                    alarm.getDstIp(),
                    "合法服务器地址",
                    8
            ));
        }
        
        // 访问挖矿域名检测
        if (alarm.getExtendedProperties() != null) {
            Object domain = alarm.getExtendedProperties().get("mining_domain");
            if (domain != null && !domain.toString().isEmpty()) {
                reasons.add(createDetectionReason(
                        "访问挖矿域名",
                        "检测到访问已知挖矿池域名",
                        "正常域名访问",
                        domain.toString(),
                        "合法业务域名",
                        7
                ));
            }
        }
        
        // 使用矿池通讯指纹检测
        if (alarm.getExtendedProperties() != null) {
            Object fingerprint = alarm.getExtendedProperties().get("mining_fingerprint");
            if (fingerprint != null && !fingerprint.toString().isEmpty()) {
                reasons.add(createDetectionReason(
                        "使用矿池通讯指纹",
                        "检测到挖矿协议特征指纹",
                        "正常协议指纹",
                        fingerprint.toString(),
                        "标准协议指纹",
                        9
                ));
                
                // LSTM模型预测结果
                reasons.add(createDetectionReason(
                        "LSTM模型预测结果",
                        "机器学习模型识别为挖矿行为",
                        "正常行为模式",
                        "0.9",
                        "0.1以下",
                        8
                ));
            }
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        "立即隔离受感染主机，阻断网络连接",
                        "检查主机CPU、GPU使用率是否异常",
                        "扫描主机是否存在挖矿程序进程"
                ))
                .investigationSteps(Arrays.asList(
                        "分析安全告警，确认告警正确性",
                        "对日志进行分析，确认所有中招主机",
                        "分析挖矿程序的传播路径和感染方式",
                        "检查是否存在其他恶意软件"
                ))
                .preventionMeasures(Arrays.asList(
                        "更新防病毒软件病毒库",
                        "加强终端安全防护",
                        "定期进行安全扫描",
                        "加强员工安全意识培训"
                ))
                .recoverySteps(Arrays.asList(
                        "彻底清除挖矿程序和相关恶意文件",
                        "恢复系统正常性能和资源使用",
                        "重新安装或修复被破坏的系统组件",
                        "更新系统补丁和安全软件",
                        "恢复正常的网络连接和服务",
                        "监控系统确保挖矿程序完全清除"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        return "使用挖矿通讯协议进行挖矿通讯，使用计算机或者移动设备内的资源挖掘加密货币。";
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过检测网络流量中的挖矿协议特征、已知挖矿池地址和域名，结合机器学习模型进行综合判断。";
    }
    
    @Override
    public List<Map<String, String>> getVictimInfo(Alarm alarm) {
        List<Map<String, String>> victims = new ArrayList<>();
        
        // 挖矿病毒中，源IP通常是受害者
        if (alarm.getSrcIp() != null) {
            Map<String, String> victim = new HashMap<>();
            victim.put("ip", alarm.getSrcIp());
            victim.put("role", "受感染主机");
            victims.add(victim);
        }
        
        return victims;
    }
    
    @Override
    public List<Map<String, String>> getAttackerInfo(Alarm alarm) {
        List<Map<String, String>> attackers = new ArrayList<>();
        
        // 挖矿池地址作为攻击者
        if (alarm.getDstIp() != null) {
            Map<String, String> attacker = new HashMap<>();
            attacker.put("ip", alarm.getDstIp());
            attacker.put("role", "挖矿池服务器");
            attackers.add(attacker);
        }
        
        return attackers;
    }
    
    @Override
    public List<Map<String, Object>> getAttackFamily(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Map<String, Object>> attackFamily = new ArrayList<>();
        
        try {
            // 查询挖矿相关威胁情报
            Map<String, Object> miningThreat = knowledgeBaseClient.getThreatIntelligenceByName("挖矿病毒");
            if (miningThreat != null) {
                attackFamily.add(miningThreat);
            }
            
            // 如果有具体的挖矿家族信息
            if (alarm.getExtendedProperties() != null) {
                Object familyName = alarm.getExtendedProperties().get("mining_family");
                if (familyName != null) {
                    Map<String, Object> specificFamily = knowledgeBaseClient.getThreatIntelligenceByName(familyName.toString());
                    if (specificFamily != null) {
                        attackFamily.add(specificFamily);
                    }
                }
            }
            
        } catch (Exception e) {
            log.warn("获取挖矿攻击家族信息失败: {}", e.getMessage());
        }
        
        return attackFamily;
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99006"; // 挖矿病毒模型ID
    }
    
    @Override
    public boolean requiresSpecialHandling(Alarm alarm) {
        return true; // 挖矿病毒需要特殊处理
    }
}
