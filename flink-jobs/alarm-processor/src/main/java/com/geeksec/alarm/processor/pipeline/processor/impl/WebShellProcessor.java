package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * WebShell攻击告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class WebShellProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.WEBSHELL_ATTACK;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String webshellType = getWebShellType(alarm);
        String webshellEncrypted = getWebShellEncrypted(alarm);
        String webshellEncryptionType = getWebShellEncryptionType(alarm);
        String webshellCodingType = getWebShellCodingType(alarm);
        String webshellOneWay = getWebShellOneWay(alarm);
        
        String description = String.format("%s工具为%s%s%s%s", 
                webshellType, webshellEncrypted, webshellEncryptionType, webshellCodingType, webshellOneWay);
        
        // 根据WebShell类型生成特定的检测原因
        switch (webshellType) {
            case "中国菜刀":
                reasons.add(createDetectionReason(
                        "发现以下属于中国菜刀攻击工具的特征",
                        "请求包中的请求体解码后根据&字符分割，第一段内容中包含cmd=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B特征字段，" +
                        "第二段内容包含posix_getpwuid、posix_geteuid、system特定内容。 " + description,
                        "正常HTTP请求",
                        "中国菜刀特征字段",
                        "标准Web请求",
                        9
                ));
                break;
                
            case "蚁剑":
                reasons.add(createDetectionReason(
                        "发现以下属于蚁剑攻击工具的特征",
                        "请求包中的请求体负载内容包含function%20HexAsciiConvert(hex%3AString)特定内容。 " + description,
                        "正常HTTP请求",
                        "蚁剑特征字段",
                        "标准Web请求",
                        9
                ));
                break;
                
            case "WeBacoo":
                reasons.add(createDetectionReason(
                        "发现以下属于WeBacoo攻击工具的特征",
                        "请求包中的请求体负载内容包含WeBacoo特定标识。 " + description,
                        "正常HTTP请求",
                        "WeBacoo特征字段",
                        "标准Web请求",
                        8
                ));
                break;
                
            case "冰蝎3":
            case "冰蝎4":
                reasons.add(createDetectionReason(
                        String.format("发现以下属于%s攻击工具的特征", webshellType),
                        String.format("请求包中检测到%s的特征标识。 %s", webshellType, description),
                        "正常HTTP请求",
                        webshellType + "特征字段",
                        "标准Web请求",
                        9
                ));
                break;
                
            case "哥斯拉":
                reasons.add(createDetectionReason(
                        "发现以下属于哥斯拉攻击工具的特征",
                        "根据请求包请求头和请求体内容检测出符合哥斯拉攻击工具的特征。 " + description,
                        "正常HTTP请求",
                        "哥斯拉特征字段",
                        "标准Web请求",
                        9
                ));
                break;
                
            case "SharPyShell":
                reasons.add(createDetectionReason(
                        "发现以下属于SharPyShell攻击工具的特征",
                        "请求包中的请求头中Content-Type字段为固定内容multipart/form-data; boundary=8d8ef8552fca8671052f3044faf663a0、" +
                        "Content-Disposition字段为固定内容form-data; name=\"data\"、" +
                        "响应头中Cache-Control字段为固定内容private。 " + description,
                        "正常HTTP请求",
                        "SharPyShell特征字段",
                        "标准Web请求",
                        8
                ));
                break;
                
            default:
                reasons.add(createDetectionReason(
                        "发现WebShell攻击特征",
                        "检测到未知类型的WebShell攻击工具特征。 " + description,
                        "正常HTTP请求",
                        "WebShell特征",
                        "标准Web请求",
                        7
                ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String webshellType = getWebShellType(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        String.format("立即检查服务器是否被%s攻击工具攻击", webshellType),
                        "隔离受影响的Web服务器",
                        "阻断可疑的网络连接",
                        "备份相关日志和证据"
                ))
                .investigationSteps(Arrays.asList(
                        "监控系统日志审查是否出现异常行为",
                        "特别注意远程登录、文件上传、进程管理等关键操作的日志记录",
                        "检查Web应用目录是否存在可疑文件",
                        "分析攻击者的访问路径和权限获取方式"
                ))
                .preventionMeasures(Arrays.asList(
                        "更新Web应用和服务器补丁",
                        "加强Web应用安全防护",
                        "实施文件上传限制和检查",
                        "配置Web应用防火墙(WAF)"
                ))
                .recoverySteps(Arrays.asList(
                        String.format("彻底清除%s WebShell文件和相关恶意代码", webshellType),
                        "修复被WebShell利用的Web应用漏洞",
                        "重新部署干净的Web应用代码",
                        "恢复被篡改的系统文件和配置",
                        "重新设置Web服务器和应用的安全配置",
                        "监控Web应用确保WebShell完全清除"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String webshellType = getWebShellType(alarm);
        return String.format("攻击者通过Web应用漏洞上传%s等WebShell工具，获取服务器控制权限，进行远程命令执行。", webshellType);
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        String webshellType = getWebShellType(alarm);
        switch (webshellType) {
            case "中国菜刀":
                return "根据请求包请求体内容检测出符合中国菜刀攻击工具的特征。";
            case "蚁剑":
                return "根据请求包请求体内容检测出符合蚁剑攻击工具的特征。";
            case "冰蝎3":
            case "冰蝎4":
                return String.format("根据请求包内容检测出符合%s攻击工具的特征。", webshellType);
            case "哥斯拉":
                return "根据请求包请求头和请求体内容检测出符合哥斯拉攻击工具的特征。";
            case "SharPyShell":
                return "根据请求包请求头和响应头内容检测出符合SharPyShell攻击工具的特征。";
            default:
                return "根据HTTP请求特征检测出WebShell攻击工具的使用。";
        }
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99173"; // WebShell攻击模型ID
    }
    
    @Override
    public boolean requiresSpecialHandling(Alarm alarm) {
        return true; // WebShell攻击需要特殊处理
    }
    
    /**
     * 获取WebShell类型
     */
    private String getWebShellType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object webshellType = alarm.getExtendedProperties().get("webshell_type");
            if (webshellType != null) {
                return webshellType.toString();
            }
        }
        return "未知WebShell";
    }
    
    /**
     * 获取WebShell加密状态
     */
    private String getWebShellEncrypted(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object encrypted = alarm.getExtendedProperties().get("webshell_encrypted");
            if (encrypted != null) {
                return encrypted.toString();
            }
        }
        return "";
    }
    
    /**
     * 获取WebShell加密类型
     */
    private String getWebShellEncryptionType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object encryptionType = alarm.getExtendedProperties().get("webshell_encryption_type");
            if (encryptionType != null) {
                return encryptionType.toString();
            }
        }
        return "";
    }
    
    /**
     * 获取WebShell编码类型
     */
    private String getWebShellCodingType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object codingType = alarm.getExtendedProperties().get("webshell_coding_type");
            if (codingType != null) {
                return codingType.toString();
            }
        }
        return "";
    }
    
    /**
     * 获取WebShell单向性
     */
    private String getWebShellOneWay(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object oneWay = alarm.getExtendedProperties().get("webshell_one_way");
            if (oneWay != null) {
                return oneWay.toString();
            }
        }
        return "";
    }
}
