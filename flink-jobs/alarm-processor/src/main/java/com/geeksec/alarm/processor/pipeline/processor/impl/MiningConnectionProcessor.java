package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;

import java.util.*;

/**
 * 尝试挖矿连接告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class MiningConnectionProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.MINING_CONNECTION_ATTEMPT;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        reasons.add(createDetectionReason(
                "尝试连接挖矿池",
                "检测到尝试连接已知挖矿池地址的行为",
                "正常网络连接",
                alarm.getDstIp(),
                "合法服务器地址",
                7
        ));
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        "阻断与挖矿池的网络连接",
                        "检查主机是否存在挖矿程序",
                        "监控主机资源使用情况"
                ))
                .investigationSteps(Arrays.asList(
                        "分析连接尝试的频率和模式",
                        "检查是否有成功的挖矿连接",
                        "确认挖矿程序的来源"
                ))
                .preventionMeasures(Arrays.asList(
                        "更新挖矿池黑名单",
                        "加强网络出口控制",
                        "定期扫描挖矿程序"
                ))
                .recoverySteps(Arrays.asList(
                        "检测到当前网络存在挖矿软件运行，受害者主机的CPU、GPU被复杂运算的恶意程序占用",
                        "确认告警：对告警进行分析，确认告警正确性",
                        "对日志进行分析，确认所有中招主机",
                        "现状确认：分析安全告警，如连接/查询矿池，告警最早发现时间",
                        "处置措施：在主机上关闭挖矿程序",
                        "恢复系统正常性能和资源使用"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        return "主机尝试连接已知的挖矿池服务器，可能存在挖矿程序或恶意软件。";
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99017";
    }
}
