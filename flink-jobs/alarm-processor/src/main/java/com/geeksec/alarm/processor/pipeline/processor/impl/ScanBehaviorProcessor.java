package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 扫描行为告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class ScanBehaviorProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.SCAN_BEHAVIOR;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String scanType = getScanType(alarm);
        
        switch (scanType) {
            case "渗透工具指纹":
                reasons.add(createDetectionReason(
                        "渗透工具指纹检测",
                        "检测到已知渗透工具的网络指纹特征",
                        "正常工具指纹",
                        getToolFingerprint(alarm),
                        "合法应用指纹",
                        8
                ));
                break;
                
            case "端口扫描sip":
                reasons.add(createDetectionReason(
                        "源IP端口扫描",
                        "检测到来自源IP的大量端口扫描行为",
                        "正常端口访问",
                        String.format("扫描端口数量: %s", getScannedPortCount(alarm)),
                        "少量端口访问",
                        7
                ));
                break;
                
            case "端口扫描dip":
                reasons.add(createDetectionReason(
                        "目标IP端口扫描",
                        "检测到对目标IP的大量端口扫描行为",
                        "正常端口访问",
                        String.format("被扫描端口数量: %s", getScannedPortCount(alarm)),
                        "少量端口访问",
                        7
                ));
                break;
                
            case "web登录爆破":
                reasons.add(createDetectionReason(
                        "Web登录暴力破解",
                        "检测到对Web应用的暴力破解攻击",
                        "正常登录尝试",
                        String.format("尝试次数: %s", getLoginAttempts(alarm)),
                        "少量登录尝试",
                        9
                ));
                break;
                
            default:
                reasons.add(createDetectionReason(
                        "异常扫描行为",
                        "检测到可疑的网络扫描活动",
                        "正常网络访问",
                        "扫描行为特征",
                        "正常访问模式",
                        6
                ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String scanType = getScanType(alarm);
        
        List<String> immediateActions = new ArrayList<>();
        List<String> investigationSteps = new ArrayList<>();
        List<String> preventionMeasures = new ArrayList<>();
        
        switch (scanType) {
            case "web登录爆破":
                immediateActions.addAll(Arrays.asList(
                        "立即封禁攻击源IP地址",
                        "检查是否存在密钥泄露的情况",
                        "强制重置相关账户密码"
                ));
                investigationSteps.addAll(Arrays.asList(
                        "分析登录日志，确认攻击范围",
                        "检查是否有成功登录记录",
                        "审查账户权限和访问记录"
                ));
                preventionMeasures.addAll(Arrays.asList(
                        "启用账户锁定策略",
                        "实施多因素认证",
                        "加强密码复杂度要求"
                ));
                break;
                
            case "端口扫描sip":
            case "端口扫描dip":
                immediateActions.addAll(Arrays.asList(
                        "检查内网主机的端口访问情况",
                        "关闭非必要端口",
                        "加强防火墙规则"
                ));
                investigationSteps.addAll(Arrays.asList(
                        "分析扫描模式和目标",
                        "检查是否有后续攻击行为",
                        "确认扫描来源和动机"
                ));
                preventionMeasures.addAll(Arrays.asList(
                        "配置入侵检测系统",
                        "实施网络分段",
                        "定期进行端口安全审计"
                ));
                break;
                
            default:
                immediateActions.addAll(Arrays.asList(
                        "过滤掉来自该客户端IP的访问",
                        "加强网络监控",
                        "检查系统安全状态"
                ));
                investigationSteps.addAll(Arrays.asList(
                        "分析扫描行为模式",
                        "确认攻击意图",
                        "检查相关系统日志"
                ));
                preventionMeasures.addAll(Arrays.asList(
                        "更新安全策略",
                        "加强访问控制",
                        "定期安全评估"
                ));
        }
        
        // 恢复步骤
        List<String> recoverySteps = new ArrayList<>();
        if ("web登录爆破".equals(scanType)) {
            recoverySteps.addAll(Arrays.asList(
                    "重置被攻击账户的密码",
                    "清理异常登录会话和缓存",
                    "恢复被锁定的用户账户",
                    "修复被爆破攻击影响的Web应用",
                    "恢复正常的登录验证机制"
            ));
        } else if ("端口扫描sip".equals(scanType)) {
            recoverySteps.addAll(Arrays.asList(
                    "关闭不必要的开放端口",
                    "修复被扫描发现的服务漏洞",
                    "恢复正常的网络服务配置",
                    "清理扫描产生的异常连接记录"
            ));
        } else {
            recoverySteps.addAll(Arrays.asList(
                    "修复被扫描发现的安全漏洞",
                    "恢复正常的系统和网络配置",
                    "清理扫描攻击产生的异常记录",
                    "重新配置安全防护措施"
            ));
        }

        return Alarm.HandlingSuggestions.builder()
                .immediateActions(immediateActions)
                .investigationSteps(investigationSteps)
                .preventionMeasures(preventionMeasures)
                .recoverySteps(recoverySteps)
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String scanType = getScanType(alarm);
        switch (scanType) {
            case "web登录爆破":
                return "攻击者通过自动化工具对Web应用进行大量登录尝试，试图破解用户凭据。";
            case "端口扫描sip":
            case "端口扫描dip":
                return "攻击者通过扫描目标主机的端口，探测开放的服务和潜在的攻击入口。";
            case "渗透工具指纹":
                return "攻击者使用专业渗透测试工具进行网络侦察和漏洞探测。";
            default:
                return "攻击者进行网络扫描以收集目标信息，为后续攻击做准备。";
        }
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过分析网络流量模式、连接频率、端口访问行为等特征，识别异常的扫描活动。";
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99002"; // 扫描行为模型ID
    }
    
    /**
     * 获取扫描类型
     */
    private String getScanType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object scanType = alarm.getExtendedProperties().get("scan_type");
            if (scanType != null) {
                return scanType.toString();
            }
        }
        return "未知扫描";
    }
    
    /**
     * 获取工具指纹
     */
    private String getToolFingerprint(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object fingerprint = alarm.getExtendedProperties().get("tool_fingerprint");
            if (fingerprint != null) {
                return fingerprint.toString();
            }
        }
        return "未知工具指纹";
    }
    
    /**
     * 获取扫描端口数量
     */
    private String getScannedPortCount(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object portCount = alarm.getExtendedProperties().get("scanned_port_count");
            if (portCount != null) {
                return portCount.toString();
            }
        }
        return "未知";
    }
    
    /**
     * 获取登录尝试次数
     */
    private String getLoginAttempts(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object attempts = alarm.getExtendedProperties().get("login_attempts");
            if (attempts != null) {
                return attempts.toString();
            }
        }
        return "未知";
    }
}
