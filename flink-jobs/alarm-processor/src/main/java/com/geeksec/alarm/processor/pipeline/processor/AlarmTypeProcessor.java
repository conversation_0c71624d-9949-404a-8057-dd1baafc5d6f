package com.geeksec.alarm.processor.pipeline.processor;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 告警类型处理器接口
 * 为不同告警类型提供特化的处理逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmTypeProcessor extends Serializable {
    
    /**
     * 获取支持的告警类型
     */
    AlarmType getSupportedType();
    
    /**
     * 处理告警，生成特化的告警内容
     * 
     * @param alarm 原始告警对象
     * @param knowledgeBaseClient 知识库客户端
     * @return 处理后的告警对象
     */
    Alarm processAlarm(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient);
    
    /**
     * 生成告警原因分析
     * 
     * @param alarm 告警对象
     * @param knowledgeBaseClient 知识库客户端
     * @return 原因分析列表
     */
    List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient);
    
    /**
     * 生成处理建议
     * 
     * @param alarm 告警对象
     * @param knowledgeBaseClient 知识库客户端
     * @return 处理建议
     */
    Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient);
    
    /**
     * 生成攻击路径
     * 
     * @param alarm 告警对象
     * @param knowledgeBaseClient 知识库客户端
     * @return 攻击路径列表
     */
    List<Map<String, Object>> generateAttackRoute(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient);
    
    /**
     * 获取告警原理说明
     * 
     * @param alarm 告警对象
     * @return 告警原理
     */
    String getAlarmPrinciple(Alarm alarm);
    
    /**
     * 获取检测原理说明
     * 
     * @param alarm 告警对象
     * @return 检测原理
     */
    String getDetectionPrinciple(Alarm alarm);
    
    /**
     * 获取受害者信息
     * 
     * @param alarm 告警对象
     * @return 受害者信息列表
     */
    List<Map<String, String>> getVictimInfo(Alarm alarm);
    
    /**
     * 获取攻击者信息
     * 
     * @param alarm 告警对象
     * @return 攻击者信息列表
     */
    List<Map<String, String>> getAttackerInfo(Alarm alarm);
    
    /**
     * 获取攻击家族信息
     * 
     * @param alarm 告警对象
     * @param knowledgeBaseClient 知识库客户端
     * @return 攻击家族信息列表
     */
    List<Map<String, Object>> getAttackFamily(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient);
    
    /**
     * 获取目标信息
     * 
     * @param alarm 告警对象
     * @return 目标信息列表
     */
    List<Map<String, Object>> getTargets(Alarm alarm);
    
    /**
     * 检查是否需要特殊处理
     * 
     * @param alarm 告警对象
     * @return 是否需要特殊处理
     */
    default boolean requiresSpecialHandling(Alarm alarm) {
        return false;
    }
    
    /**
     * 获取模型ID
     * 
     * @param alarm 告警对象
     * @return 模型ID
     */
    default String getModelId(Alarm alarm) {
        return "99999"; // 默认模型ID
    }
}
