package com.geeksec.flink.common.sink;

import java.util.Properties;

import org.apache.doris.flink.cfg.DorisExecutionOptions;
import org.apache.doris.flink.cfg.DorisOptions;
import org.apache.doris.flink.cfg.DorisReadOptions;
import org.apache.doris.flink.sink.DorisSink;
import org.apache.doris.flink.sink.writer.LoadConstants;
import org.apache.doris.flink.sink.writer.serializer.RowSerializer;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import lombok.extern.slf4j.Slf4j;

/**
 * Flink Doris Sink 管理器
 * 提供 Doris Sink 的构建和配置功能
 *
 * <AUTHOR>
 */
@Slf4j
public class FlinkDorisSinkManager {

    /**
     * 私有构造函数，防止实例化
     */
    private FlinkDorisSinkManager() {
        // 工具类，防止实例化
    }

    /**
     * 构建Doris Sink
     *
     * @param tableName 表名
     * @param config    配置参数
     * @return DorisSink实例
     */
    public static DorisSink<Row> buildDorisSink(String tableName, ParameterTool config) {
        log.info("构建Doris Sink，表名: {}", tableName);

        // 构建Doris选项
        DorisOptions dorisOptions = DorisOptions.builder()
                .setFenodes(config.get("doris.fenodes", "localhost:8030"))
                .setTableIdentifier(config.get("doris.database", "nta") + "." + tableName)
                .setUsername(config.get("doris.username", "root"))
                .setPassword(config.get("doris.password", ""))
                .build();

        // 构建执行选项
        DorisExecutionOptions executionOptions = DorisExecutionOptions.builder()
                .setBufferSize(config.getInt("doris.batch.size", 1000))
                .setBufferFlushMaxRows(config.getInt("doris.batch.size", 1000))
                .setBufferFlushIntervalMs(config.getLong("doris.batch.interval.ms", 5000L))
                .setMaxRetries(config.getInt("doris.max.retries", 3))
                .setStreamLoadProp(getStreamLoadProperties(config))
                .build();

        // 构建读取选项
        DorisReadOptions readOptions = DorisReadOptions.builder().build();

        return DorisSink.<Row>builder()
                .setDorisReadOptions(readOptions)
                .setDorisOptions(dorisOptions)
                .setDorisExecutionOptions(executionOptions)
                .setSerializer(RowSerializer.builder().build())
                .build();
    }

    /**
     * 构建带自定义配置的Doris Sink
     *
     * @param tableName   表名
     * @param dorisConfig Doris配置
     * @return DorisSink实例
     */
    public static DorisSink<Row> buildDorisSink(String tableName, DorisConfig dorisConfig) {
        log.info("构建Doris Sink，表名: {}", tableName);

        // 构建Doris选项
        DorisOptions dorisOptions = DorisOptions.builder()
                .setFenodes(dorisConfig.getFenodes())
                .setTableIdentifier(dorisConfig.getDatabase() + "." + tableName)
                .setUsername(dorisConfig.getUsername())
                .setPassword(dorisConfig.getPassword())
                .build();

        // 构建执行选项
        DorisExecutionOptions executionOptions = DorisExecutionOptions.builder()
                .setBufferSize(dorisConfig.getBatchSize())
                .setBufferFlushMaxRows(dorisConfig.getBatchSize())
                .setBufferFlushIntervalMs(dorisConfig.getBatchIntervalMs())
                .setMaxRetries(dorisConfig.getMaxRetries())
                .setStreamLoadProp(dorisConfig.getStreamLoadProperties())
                .build();

        // 构建读取选项
        DorisReadOptions readOptions = DorisReadOptions.builder().build();

        return DorisSink.<Row>builder()
                .setDorisReadOptions(readOptions)
                .setDorisOptions(dorisOptions)
                .setDorisExecutionOptions(executionOptions)
                .setSerializer(RowSerializer.builder().build())
                .build();
    }

    /**
     * 配置单个Doris Sink
     *
     * @param sideOutStream 侧输出流
     * @param outputTag     输出标签
     * @param tableName     Doris表名
     * @param sinkName      Sink名称
     * @param parallelism   并行度
     * @param config        配置参数
     */
    public static <T> void configureSink(
            SingleOutputStreamOperator<Row> sideOutStream,
            OutputTag<Row> outputTag,
            String tableName,
            String sinkName,
            int parallelism,
            ParameterTool config) {
        DataStream<Row> stream = sideOutStream.getSideOutput(outputTag);
        stream.sinkTo(buildDorisSink(tableName, config))
                .name(sinkName + " Doris Sink")
                .setParallelism(parallelism);
    }

    /**
     * 配置数据流的Doris Sink
     *
     * @param stream      数据流
     * @param tableName   表名
     * @param sinkName    Sink名称
     * @param parallelism 并行度
     * @param config      配置参数
     */
    public static void configureDorisSink(
            DataStream<Row> stream,
            String tableName,
            String sinkName,
            int parallelism,
            ParameterTool config) {
        stream.sinkTo(buildDorisSink(tableName, config))
                .name(sinkName + " Doris Sink")
                .setParallelism(parallelism);
    }

    /**
     * 获取Stream Load属性
     *
     * @param config 配置参数（保留参数以保持接口兼容性，但不再使用）
     * @return Stream Load属性
     */
    private static Properties getStreamLoadProperties(ParameterTool config) {
        Properties streamLoadProp = new Properties();
        streamLoadProp.setProperty(LoadConstants.FORMAT_KEY, LoadConstants.JSON);
        streamLoadProp.setProperty(LoadConstants.READ_JSON_BY_LINE, "true");
        return streamLoadProp;
    }

    /**
     * Doris配置类
     */
    public static class DorisConfig {
        private String fenodes = "localhost:8030";
        private String database = "nta";
        private String username = "root";
        private String password = "";
        private int batchSize = 1000;
        private long batchIntervalMs = 5000L;
        private int maxRetries = 3;
        private Properties streamLoadProperties = new Properties();

        // Getters and Setters
        public String getFenodes() {
            return fenodes;
        }

        public void setFenodes(String fenodes) {
            this.fenodes = fenodes;
        }

        public String getDatabase() {
            return database;
        }

        public void setDatabase(String database) {
            this.database = database;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public int getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }

        public long getBatchIntervalMs() {
            return batchIntervalMs;
        }

        public void setBatchIntervalMs(long batchIntervalMs) {
            this.batchIntervalMs = batchIntervalMs;
        }

        public int getMaxRetries() {
            return maxRetries;
        }

        public void setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
        }

        public Properties getStreamLoadProperties() {
            return streamLoadProperties;
        }

        public void setStreamLoadProperties(Properties streamLoadProperties) {
            this.streamLoadProperties = streamLoadProperties;
        }
    }

}