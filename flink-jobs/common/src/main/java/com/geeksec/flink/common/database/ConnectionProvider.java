package com.geeksec.flink.common.database;

import java.sql.SQLException;

/**
 * 通用数据库连接提供者接口
 * 定义了获取和关闭连接的契约，支持各种类型的数据库连接
 *
 * @param <T> 连接对象的类型，例如 java.sql.Connection 或 redis.clients.jedis.Jedis
 * <AUTHOR>
 * @since 3.0.0
 */
public interface ConnectionProvider<T> extends AutoCloseable {

    /**
     * 获取一个数据库连接
     *
     * @return 连接对象
     * @throws SQLException 如果获取连接失败
     */
    T getConnection() throws SQLException;

    /**
     * 释放或关闭提供的连接
     * 如果连接是从连接池获取的，通常是将其返回到池中
     *
     * @param connection 要关闭的连接对象
     */
    void releaseConnection(T connection);

    /**
     * 测试连接是否有效
     *
     * @return 连接是否有效
     */
    default boolean testConnection() {
        try (T connection = getConnection()) {
            return connection != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取连接池状态信息
     *
     * @return 连接池状态信息
     */
    default ConnectionPoolStatus getPoolStatus() {
        return ConnectionPoolStatus.unknown();
    }

    /**
     * 关闭连接提供者本身，释放所有底层资源（例如连接池）
     * 实现 AutoCloseable 接口，允许在 try-with-resources 语句中使用
     *
     * @throws Exception 关闭过程中发生的异常
     */
    @Override
    void close() throws Exception;

    /**
     * 连接池状态信息
     */
    class ConnectionPoolStatus {
        private final int activeConnections;
        private final int idleConnections;
        private final int totalConnections;
        private final int maxConnections;
        private final boolean available;

        public ConnectionPoolStatus(int activeConnections, int idleConnections, 
                                  int totalConnections, int maxConnections, boolean available) {
            this.activeConnections = activeConnections;
            this.idleConnections = idleConnections;
            this.totalConnections = totalConnections;
            this.maxConnections = maxConnections;
            this.available = available;
        }

        public static ConnectionPoolStatus unknown() {
            return new ConnectionPoolStatus(-1, -1, -1, -1, false);
        }

        public int getActiveConnections() {
            return activeConnections;
        }

        public int getIdleConnections() {
            return idleConnections;
        }

        public int getTotalConnections() {
            return totalConnections;
        }

        public int getMaxConnections() {
            return maxConnections;
        }

        public boolean isAvailable() {
            return available;
        }

        @Override
        public String toString() {
            return String.format("ConnectionPoolStatus{active=%d, idle=%d, total=%d, max=%d, available=%s}",
                    activeConnections, idleConnections, totalConnections, maxConnections, available);
        }
    }
}
