package com.geeksec.flink.common.exception;

import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 重试工具类
 * 提供各种重试策略和重试机制
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class RetryUtils {

    private RetryUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 重试配置
     */
    public static class RetryConfig {
        /** 最大重试次数 */
        private int maxAttempts = 3;
        
        /** 基础延迟时间（毫秒） */
        private long baseDelayMillis = 1000L;
        
        /** 最大延迟时间（毫秒） */
        private long maxDelayMillis = 30000L;
        
        /** 退避倍数 */
        private double backoffMultiplier = 2.0;
        
        /** 是否启用抖动 */
        private boolean jitterEnabled = true;
        
        /** 重试条件判断 */
        private Predicate<Throwable> retryCondition = throwable -> true;

        public RetryConfig maxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
            return this;
        }

        public RetryConfig baseDelay(Duration baseDelay) {
            this.baseDelayMillis = baseDelay.toMillis();
            return this;
        }

        public RetryConfig maxDelay(Duration maxDelay) {
            this.maxDelayMillis = maxDelay.toMillis();
            return this;
        }

        public RetryConfig backoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
            return this;
        }

        public RetryConfig jitterEnabled(boolean jitterEnabled) {
            this.jitterEnabled = jitterEnabled;
            return this;
        }

        public RetryConfig retryOn(Class<? extends Throwable> exceptionClass) {
            this.retryCondition = exceptionClass::isInstance;
            return this;
        }

        public RetryConfig retryIf(Predicate<Throwable> condition) {
            this.retryCondition = condition;
            return this;
        }

        public RetryConfig retryOnFlinkJobException() {
            this.retryCondition = throwable -> 
                throwable instanceof FlinkJobException && ((FlinkJobException) throwable).isRetryable();
            return this;
        }

        // Getters
        public int getMaxAttempts() { return maxAttempts; }
        public long getBaseDelayMillis() { return baseDelayMillis; }
        public long getMaxDelayMillis() { return maxDelayMillis; }
        public double getBackoffMultiplier() { return backoffMultiplier; }
        public boolean isJitterEnabled() { return jitterEnabled; }
        public Predicate<Throwable> getRetryCondition() { return retryCondition; }
    }

    /**
     * 重试结果
     */
    public static class RetryResult<T> {
        private final T result;
        private final boolean success;
        private final int attemptCount;
        private final Throwable lastException;
        private final long totalDurationMillis;

        public RetryResult(T result, boolean success, int attemptCount, 
                          Throwable lastException, long totalDurationMillis) {
            this.result = result;
            this.success = success;
            this.attemptCount = attemptCount;
            this.lastException = lastException;
            this.totalDurationMillis = totalDurationMillis;
        }

        public T getResult() { return result; }
        public boolean isSuccess() { return success; }
        public int getAttemptCount() { return attemptCount; }
        public Throwable getLastException() { return lastException; }
        public long getTotalDurationMillis() { return totalDurationMillis; }
    }

    /**
     * 执行带重试的操作
     * 
     * @param supplier 操作供应商
     * @param config 重试配置
     * @param <T> 返回类型
     * @return 操作结果
     * @throws FlinkJobException 重试失败时抛出
     */
    public static <T> T executeWithRetry(Supplier<T> supplier, RetryConfig config) {
        RetryResult<T> result = executeWithRetryResult(supplier, config);
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            throw FlinkJobException.builder(ErrorCode.OPERATION_TIMEOUT)
                    .cause(result.getLastException())
                    .context("attemptCount", result.getAttemptCount())
                    .context("totalDurationMillis", result.getTotalDurationMillis())
                    .args("重试 " + result.getAttemptCount() + " 次后仍然失败")
                    .build();
        }
    }

    /**
     * 执行带重试的操作并返回详细结果
     * 
     * @param supplier 操作供应商
     * @param config 重试配置
     * @param <T> 返回类型
     * @return 重试结果
     */
    public static <T> RetryResult<T> executeWithRetryResult(Supplier<T> supplier, RetryConfig config) {
        long startTime = System.currentTimeMillis();
        Throwable lastException = null;
        
        for (int attempt = 1; attempt <= config.getMaxAttempts(); attempt++) {
            try {
                log.debug("执行操作，第 {} 次尝试", attempt);
                T result = supplier.get();
                long duration = System.currentTimeMillis() - startTime;
                log.debug("操作成功，尝试次数: {}, 总耗时: {}ms", attempt, duration);
                return new RetryResult<>(result, true, attempt, null, duration);
            } catch (Exception e) {
                lastException = e;
                log.debug("操作失败，第 {} 次尝试: {}", attempt, e.getMessage());
                
                // 检查是否应该重试
                if (!config.getRetryCondition().test(e)) {
                    log.debug("异常不满足重试条件，停止重试: {}", e.getClass().getSimpleName());
                    break;
                }
                
                // 如果不是最后一次尝试，则等待后重试
                if (attempt < config.getMaxAttempts()) {
                    long delay = calculateDelay(attempt, config);
                    log.debug("等待 {}ms 后进行第 {} 次重试", delay, attempt + 1);
                    sleep(delay);
                }
            }
        }
        
        long duration = System.currentTimeMillis() - startTime;
        log.warn("操作最终失败，总尝试次数: {}, 总耗时: {}ms", config.getMaxAttempts(), duration);
        return new RetryResult<>(null, false, config.getMaxAttempts(), lastException, duration);
    }

    /**
     * 执行带重试的 Runnable 操作
     * 
     * @param runnable 可运行操作
     * @param config 重试配置
     * @throws FlinkJobException 重试失败时抛出
     */
    public static void executeWithRetry(Runnable runnable, RetryConfig config) {
        executeWithRetry(() -> {
            runnable.run();
            return null;
        }, config);
    }

    /**
     * 计算延迟时间
     * 
     * @param attempt 当前尝试次数
     * @param config 重试配置
     * @return 延迟时间（毫秒）
     */
    private static long calculateDelay(int attempt, RetryConfig config) {
        // 指数退避算法
        long delay = (long) (config.getBaseDelayMillis() * Math.pow(config.getBackoffMultiplier(), attempt - 1));
        
        // 限制最大延迟时间
        delay = Math.min(delay, config.getMaxDelayMillis());
        
        // 添加抖动以避免雷群效应
        if (config.isJitterEnabled()) {
            double jitter = ThreadLocalRandom.current().nextDouble(0.5, 1.5);
            delay = (long) (delay * jitter);
        }
        
        return delay;
    }

    /**
     * 安全睡眠
     * 
     * @param millis 睡眠时间（毫秒）
     */
    private static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new FlinkJobException(ErrorCode.OPERATION_TIMEOUT, "重试过程中被中断", e);
        }
    }

    // ==================== 预定义重试配置 ====================

    /**
     * 创建默认重试配置
     * 
     * @return 默认重试配置
     */
    public static RetryConfig defaultConfig() {
        return new RetryConfig();
    }

    /**
     * 创建快速重试配置（适用于轻量级操作）
     * 
     * @return 快速重试配置
     */
    public static RetryConfig fastRetry() {
        return new RetryConfig()
                .maxAttempts(5)
                .baseDelay(Duration.ofMillis(100))
                .maxDelay(Duration.ofSeconds(2))
                .backoffMultiplier(1.5);
    }

    /**
     * 创建慢速重试配置（适用于重量级操作）
     * 
     * @return 慢速重试配置
     */
    public static RetryConfig slowRetry() {
        return new RetryConfig()
                .maxAttempts(3)
                .baseDelay(Duration.ofSeconds(5))
                .maxDelay(Duration.ofMinutes(2))
                .backoffMultiplier(2.0);
    }

    /**
     * 创建数据库重试配置
     * 
     * @return 数据库重试配置
     */
    public static RetryConfig databaseRetry() {
        return new RetryConfig()
                .maxAttempts(3)
                .baseDelay(Duration.ofSeconds(1))
                .maxDelay(Duration.ofSeconds(30))
                .backoffMultiplier(2.0)
                .retryIf(throwable -> 
                    throwable instanceof FlinkJobException &&
                    ((FlinkJobException) throwable).getErrorCode().getCode().startsWith("2") // 数据库错误码
                );
    }

    /**
     * 创建网络重试配置
     * 
     * @return 网络重试配置
     */
    public static RetryConfig networkRetry() {
        return new RetryConfig()
                .maxAttempts(5)
                .baseDelay(Duration.ofMillis(500))
                .maxDelay(Duration.ofSeconds(10))
                .backoffMultiplier(1.8)
                .retryIf(throwable -> 
                    throwable instanceof FlinkJobException &&
                    ((FlinkJobException) throwable).getErrorCode().getCode().startsWith("6") // 网络错误码
                );
    }

    /**
     * 创建 Kafka 重试配置
     * 
     * @return Kafka 重试配置
     */
    public static RetryConfig kafkaRetry() {
        return new RetryConfig()
                .maxAttempts(3)
                .baseDelay(Duration.ofSeconds(2))
                .maxDelay(Duration.ofSeconds(20))
                .backoffMultiplier(2.0)
                .retryIf(throwable -> 
                    throwable instanceof FlinkJobException &&
                    ((FlinkJobException) throwable).getErrorCode().getCode().startsWith("3") // Kafka 错误码
                );
    }

    /**
     * 创建无重试配置
     * 
     * @return 无重试配置
     */
    public static RetryConfig noRetry() {
        return new RetryConfig().maxAttempts(1);
    }
}
