package com.geeksec.flink.common.database;

import com.geeksec.flink.common.database.config.DatabaseConfig;
import com.geeksec.flink.common.database.config.DorisConfig;
import com.geeksec.flink.common.database.config.PostgreSQLConfig;
import com.geeksec.flink.common.database.config.RedisConfig;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Flink 数据库连接管理器
 * 提供统一的数据库连接管理功能，支持连接池和连接复用
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class FlinkDatabaseConnectionManager {

    /** 单例实例 */
    private static volatile FlinkDatabaseConnectionManager instance;

    /** 数据库连接池缓存 */
    private final Map<String, Object> connectionPools = new ConcurrentHashMap<>();

    /** 私有构造函数 */
    private FlinkDatabaseConnectionManager() {
        // 单例模式
    }

    /**
     * 获取单例实例
     * 
     * @return 连接管理器实例
     */
    public static FlinkDatabaseConnectionManager getInstance() {
        if (instance == null) {
            synchronized (FlinkDatabaseConnectionManager.class) {
                if (instance == null) {
                    instance = new FlinkDatabaseConnectionManager();
                }
            }
        }
        return instance;
    }

    /**
     * 获取 PostgreSQL 连接
     * 
     * @param config PostgreSQL 配置
     * @return 数据库连接
     * @throws SQLException 连接异常
     */
    public Connection getPostgreSQLConnection(PostgreSQLConfig config) throws SQLException {
        validateConfig(config);
        
        try {
            // 加载驱动
            Class.forName(config.getDriverClassName());
            
            // 创建连接
            return DriverManager.getConnection(
                config.getConnectionUrl(),
                config.getConnectionProperties()
            );
        } catch (ClassNotFoundException e) {
            throw new SQLException("PostgreSQL 驱动未找到", e);
        }
    }

    /**
     * 获取 Doris 连接
     * 
     * @param config Doris 配置
     * @return 数据库连接
     * @throws SQLException 连接异常
     */
    public Connection getDorisConnection(DorisConfig config) throws SQLException {
        validateConfig(config);
        
        try {
            // 加载驱动
            Class.forName(config.getDriverClassName());
            
            // 创建连接
            return DriverManager.getConnection(
                config.getConnectionUrl(),
                config.getConnectionProperties()
            );
        } catch (ClassNotFoundException e) {
            throw new SQLException("Doris 驱动未找到", e);
        }
    }

    /**
     * 获取 Redis 连接
     * 
     * @param config Redis 配置
     * @return Redis 连接
     */
    public Jedis getRedisConnection(RedisConfig config) {
        validateRedisConfig(config);
        
        JedisPool pool = getOrCreateRedisPool(config);
        return pool.getResource();
    }

    /**
     * 获取或创建 Redis 连接池
     * 
     * @param config Redis 配置
     * @return Redis 连接池
     */
    private JedisPool getOrCreateRedisPool(RedisConfig config) {
        String poolKey = generateRedisPoolKey(config);
        
        return (JedisPool) connectionPools.computeIfAbsent(poolKey, key -> {
            log.info("创建 Redis 连接池: {}", config);
            
            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(config.getMaxTotal());
            poolConfig.setMaxIdle(config.getMaxIdle());
            poolConfig.setMinIdle(config.getMinIdle());
            poolConfig.setMaxWaitMillis(config.getMaxWaitMillis());
            poolConfig.setTestOnBorrow(config.isTestOnBorrow());
            poolConfig.setTestOnReturn(config.isTestOnReturn());
            poolConfig.setTestWhileIdle(config.isTestWhileIdle());
            poolConfig.setTimeBetweenEvictionRunsMillis(config.getTimeBetweenEvictionRunsMillis());
            poolConfig.setMinEvictableIdleTimeMillis(config.getMinEvictableIdleTimeMillis());
            poolConfig.setNumTestsPerEvictionRun(config.getNumTestsPerEvictionRun());
            poolConfig.setJmxEnabled(config.isJmxEnabled());
            if (config.isJmxEnabled()) {
                poolConfig.setJmxNamePrefix(config.getJmxNamePrefix());
            }

            if (config.getPassword() != null && !config.getPassword().trim().isEmpty()) {
                return new JedisPool(poolConfig, config.getHost(), config.getPort(),
                        config.getConnectionTimeout(), config.getSocketTimeout(),
                        config.getPassword(), config.getDatabase(), null);
            } else {
                return new JedisPool(poolConfig, config.getHost(), config.getPort(),
                        config.getConnectionTimeout(), config.getSocketTimeout(),
                        null, config.getDatabase(), null);
            }
        });
    }

    /**
     * 生成 Redis 连接池键
     * 
     * @param config Redis 配置
     * @return 连接池键
     */
    private String generateRedisPoolKey(RedisConfig config) {
        return String.format("redis_%s_%d_%d", config.getHost(), config.getPort(), config.getDatabase());
    }

    /**
     * 测试 PostgreSQL 连接
     * 
     * @param config PostgreSQL 配置
     * @return 连接是否成功
     */
    public boolean testPostgreSQLConnection(PostgreSQLConfig config) {
        try (Connection connection = getPostgreSQLConnection(config)) {
            return connection != null && !connection.isClosed();
        } catch (Exception e) {
            log.error("PostgreSQL 连接测试失败: {}", config, e);
            return false;
        }
    }

    /**
     * 测试 Doris 连接
     * 
     * @param config Doris 配置
     * @return 连接是否成功
     */
    public boolean testDorisConnection(DorisConfig config) {
        try (Connection connection = getDorisConnection(config)) {
            return connection != null && !connection.isClosed();
        } catch (Exception e) {
            log.error("Doris 连接测试失败: {}", config, e);
            return false;
        }
    }

    /**
     * 测试 Redis 连接
     * 
     * @param config Redis 配置
     * @return 连接是否成功
     */
    public boolean testRedisConnection(RedisConfig config) {
        try (Jedis jedis = getRedisConnection(config)) {
            String response = jedis.ping();
            return "PONG".equals(response);
        } catch (Exception e) {
            log.error("Redis 连接测试失败: {}", config, e);
            return false;
        }
    }

    /**
     * 关闭所有连接池
     */
    public void closeAllPools() {
        log.info("开始关闭所有数据库连接池");
        
        connectionPools.values().forEach(pool -> {
            try {
                if (pool instanceof JedisPool) {
                    ((JedisPool) pool).close();
                }
            } catch (Exception e) {
                log.error("关闭连接池失败", e);
            }
        });
        
        connectionPools.clear();
        log.info("所有数据库连接池已关闭");
    }

    /**
     * 获取连接池状态
     * 
     * @param config Redis 配置
     * @return 连接池状态
     */
    public ConnectionProvider.ConnectionPoolStatus getRedisPoolStatus(RedisConfig config) {
        String poolKey = generateRedisPoolKey(config);
        JedisPool pool = (JedisPool) connectionPools.get(poolKey);
        
        if (pool == null) {
            return ConnectionProvider.ConnectionPoolStatus.unknown();
        }
        
        return new ConnectionProvider.ConnectionPoolStatus(
            pool.getNumActive(),
            pool.getNumIdle(),
            pool.getNumActive() + pool.getNumIdle(),
            config.getMaxTotal(),
            !pool.isClosed()
        );
    }

    /**
     * 验证数据库配置
     * 
     * @param config 数据库配置
     * @throws IllegalArgumentException 配置无效时抛出
     */
    private void validateConfig(DatabaseConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("数据库配置不能为空");
        }
        if (!config.isValid()) {
            throw new IllegalArgumentException("数据库配置无效: " + config);
        }
    }

    /**
     * 验证 Redis 配置
     * 
     * @param config Redis 配置
     * @throws IllegalArgumentException 配置无效时抛出
     */
    private void validateRedisConfig(RedisConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("Redis 配置不能为空");
        }
        if (!config.isValid()) {
            throw new IllegalArgumentException("Redis 配置无效: " + config);
        }
    }

    /**
     * 关闭连接管理器
     */
    public void shutdown() {
        closeAllPools();
        instance = null;
    }
}
