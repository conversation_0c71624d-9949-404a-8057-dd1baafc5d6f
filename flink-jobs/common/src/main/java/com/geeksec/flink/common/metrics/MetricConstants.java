package com.geeksec.flink.common.metrics;

/**
 * 指标常量类
 * 定义 Flink 作业中所有指标的名称和分组
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public final class MetricConstants {

    private MetricConstants() {
        // 工具类，禁止实例化
    }

    // ==================== 指标分组 ====================

    /** 作业级别指标分组 */
    public static final String GROUP_JOB = "job";
    
    /** 算子级别指标分组 */
    public static final String GROUP_OPERATOR = "operator";
    
    /** 源算子指标分组 */
    public static final String GROUP_SOURCE = "source";
    
    /** 汇算子指标分组 */
    public static final String GROUP_SINK = "sink";
    
    /** 处理函数指标分组 */
    public static final String GROUP_PROCESS = "process";
    
    /** 过滤器指标分组 */
    public static final String GROUP_FILTER = "filter";
    
    /** 转换器指标分组 */
    public static final String GROUP_TRANSFORM = "transform";
    
    /** 聚合器指标分组 */
    public static final String GROUP_AGGREGATE = "aggregate";
    
    /** 窗口指标分组 */
    public static final String GROUP_WINDOW = "window";
    
    /** 缓存指标分组 */
    public static final String GROUP_CACHE = "cache";
    
    /** 数据库指标分组 */
    public static final String GROUP_DATABASE = "database";
    
    /** Kafka 指标分组 */
    public static final String GROUP_KAFKA = "kafka";
    
    /** 网络指标分组 */
    public static final String GROUP_NETWORK = "network";
    
    /** 错误指标分组 */
    public static final String GROUP_ERROR = "error";
    
    /** 性能指标分组 */
    public static final String GROUP_PERFORMANCE = "performance";

    // ==================== 通用计数器指标 ====================

    /** 总记录数 */
    public static final String COUNTER_TOTAL_RECORDS = "total_records";
    
    /** 处理成功记录数 */
    public static final String COUNTER_SUCCESS_RECORDS = "success_records";
    
    /** 处理失败记录数 */
    public static final String COUNTER_FAILED_RECORDS = "failed_records";
    
    /** 跳过记录数 */
    public static final String COUNTER_SKIPPED_RECORDS = "skipped_records";
    
    /** 过滤记录数 */
    public static final String COUNTER_FILTERED_RECORDS = "filtered_records";
    
    /** 重试次数 */
    public static final String COUNTER_RETRIES = "retries";
    
    /** 错误次数 */
    public static final String COUNTER_ERRORS = "errors";
    
    /** 警告次数 */
    public static final String COUNTER_WARNINGS = "warnings";

    // ==================== 业务特定计数器指标 ====================

    /** 告警总数 */
    public static final String COUNTER_TOTAL_ALARMS = "total_alarms";
    
    /** 有效告警数 */
    public static final String COUNTER_VALID_ALARMS = "valid_alarms";
    
    /** 无效告警数 */
    public static final String COUNTER_INVALID_ALARMS = "invalid_alarms";
    
    /** 重复告警数 */
    public static final String COUNTER_DUPLICATE_ALARMS = "duplicate_alarms";
    
    /** 抑制告警数 */
    public static final String COUNTER_SUPPRESSED_ALARMS = "suppressed_alarms";
    
    /** 链式告警数 */
    public static final String COUNTER_CHAINED_ALARMS = "chained_alarms";
    
    /** 孤立告警数 */
    public static final String COUNTER_ISOLATED_ALARMS = "isolated_alarms";
    
    /** 新攻击链数 */
    public static final String COUNTER_NEW_CHAINS = "new_chains";
    
    /** 威胁检测数 */
    public static final String COUNTER_THREATS_DETECTED = "threats_detected";
    
    /** 恶意流量数 */
    public static final String COUNTER_MALICIOUS_TRAFFIC = "malicious_traffic";
    
    /** 证书分析数 */
    public static final String COUNTER_CERTIFICATES_ANALYZED = "certificates_analyzed";
    
    /** 异常证书数 */
    public static final String COUNTER_ANOMALOUS_CERTIFICATES = "anomalous_certificates";

    // ==================== 数据源指标 ====================

    /** Kafka 消息接收数 */
    public static final String COUNTER_KAFKA_MESSAGES_RECEIVED = "kafka_messages_received";
    
    /** Kafka 消息发送数 */
    public static final String COUNTER_KAFKA_MESSAGES_SENT = "kafka_messages_sent";
    
    /** Kafka 消费延迟 */
    public static final String GAUGE_KAFKA_CONSUMER_LAG = "kafka_consumer_lag";
    
    /** Kafka 连接错误数 */
    public static final String COUNTER_KAFKA_CONNECTION_ERRORS = "kafka_connection_errors";
    
    /** 数据库查询数 */
    public static final String COUNTER_DATABASE_QUERIES = "database_queries";
    
    /** 数据库连接错误数 */
    public static final String COUNTER_DATABASE_CONNECTION_ERRORS = "database_connection_errors";

    // ==================== 性能指标 ====================

    /** 处理延迟（毫秒） */
    public static final String HISTOGRAM_PROCESSING_LATENCY = "processing_latency_ms";
    
    /** 端到端延迟（毫秒） */
    public static final String HISTOGRAM_END_TO_END_LATENCY = "end_to_end_latency_ms";
    
    /** 吞吐量（记录/秒） */
    public static final String METER_THROUGHPUT = "throughput_records_per_second";
    
    /** CPU 使用率 */
    public static final String GAUGE_CPU_USAGE = "cpu_usage_percent";
    
    /** 内存使用率 */
    public static final String GAUGE_MEMORY_USAGE = "memory_usage_percent";
    
    /** 堆内存使用量 */
    public static final String GAUGE_HEAP_MEMORY_USED = "heap_memory_used_bytes";
    
    /** 非堆内存使用量 */
    public static final String GAUGE_NON_HEAP_MEMORY_USED = "non_heap_memory_used_bytes";
    
    /** GC 次数 */
    public static final String COUNTER_GC_COUNT = "gc_count";
    
    /** GC 时间（毫秒） */
    public static final String COUNTER_GC_TIME = "gc_time_ms";

    // ==================== 缓存指标 ====================

    /** 缓存大小 */
    public static final String GAUGE_CACHE_SIZE = "cache_size";
    
    /** 缓存命中率 */
    public static final String GAUGE_CACHE_HIT_RATE = "cache_hit_rate";
    
    /** 缓存未命中率 */
    public static final String GAUGE_CACHE_MISS_RATE = "cache_miss_rate";
    
    /** 缓存命中数 */
    public static final String COUNTER_CACHE_HITS = "cache_hits";
    
    /** 缓存未命中数 */
    public static final String COUNTER_CACHE_MISSES = "cache_misses";
    
    /** 缓存加载数 */
    public static final String COUNTER_CACHE_LOADS = "cache_loads";
    
    /** 缓存驱逐数 */
    public static final String COUNTER_CACHE_EVICTIONS = "cache_evictions";
    
    /** 缓存加载时间（毫秒） */
    public static final String HISTOGRAM_CACHE_LOAD_TIME = "cache_load_time_ms";

    // ==================== 网络指标 ====================

    /** 网络字节接收数 */
    public static final String COUNTER_NETWORK_BYTES_RECEIVED = "network_bytes_received";
    
    /** 网络字节发送数 */
    public static final String COUNTER_NETWORK_BYTES_SENT = "network_bytes_sent";
    
    /** 网络包接收数 */
    public static final String COUNTER_NETWORK_PACKETS_RECEIVED = "network_packets_received";
    
    /** 网络包发送数 */
    public static final String COUNTER_NETWORK_PACKETS_SENT = "network_packets_sent";
    
    /** 网络连接数 */
    public static final String GAUGE_NETWORK_CONNECTIONS = "network_connections";
    
    /** 网络错误数 */
    public static final String COUNTER_NETWORK_ERRORS = "network_errors";

    // ==================== 状态指标 ====================

    /** 状态大小（字节） */
    public static final String GAUGE_STATE_SIZE = "state_size_bytes";
    
    /** 状态后端延迟（毫秒） */
    public static final String HISTOGRAM_STATE_BACKEND_LATENCY = "state_backend_latency_ms";
    
    /** 检查点大小（字节） */
    public static final String GAUGE_CHECKPOINT_SIZE = "checkpoint_size_bytes";
    
    /** 检查点持续时间（毫秒） */
    public static final String HISTOGRAM_CHECKPOINT_DURATION = "checkpoint_duration_ms";
    
    /** 检查点对齐时间（毫秒） */
    public static final String HISTOGRAM_CHECKPOINT_ALIGNMENT_TIME = "checkpoint_alignment_time_ms";

    // ==================== 错误指标 ====================

    /** 序列化错误数 */
    public static final String COUNTER_SERIALIZATION_ERRORS = "serialization_errors";
    
    /** 反序列化错误数 */
    public static final String COUNTER_DESERIALIZATION_ERRORS = "deserialization_errors";
    
    /** 解析错误数 */
    public static final String COUNTER_PARSING_ERRORS = "parsing_errors";
    
    /** 验证错误数 */
    public static final String COUNTER_VALIDATION_ERRORS = "validation_errors";
    
    /** 转换错误数 */
    public static final String COUNTER_TRANSFORMATION_ERRORS = "transformation_errors";
    
    /** 超时错误数 */
    public static final String COUNTER_TIMEOUT_ERRORS = "timeout_errors";
    
    /** 连接错误数 */
    public static final String COUNTER_CONNECTION_ERRORS = "connection_errors";

    // ==================== 业务指标 ====================

    /** 会话数 */
    public static final String GAUGE_ACTIVE_SESSIONS = "active_sessions";
    
    /** 协议类型分布 */
    public static final String COUNTER_PROTOCOL_DISTRIBUTION = "protocol_distribution";
    
    /** 数据质量评分 */
    public static final String GAUGE_DATA_QUALITY_SCORE = "data_quality_score";
    
    /** 处理队列长度 */
    public static final String GAUGE_PROCESSING_QUEUE_LENGTH = "processing_queue_length";
    
    /** 待处理任务数 */
    public static final String GAUGE_PENDING_TASKS = "pending_tasks";

    // ==================== 指标名称构建方法 ====================

    /**
     * 构建分组指标名称
     * 
     * @param group 指标分组
     * @param metric 指标名称
     * @return 完整的指标名称
     */
    public static String buildMetricName(String group, String metric) {
        return group + "." + metric;
    }

    /**
     * 构建带子分组的指标名称
     * 
     * @param group 主分组
     * @param subGroup 子分组
     * @param metric 指标名称
     * @return 完整的指标名称
     */
    public static String buildMetricName(String group, String subGroup, String metric) {
        return group + "." + subGroup + "." + metric;
    }

    /**
     * 构建算子指标名称
     * 
     * @param operatorName 算子名称
     * @param metric 指标名称
     * @return 完整的指标名称
     */
    public static String buildOperatorMetricName(String operatorName, String metric) {
        return buildMetricName(GROUP_OPERATOR, operatorName, metric);
    }

    /**
     * 构建源算子指标名称
     * 
     * @param sourceName 源名称
     * @param metric 指标名称
     * @return 完整的指标名称
     */
    public static String buildSourceMetricName(String sourceName, String metric) {
        return buildMetricName(GROUP_SOURCE, sourceName, metric);
    }

    /**
     * 构建汇算子指标名称
     * 
     * @param sinkName 汇名称
     * @param metric 指标名称
     * @return 完整的指标名称
     */
    public static String buildSinkMetricName(String sinkName, String metric) {
        return buildMetricName(GROUP_SINK, sinkName, metric);
    }

    /**
     * 构建缓存指标名称
     * 
     * @param cacheName 缓存名称
     * @param metric 指标名称
     * @return 完整的指标名称
     */
    public static String buildCacheMetricName(String cacheName, String metric) {
        return buildMetricName(GROUP_CACHE, cacheName, metric);
    }

    /**
     * 构建错误指标名称
     * 
     * @param errorType 错误类型
     * @param metric 指标名称
     * @return 完整的指标名称
     */
    public static String buildErrorMetricName(String errorType, String metric) {
        return buildMetricName(GROUP_ERROR, errorType, metric);
    }
}
