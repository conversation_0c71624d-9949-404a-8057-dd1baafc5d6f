package com.geeksec.flink.common.database.config;

import com.geeksec.flink.common.constants.FlinkConfigConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.Properties;

/**
 * Doris 数据库配置类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DorisConfig extends DatabaseConfig {

    private static final long serialVersionUID = 1L;

    /** 默认查询端口 */
    private static final int DEFAULT_QUERY_PORT = 9030;

    /** 默认 HTTP 端口 */
    private static final int DEFAULT_HTTP_PORT = 8030;

    /** 默认驱动类名 */
    private static final String DRIVER_CLASS_NAME = "com.mysql.cj.jdbc.Driver";

    /** FE 节点地址（格式：host1:port1,host2:port2） */
    private String fenodes;

    /** HTTP 端口 */
    private int httpPort = DEFAULT_HTTP_PORT;

    /** 查询端口 */
    private int queryPort = DEFAULT_QUERY_PORT;

    /** 批量大小 */
    private int batchSize = 1000;

    /** 批量间隔（毫秒） */
    private long batchIntervalMs = 5000L;

    /** 最大重试次数 */
    private int maxRetries = 3;

    /** Stream Load 属性 */
    private Properties streamLoadProperties = new Properties();

    /** 是否启用删除操作 */
    private boolean enableDelete = false;

    /** 字符集编码 */
    private String characterEncoding = "UTF-8";

    /** 时区 */
    private String serverTimezone = "Asia/Shanghai";

    /**
     * 默认构造函数
     */
    public DorisConfig() {
        this.host = "doris-fe";
        this.port = DEFAULT_HTTP_PORT;
        this.queryPort = DEFAULT_QUERY_PORT;
        this.database = FlinkConfigConstants.DEFAULT_DORIS_DATABASE;
        this.username = FlinkConfigConstants.DEFAULT_DORIS_USERNAME;
        this.password = FlinkConfigConstants.DEFAULT_DORIS_PASSWORD;
        this.fenodes = FlinkConfigConstants.DEFAULT_DORIS_FENODES;
        
        // 设置默认的 Stream Load 属性
        initDefaultStreamLoadProperties();
    }

    /**
     * 初始化默认的 Stream Load 属性
     */
    private void initDefaultStreamLoadProperties() {
        streamLoadProperties.setProperty("format", "json");
        streamLoadProperties.setProperty("strip_outer_array", "true");
        streamLoadProperties.setProperty("max_filter_ratio", "0.1");
        streamLoadProperties.setProperty("timeout", "600");
    }

    /**
     * 从 ParameterTool 创建配置
     * 
     * @param parameterTool 参数工具
     * @return Doris 配置
     */
    public static DorisConfig fromParameterTool(ParameterTool parameterTool) {
        DorisConfig config = new DorisConfig();
        config.loadFromParameterTool(parameterTool);
        return config;
    }

    /**
     * 从 ParameterTool 加载配置
     * 
     * @param parameterTool 参数工具
     */
    public void loadFromParameterTool(ParameterTool parameterTool) {
        // 加载通用配置
        loadCommonConfig(parameterTool, "doris");

        // 加载 Doris 特定配置
        this.fenodes = parameterTool.get("doris.fenodes", this.fenodes);
        this.httpPort = parameterTool.getInt("doris.http.port", this.httpPort);
        this.queryPort = parameterTool.getInt("doris.query.port", this.queryPort);
        this.batchSize = parameterTool.getInt("doris.batch.size", this.batchSize);
        this.batchIntervalMs = parameterTool.getLong("doris.batch.interval.ms", this.batchIntervalMs);
        this.maxRetries = parameterTool.getInt("doris.max.retries", this.maxRetries);
        this.enableDelete = parameterTool.getBoolean("doris.enable.delete", this.enableDelete);
        this.characterEncoding = parameterTool.get("doris.character.encoding", this.characterEncoding);
        this.serverTimezone = parameterTool.get("doris.server.timezone", this.serverTimezone);

        // 加载 Stream Load 属性
        loadStreamLoadProperties(parameterTool);
    }

    /**
     * 加载 Stream Load 属性
     * 
     * @param parameterTool 参数工具
     */
    private void loadStreamLoadProperties(ParameterTool parameterTool) {
        // 从配置中加载 Stream Load 属性
        String format = parameterTool.get("doris.stream.load.format", "json");
        String stripOuterArray = parameterTool.get("doris.stream.load.strip.outer.array", "true");
        String maxFilterRatio = parameterTool.get("doris.stream.load.max.filter.ratio", "0.1");
        String timeout = parameterTool.get("doris.stream.load.timeout", "600");

        streamLoadProperties.setProperty("format", format);
        streamLoadProperties.setProperty("strip_outer_array", stripOuterArray);
        streamLoadProperties.setProperty("max_filter_ratio", maxFilterRatio);
        streamLoadProperties.setProperty("timeout", timeout);
    }

    @Override
    public String getConnectionUrl() {
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append("jdbc:mysql://")
                  .append(host)
                  .append(":")
                  .append(queryPort)
                  .append("/")
                  .append(database);

        // 添加连接参数
        urlBuilder.append("?useUnicode=true")
                  .append("&characterEncoding=").append(characterEncoding)
                  .append("&serverTimezone=").append(serverTimezone)
                  .append("&useSSL=").append(sslEnabled)
                  .append("&allowPublicKeyRetrieval=true")
                  .append("&connectTimeout=").append(connectionTimeout)
                  .append("&socketTimeout=").append(socketTimeout);

        return urlBuilder.toString();
    }

    @Override
    public String getDriverClassName() {
        return DRIVER_CLASS_NAME;
    }

    /**
     * 获取连接属性
     * 
     * @return 连接属性
     */
    public Properties getConnectionProperties() {
        Properties props = new Properties();
        props.setProperty("user", username);
        if (password != null) {
            props.setProperty("password", password);
        }
        props.setProperty("useSSL", String.valueOf(sslEnabled));
        props.setProperty("allowPublicKeyRetrieval", "true");
        props.setProperty("useUnicode", "true");
        props.setProperty("characterEncoding", characterEncoding);
        props.setProperty("serverTimezone", serverTimezone);
        props.setProperty("connectTimeout", String.valueOf(connectionTimeout));
        props.setProperty("socketTimeout", String.valueOf(socketTimeout));
        
        return props;
    }

    /**
     * 获取 HTTP 连接 URL
     * 
     * @return HTTP 连接 URL
     */
    public String getHttpUrl() {
        return String.format("http://%s:%d", host, httpPort);
    }

    /**
     * 获取 Stream Load URL
     * 
     * @param table 表名
     * @return Stream Load URL
     */
    public String getStreamLoadUrl(String table) {
        return String.format("http://%s:%d/api/%s/%s/_stream_load", host, httpPort, database, table);
    }

    /**
     * 验证 Doris 特定配置
     * 
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() &&
               fenodes != null && !fenodes.trim().isEmpty() &&
               httpPort > 0 && httpPort <= 65535 &&
               queryPort > 0 && queryPort <= 65535 &&
               batchSize > 0 &&
               batchIntervalMs > 0 &&
               maxRetries >= 0 &&
               characterEncoding != null && !characterEncoding.trim().isEmpty() &&
               serverTimezone != null && !serverTimezone.trim().isEmpty();
    }

    /**
     * 创建默认配置
     * 
     * @return 默认 Doris 配置
     */
    public static DorisConfig createDefault() {
        return new DorisConfig();
    }

    /**
     * 创建用于指定数据库的配置
     * 
     * @param database 数据库名
     * @return Doris 配置
     */
    public static DorisConfig forDatabase(String database) {
        DorisConfig config = new DorisConfig();
        config.setDatabase(database);
        return config;
    }

    /**
     * 添加 Stream Load 属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    public void addStreamLoadProperty(String key, String value) {
        streamLoadProperties.setProperty(key, value);
    }

    /**
     * 获取 Stream Load 属性的副本
     * 
     * @return Stream Load 属性副本
     */
    public Properties getStreamLoadPropertiesCopy() {
        Properties copy = new Properties();
        copy.putAll(streamLoadProperties);
        return copy;
    }

    @Override
    public String toString() {
        return String.format("DorisConfig{fenodes='%s', database='%s', username='%s', batchSize=%d, ssl=%s}",
                fenodes, database, username, batchSize, sslEnabled);
    }
}
