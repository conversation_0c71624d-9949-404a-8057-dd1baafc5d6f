package com.geeksec.flink.common.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 处理状态枚举
 * 定义数据处理过程中的各种状态
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum ProcessingStatus {
    
    /** 待处理 */
    PENDING(0, "PENDING", "待处理", "数据已接收，等待处理", "#1890FF"),
    
    /** 处理中 */
    PROCESSING(1, "PROCESSING", "处理中", "数据正在处理中", "#FAAD14"),
    
    /** 处理成功 */
    SUCCESS(2, "SUCCESS", "处理成功", "数据处理完成", "#52C41A"),
    
    /** 处理失败 */
    FAILED(3, "FAILED", "处理失败", "数据处理失败", "#F5222D"),
    
    /** 部分成功 */
    PARTIAL_SUCCESS(4, "PARTIAL_SUCCESS", "部分成功", "数据部分处理成功", "#FA8C16"),
    
    /** 已跳过 */
    SKIPPED(5, "SKIPPED", "已跳过", "数据被跳过处理", "#8C8C8C"),
    
    /** 已取消 */
    CANCELLED(6, "CANCELLED", "已取消", "数据处理被取消", "#722ED1"),
    
    /** 超时 */
    TIMEOUT(7, "TIMEOUT", "超时", "数据处理超时", "#EB2F96"),
    
    /** 重试中 */
    RETRYING(8, "RETRYING", "重试中", "数据处理重试中", "#13C2C2"),
    
    /** 已暂停 */
    PAUSED(9, "PAUSED", "已暂停", "数据处理已暂停", "#595959"),
    
    /** 已恢复 */
    RESUMED(10, "RESUMED", "已恢复", "数据处理已恢复", "#096DD9"),
    
    /** 等待依赖 */
    WAITING_DEPENDENCY(11, "WAITING_DEPENDENCY", "等待依赖", "等待依赖项完成", "#AD6800"),
    
    /** 验证中 */
    VALIDATING(12, "VALIDATING", "验证中", "数据验证中", "#531DAB"),
    
    /** 验证失败 */
    VALIDATION_FAILED(13, "VALIDATION_FAILED", "验证失败", "数据验证失败", "#C41D7F"),
    
    /** 已归档 */
    ARCHIVED(14, "ARCHIVED", "已归档", "数据已归档", "#434343");

    /** 状态代码 */
    private final int code;
    
    /** 状态名称 */
    private final String name;
    
    /** 显示名称 */
    private final String displayName;
    
    /** 描述信息 */
    private final String description;
    
    /** 颜色代码 */
    private final String colorCode;

    /** 代码映射 */
    private static final Map<Integer, ProcessingStatus> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ProcessingStatus::getCode, Function.identity()));

    /** 名称映射 */
    private static final Map<String, ProcessingStatus> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ProcessingStatus::getName, Function.identity()));

    /** 显示名称映射 */
    private static final Map<String, ProcessingStatus> DISPLAY_NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ProcessingStatus::getDisplayName, Function.identity()));

    /**
     * 构造函数
     * 
     * @param code 状态代码
     * @param name 状态名称
     * @param displayName 显示名称
     * @param description 描述信息
     * @param colorCode 颜色代码
     */
    ProcessingStatus(int code, String name, String displayName, String description, String colorCode) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
        this.description = description;
        this.colorCode = colorCode;
    }

    /**
     * 根据代码获取处理状态
     * 
     * @param code 状态代码
     * @return 处理状态，如果不存在则返回 null
     */
    public static ProcessingStatus fromCode(int code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据名称获取处理状态
     * 
     * @param name 状态名称
     * @return 处理状态，如果不存在则返回 null
     */
    public static ProcessingStatus fromName(String name) {
        if (name == null) {
            return null;
        }
        return NAME_MAP.get(name.toUpperCase());
    }

    /**
     * 根据显示名称获取处理状态
     * 
     * @param displayName 显示名称
     * @return 处理状态，如果不存在则返回 null
     */
    public static ProcessingStatus fromDisplayName(String displayName) {
        return DISPLAY_NAME_MAP.get(displayName);
    }

    /**
     * 根据字符串获取处理状态（支持多种格式）
     * 
     * @param value 字符串值
     * @return 处理状态，如果不存在则返回 PENDING
     */
    public static ProcessingStatus fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return PENDING;
        }

        // 尝试按名称匹配
        ProcessingStatus status = fromName(value);
        if (status != null) {
            return status;
        }

        // 尝试按显示名称匹配
        status = fromDisplayName(value);
        if (status != null) {
            return status;
        }

        // 尝试按代码匹配
        try {
            int code = Integer.parseInt(value);
            status = fromCode(code);
            if (status != null) {
                return status;
            }
        } catch (NumberFormatException ignored) {
            // 忽略数字解析异常
        }

        // 默认返回待处理
        return PENDING;
    }

    /**
     * 判断是否为终态状态
     * 
     * @return 是否为终态状态
     */
    public boolean isFinalState() {
        return this == SUCCESS || this == FAILED || this == CANCELLED || 
               this == SKIPPED || this == TIMEOUT || this == VALIDATION_FAILED || 
               this == ARCHIVED;
    }

    /**
     * 判断是否为成功状态
     * 
     * @return 是否为成功状态
     */
    public boolean isSuccessState() {
        return this == SUCCESS || this == PARTIAL_SUCCESS;
    }

    /**
     * 判断是否为失败状态
     * 
     * @return 是否为失败状态
     */
    public boolean isFailureState() {
        return this == FAILED || this == TIMEOUT || this == VALIDATION_FAILED;
    }

    /**
     * 判断是否为进行中状态
     * 
     * @return 是否为进行中状态
     */
    public boolean isInProgressState() {
        return this == PROCESSING || this == RETRYING || this == VALIDATING;
    }

    /**
     * 判断是否为等待状态
     * 
     * @return 是否为等待状态
     */
    public boolean isWaitingState() {
        return this == PENDING || this == WAITING_DEPENDENCY || this == PAUSED;
    }

    /**
     * 判断是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED || this == TIMEOUT || this == VALIDATION_FAILED;
    }

    /**
     * 判断是否可以取消
     * 
     * @return 是否可以取消
     */
    public boolean canCancel() {
        return !isFinalState() && this != CANCELLED;
    }

    /**
     * 判断是否可以暂停
     * 
     * @return 是否可以暂停
     */
    public boolean canPause() {
        return isInProgressState() || this == PENDING || this == WAITING_DEPENDENCY;
    }

    /**
     * 判断是否可以恢复
     * 
     * @return 是否可以恢复
     */
    public boolean canResume() {
        return this == PAUSED;
    }

    /**
     * 获取下一个可能的状态
     * 
     * @return 下一个可能的状态数组
     */
    public ProcessingStatus[] getNextPossibleStates() {
        switch (this) {
            case PENDING:
                return new ProcessingStatus[]{PROCESSING, CANCELLED, PAUSED, VALIDATING};
            case PROCESSING:
                return new ProcessingStatus[]{SUCCESS, FAILED, PARTIAL_SUCCESS, CANCELLED, PAUSED, TIMEOUT};
            case VALIDATING:
                return new ProcessingStatus[]{PROCESSING, VALIDATION_FAILED, CANCELLED, PAUSED};
            case FAILED:
                return new ProcessingStatus[]{RETRYING, CANCELLED, ARCHIVED};
            case TIMEOUT:
                return new ProcessingStatus[]{RETRYING, CANCELLED, ARCHIVED};
            case VALIDATION_FAILED:
                return new ProcessingStatus[]{RETRYING, CANCELLED, ARCHIVED};
            case RETRYING:
                return new ProcessingStatus[]{PROCESSING, FAILED, CANCELLED, TIMEOUT};
            case PAUSED:
                return new ProcessingStatus[]{RESUMED, CANCELLED};
            case RESUMED:
                return new ProcessingStatus[]{PROCESSING, CANCELLED};
            case WAITING_DEPENDENCY:
                return new ProcessingStatus[]{PROCESSING, CANCELLED, PAUSED, TIMEOUT};
            case SUCCESS:
            case PARTIAL_SUCCESS:
                return new ProcessingStatus[]{ARCHIVED};
            case CANCELLED:
            case SKIPPED:
            case ARCHIVED:
            default:
                return new ProcessingStatus[0]; // 终态，无后续状态
        }
    }

    @Override
    public String toString() {
        return displayName;
    }
}
