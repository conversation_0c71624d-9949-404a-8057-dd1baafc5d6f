package com.geeksec.flink.common.database.config;

import com.geeksec.flink.common.constants.FlinkConfigConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.flink.api.java.utils.ParameterTool;

/**
 * PostgreSQL 数据库配置类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PostgreSQLConfig extends DatabaseConfig {

    private static final long serialVersionUID = 1L;

    /** 默认端口 */
    private static final int DEFAULT_PORT = 5432;

    /** 默认驱动类名 */
    private static final String DRIVER_CLASS_NAME = "org.postgresql.Driver";

    /** 应用名称 */
    private String applicationName = "flink-job";

    /** 是否启用预处理语句缓存 */
    private boolean prepareThreshold = true;

    /** 预处理语句缓存大小 */
    private int preparedStatementCacheQueries = 256;

    /** 预处理语句缓存SQL大小限制 */
    private int preparedStatementCacheSizeMiB = 5;

    /** 是否启用自动重连 */
    private boolean autoReconnect = true;

    /** 字符集编码 */
    private String characterEncoding = "UTF-8";

    /** 时区 */
    private String serverTimezone = "Asia/Shanghai";

    /**
     * 默认构造函数
     */
    public PostgreSQLConfig() {
        this.host = FlinkConfigConstants.DEFAULT_POSTGRESQL_HOST;
        this.port = FlinkConfigConstants.DEFAULT_POSTGRESQL_PORT;
        this.database = FlinkConfigConstants.DEFAULT_POSTGRESQL_DATABASE;
        this.username = FlinkConfigConstants.DEFAULT_POSTGRESQL_USERNAME;
    }

    /**
     * 从 ParameterTool 创建配置
     * 
     * @param parameterTool 参数工具
     * @return PostgreSQL 配置
     */
    public static PostgreSQLConfig fromParameterTool(ParameterTool parameterTool) {
        PostgreSQLConfig config = new PostgreSQLConfig();
        config.loadFromParameterTool(parameterTool);
        return config;
    }

    /**
     * 从 ParameterTool 加载配置
     * 
     * @param parameterTool 参数工具
     */
    public void loadFromParameterTool(ParameterTool parameterTool) {
        // 加载通用配置
        loadCommonConfig(parameterTool, "postgresql");

        // 加载 PostgreSQL 特定配置
        this.applicationName = parameterTool.get("postgresql.application.name", this.applicationName);
        this.prepareThreshold = parameterTool.getBoolean("postgresql.prepare.threshold", this.prepareThreshold);
        this.preparedStatementCacheQueries = parameterTool.getInt("postgresql.prepared.statement.cache.queries", this.preparedStatementCacheQueries);
        this.preparedStatementCacheSizeMiB = parameterTool.getInt("postgresql.prepared.statement.cache.size.mib", this.preparedStatementCacheSizeMiB);
        this.autoReconnect = parameterTool.getBoolean("postgresql.auto.reconnect", this.autoReconnect);
        this.characterEncoding = parameterTool.get("postgresql.character.encoding", this.characterEncoding);
        this.serverTimezone = parameterTool.get("postgresql.server.timezone", this.serverTimezone);
    }

    @Override
    public String getConnectionUrl() {
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append("jdbc:postgresql://")
                  .append(host)
                  .append(":")
                  .append(port)
                  .append("/")
                  .append(database);

        // 添加连接参数
        urlBuilder.append("?useUnicode=true")
                  .append("&characterEncoding=").append(characterEncoding)
                  .append("&serverTimezone=").append(serverTimezone)
                  .append("&useSSL=").append(sslEnabled)
                  .append("&autoReconnect=").append(autoReconnect)
                  .append("&connectTimeout=").append(connectionTimeout)
                  .append("&socketTimeout=").append(socketTimeout);

        if (applicationName != null && !applicationName.trim().isEmpty()) {
            urlBuilder.append("&ApplicationName=").append(applicationName);
        }

        if (prepareThreshold) {
            urlBuilder.append("&prepareThreshold=1");
        }

        urlBuilder.append("&preparedStatementCacheQueries=").append(preparedStatementCacheQueries)
                  .append("&preparedStatementCacheSizeMiB=").append(preparedStatementCacheSizeMiB);

        return urlBuilder.toString();
    }

    @Override
    public String getDriverClassName() {
        return DRIVER_CLASS_NAME;
    }

    /**
     * 获取连接属性
     * 
     * @return 连接属性
     */
    public java.util.Properties getConnectionProperties() {
        java.util.Properties props = new java.util.Properties();
        props.setProperty("user", username);
        if (password != null) {
            props.setProperty("password", password);
        }
        props.setProperty("ssl", String.valueOf(sslEnabled));
        props.setProperty("connectTimeout", String.valueOf(connectionTimeout / 1000)); // PostgreSQL expects seconds
        props.setProperty("socketTimeout", String.valueOf(socketTimeout / 1000)); // PostgreSQL expects seconds
        
        if (applicationName != null && !applicationName.trim().isEmpty()) {
            props.setProperty("ApplicationName", applicationName);
        }
        
        if (prepareThreshold) {
            props.setProperty("prepareThreshold", "1");
        }
        
        props.setProperty("preparedStatementCacheQueries", String.valueOf(preparedStatementCacheQueries));
        props.setProperty("preparedStatementCacheSizeMiB", String.valueOf(preparedStatementCacheSizeMiB));
        
        return props;
    }

    /**
     * 验证 PostgreSQL 特定配置
     * 
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() &&
               characterEncoding != null && !characterEncoding.trim().isEmpty() &&
               serverTimezone != null && !serverTimezone.trim().isEmpty() &&
               preparedStatementCacheQueries > 0 &&
               preparedStatementCacheSizeMiB > 0;
    }

    /**
     * 创建默认配置
     * 
     * @return 默认 PostgreSQL 配置
     */
    public static PostgreSQLConfig createDefault() {
        return new PostgreSQLConfig();
    }

    /**
     * 创建用于指定数据库的配置
     * 
     * @param database 数据库名
     * @return PostgreSQL 配置
     */
    public static PostgreSQLConfig forDatabase(String database) {
        PostgreSQLConfig config = new PostgreSQLConfig();
        config.setDatabase(database);
        return config;
    }

    @Override
    public String toString() {
        return String.format("PostgreSQLConfig{host='%s', port=%d, database='%s', username='%s', ssl=%s}",
                host, port, database, username, sslEnabled);
    }
}
