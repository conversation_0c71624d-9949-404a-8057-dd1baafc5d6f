package com.geeksec.flink.common.database.provider;

import com.geeksec.flink.common.database.ConnectionProvider;
import com.geeksec.flink.common.database.FlinkDatabaseConnectionManager;
import com.geeksec.flink.common.database.config.DorisConfig;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * Doris 连接提供者实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class DorisConnectionProvider implements ConnectionProvider<Connection> {

    private final DorisConfig config;
    private final FlinkDatabaseConnectionManager connectionManager;

    /**
     * 构造函数
     * 
     * @param config Doris 配置
     */
    public DorisConnectionProvider(DorisConfig config) {
        this.config = config;
        this.connectionManager = FlinkDatabaseConnectionManager.getInstance();
        log.info("Doris 连接提供者已初始化: {}", config);
    }

    @Override
    public Connection getConnection() throws SQLException {
        try {
            Connection connection = connectionManager.getDorisConnection(config);
            log.debug("获取 Doris 连接成功: {}", config.getFenodes());
            return connection;
        } catch (SQLException e) {
            log.error("获取 Doris 连接失败: {}", config, e);
            throw e;
        }
    }

    @Override
    public void releaseConnection(Connection connection) {
        if (connection != null) {
            try {
                if (!connection.isClosed()) {
                    connection.close();
                    log.debug("Doris 连接已释放");
                }
            } catch (SQLException e) {
                log.warn("释放 Doris 连接时发生错误", e);
            }
        }
    }

    @Override
    public boolean testConnection() {
        return connectionManager.testDorisConnection(config);
    }

    @Override
    public void close() throws Exception {
        log.info("Doris 连接提供者已关闭");
        // Doris 连接提供者不需要特殊的关闭操作
        // 连接池由 FlinkDatabaseConnectionManager 统一管理
    }

    /**
     * 获取配置信息
     * 
     * @return Doris 配置
     */
    public DorisConfig getConfig() {
        return config;
    }

    /**
     * 执行查询并返回结果
     * 
     * @param sql SQL 查询语句
     * @param resultHandler 结果处理器
     * @param <T> 结果类型
     * @return 查询结果
     * @throws SQLException SQL 异常
     */
    public <T> T executeQuery(String sql, ResultHandler<T> resultHandler) throws SQLException {
        try (Connection connection = getConnection();
             var statement = connection.createStatement();
             var resultSet = statement.executeQuery(sql)) {
            
            return resultHandler.handle(resultSet);
        }
    }

    /**
     * 执行更新操作
     * 
     * @param sql SQL 更新语句
     * @return 影响的行数
     * @throws SQLException SQL 异常
     */
    public int executeUpdate(String sql) throws SQLException {
        try (Connection connection = getConnection();
             var statement = connection.createStatement()) {
            
            return statement.executeUpdate(sql);
        }
    }

    /**
     * 执行批量更新操作
     * 
     * @param sqls SQL 语句列表
     * @return 每个语句影响的行数
     * @throws SQLException SQL 异常
     */
    public int[] executeBatch(String[] sqls) throws SQLException {
        try (Connection connection = getConnection();
             var statement = connection.createStatement()) {
            
            connection.setAutoCommit(false);
            
            for (String sql : sqls) {
                statement.addBatch(sql);
            }
            
            int[] results = statement.executeBatch();
            connection.commit();
            
            return results;
        }
    }

    /**
     * 获取数据库版本信息
     * 
     * @return 版本信息
     * @throws SQLException SQL 异常
     */
    public String getVersion() throws SQLException {
        return executeQuery("SELECT VERSION()", resultSet -> {
            if (resultSet.next()) {
                return resultSet.getString(1);
            }
            return "Unknown";
        });
    }

    /**
     * 获取数据库列表
     * 
     * @return 数据库列表
     * @throws SQLException SQL 异常
     */
    public java.util.List<String> getDatabases() throws SQLException {
        return executeQuery("SHOW DATABASES", resultSet -> {
            java.util.List<String> databases = new java.util.ArrayList<>();
            while (resultSet.next()) {
                databases.add(resultSet.getString(1));
            }
            return databases;
        });
    }

    /**
     * 获取表列表
     * 
     * @param database 数据库名
     * @return 表列表
     * @throws SQLException SQL 异常
     */
    public java.util.List<String> getTables(String database) throws SQLException {
        String sql = String.format("SHOW TABLES FROM %s", database);
        return executeQuery(sql, resultSet -> {
            java.util.List<String> tables = new java.util.ArrayList<>();
            while (resultSet.next()) {
                tables.add(resultSet.getString(1));
            }
            return tables;
        });
    }

    /**
     * 结果处理器接口
     * 
     * @param <T> 结果类型
     */
    @FunctionalInterface
    public interface ResultHandler<T> {
        /**
         * 处理结果集
         * 
         * @param resultSet 结果集
         * @return 处理结果
         * @throws SQLException SQL 异常
         */
        T handle(java.sql.ResultSet resultSet) throws SQLException;
    }

    @Override
    public String toString() {
        return String.format("DorisConnectionProvider{config=%s}", config);
    }
}
