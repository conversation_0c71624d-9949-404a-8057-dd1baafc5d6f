package com.geeksec.flink.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 字符串工具类
 * 提供 Flink 作业中常用的字符串处理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class StringUtils {

    private StringUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 常用正则表达式 ====================

    /** IP 地址正则表达式 */
    private static final Pattern IP_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

    /** 域名正则表达式 */
    private static final Pattern DOMAIN_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?(\\.([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?))*$");

    /** 邮箱正则表达式 */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    /** 数字正则表达式 */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");

    /** 整数正则表达式 */
    private static final Pattern INTEGER_PATTERN = Pattern.compile("^-?\\d+$");

    // ==================== 空值检查 ====================

    /**
     * 检查字符串是否为空或 null
     * 
     * @param str 字符串
     * @return 是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }

    /**
     * 检查字符串是否不为空且不为 null
     * 
     * @param str 字符串
     * @return 是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 检查字符串是否为空白（null、空字符串或只包含空白字符）
     * 
     * @param str 字符串
     * @return 是否为空白
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查字符串是否不为空白
     * 
     * @param str 字符串
     * @return 是否不为空白
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 检查对象是否为空或 null
     * 
     * @param obj 对象
     * @return 是否为空
     */
    public static boolean isNullOrEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof String) {
            return isEmpty((String) obj);
        }
        if (obj instanceof Collection) {
            return ((Collection<?>) obj).isEmpty();
        }
        if (obj instanceof Map) {
            return ((Map<?, ?>) obj).isEmpty();
        }
        return false;
    }

    // ==================== 字符串转换 ====================

    /**
     * 安全地将对象转换为字符串
     * 
     * @param obj 对象
     * @return 字符串表示，null 对象返回空字符串
     */
    public static String toString(Object obj) {
        return obj != null ? obj.toString() : "";
    }

    /**
     * 安全地将对象转换为字符串
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 字符串表示，null 对象返回默认值
     */
    public static String toString(Object obj, String defaultValue) {
        return obj != null ? obj.toString() : defaultValue;
    }

    /**
     * 首字母大写
     * 
     * @param str 字符串
     * @return 首字母大写的字符串
     */
    public static String capitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * 首字母小写
     * 
     * @param str 字符串
     * @return 首字母小写的字符串
     */
    public static String uncapitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }

    /**
     * 驼峰命名转下划线命名
     * 
     * @param camelCase 驼峰命名字符串
     * @return 下划线命名字符串
     */
    public static String camelToSnake(String camelCase) {
        if (isEmpty(camelCase)) {
            return camelCase;
        }
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 下划线命名转驼峰命名
     * 
     * @param snakeCase 下划线命名字符串
     * @return 驼峰命名字符串
     */
    public static String snakeToCamel(String snakeCase) {
        if (isEmpty(snakeCase)) {
            return snakeCase;
        }
        String[] parts = snakeCase.split("_");
        StringBuilder result = new StringBuilder(parts[0].toLowerCase());
        for (int i = 1; i < parts.length; i++) {
            result.append(capitalize(parts[i].toLowerCase()));
        }
        return result.toString();
    }

    // ==================== 字符串操作 ====================

    /**
     * 重复字符串指定次数
     * 
     * @param str 字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    public static String repeat(String str, int count) {
        if (str == null || count <= 0) {
            return "";
        }
        return str.repeat(count);
    }

    /**
     * 左填充字符串到指定长度
     * 
     * @param str 原字符串
     * @param length 目标长度
     * @param padChar 填充字符
     * @return 填充后的字符串
     */
    public static String leftPad(String str, int length, char padChar) {
        if (str == null) {
            str = "";
        }
        if (str.length() >= length) {
            return str;
        }
        return repeat(String.valueOf(padChar), length - str.length()) + str;
    }

    /**
     * 右填充字符串到指定长度
     * 
     * @param str 原字符串
     * @param length 目标长度
     * @param padChar 填充字符
     * @return 填充后的字符串
     */
    public static String rightPad(String str, int length, char padChar) {
        if (str == null) {
            str = "";
        }
        if (str.length() >= length) {
            return str;
        }
        return str + repeat(String.valueOf(padChar), length - str.length());
    }

    /**
     * 截断字符串到指定长度
     * 
     * @param str 原字符串
     * @param maxLength 最大长度
     * @return 截断后的字符串
     */
    public static String truncate(String str, int maxLength) {
        if (str == null || str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength);
    }

    /**
     * 截断字符串到指定长度并添加省略号
     * 
     * @param str 原字符串
     * @param maxLength 最大长度
     * @return 截断后的字符串
     */
    public static String truncateWithEllipsis(String str, int maxLength) {
        if (str == null || str.length() <= maxLength) {
            return str;
        }
        if (maxLength <= 3) {
            return truncate(str, maxLength);
        }
        return str.substring(0, maxLength - 3) + "...";
    }

    // ==================== 字符串分割和连接 ====================

    /**
     * 安全地分割字符串
     * 
     * @param str 字符串
     * @param delimiter 分隔符
     * @return 分割后的字符串数组，空字符串返回空数组
     */
    public static String[] split(String str, String delimiter) {
        if (isEmpty(str)) {
            return new String[0];
        }
        return str.split(Pattern.quote(delimiter));
    }

    /**
     * 连接字符串数组
     * 
     * @param array 字符串数组
     * @param delimiter 分隔符
     * @return 连接后的字符串
     */
    public static String join(String[] array, String delimiter) {
        if (array == null || array.length == 0) {
            return "";
        }
        return String.join(delimiter, array);
    }

    /**
     * 连接字符串集合
     * 
     * @param collection 字符串集合
     * @param delimiter 分隔符
     * @return 连接后的字符串
     */
    public static String join(Collection<String> collection, String delimiter) {
        if (collection == null || collection.isEmpty()) {
            return "";
        }
        return String.join(delimiter, collection);
    }

    // ==================== 格式验证 ====================

    /**
     * 验证是否为有效的 IP 地址
     * 
     * @param ip IP 地址字符串
     * @return 是否为有效 IP
     */
    public static boolean isValidIp(String ip) {
        return isNotBlank(ip) && IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 验证是否为有效的域名
     * 
     * @param domain 域名字符串
     * @return 是否为有效域名
     */
    public static boolean isValidDomain(String domain) {
        return isNotBlank(domain) && DOMAIN_PATTERN.matcher(domain).matches();
    }

    /**
     * 验证是否为有效的邮箱地址
     * 
     * @param email 邮箱地址字符串
     * @return 是否为有效邮箱
     */
    public static boolean isValidEmail(String email) {
        return isNotBlank(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证是否为数字
     * 
     * @param str 字符串
     * @return 是否为数字
     */
    public static boolean isNumber(String str) {
        return isNotBlank(str) && NUMBER_PATTERN.matcher(str).matches();
    }

    /**
     * 验证是否为整数
     * 
     * @param str 字符串
     * @return 是否为整数
     */
    public static boolean isInteger(String str) {
        return isNotBlank(str) && INTEGER_PATTERN.matcher(str).matches();
    }

    // ==================== 哈希和编码 ====================

    /**
     * 计算字符串的 MD5 哈希值
     * 
     * @param str 字符串
     * @return MD5 哈希值
     */
    public static String md5(String str) {
        if (str == null) {
            return null;
        }
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(str.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5 算法不可用", e);
            return null;
        }
    }

    /**
     * 计算字符串的 SHA-256 哈希值
     * 
     * @param str 字符串
     * @return SHA-256 哈希值
     */
    public static String sha256(String str) {
        if (str == null) {
            return null;
        }
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(str.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256 算法不可用", e);
            return null;
        }
    }

    /**
     * 字节数组转十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    // ==================== 字符串清理 ====================

    /**
     * 移除字符串中的所有空白字符
     * 
     * @param str 字符串
     * @return 移除空白字符后的字符串
     */
    public static String removeWhitespace(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("\\s+", "");
    }

    /**
     * 标准化空白字符（将多个连续空白字符替换为单个空格）
     * 
     * @param str 字符串
     * @return 标准化后的字符串
     */
    public static String normalizeWhitespace(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("\\s+", " ").trim();
    }

    /**
     * 移除字符串中的特殊字符，只保留字母、数字和指定字符
     * 
     * @param str 字符串
     * @param keepChars 要保留的特殊字符
     * @return 清理后的字符串
     */
    public static String removeSpecialChars(String str, String keepChars) {
        if (str == null) {
            return null;
        }
        String pattern = "[^a-zA-Z0-9" + Pattern.quote(keepChars) + "]";
        return str.replaceAll(pattern, "");
    }

    // ==================== 便捷方法 ====================

    /**
     * 生成随机字符串
     * 
     * @param length 长度
     * @return 随机字符串
     */
    public static String randomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        return random.ints(length, 0, chars.length())
                .mapToObj(chars::charAt)
                .map(Object::toString)
                .collect(Collectors.joining());
    }

    /**
     * 生成唯一标识符（基于时间戳和随机数）
     * 
     * @return 唯一标识符
     */
    public static String generateId() {
        return System.currentTimeMillis() + "_" + randomString(8);
    }

    /**
     * 安全地比较两个字符串（处理 null 值）
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 是否相等
     */
    public static boolean equals(String str1, String str2) {
        return Objects.equals(str1, str2);
    }

    /**
     * 安全地比较两个字符串（忽略大小写，处理 null 值）
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 是否相等
     */
    public static boolean equalsIgnoreCase(String str1, String str2) {
        if (str1 == null && str2 == null) {
            return true;
        }
        if (str1 == null || str2 == null) {
            return false;
        }
        return str1.equalsIgnoreCase(str2);
    }
}
