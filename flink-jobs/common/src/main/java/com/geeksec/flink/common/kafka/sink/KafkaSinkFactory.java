package com.geeksec.flink.common.kafka.sink;

import com.geeksec.flink.common.kafka.config.KafkaConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.sink.KafkaSinkBuilder;

/**
 * Kafka Sink 工厂
 * 提供创建各种类型 Kafka Sink 的统一接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class KafkaSinkFactory {

    private KafkaSinkFactory() {
        // 工具类，禁止实例化
    }

    /**
     * 创建字符串类型的 Kafka Sink
     * 
     * @param config Kafka 配置
     * @param topic 主题名称
     * @return Kafka Sink
     */
    public static KafkaSink<String> createStringSink(KafkaConfig config, String topic) {
        return createStringSink(config, topic, DeliveryGuarantee.AT_LEAST_ONCE);
    }

    /**
     * 创建字符串类型的 Kafka Sink（指定投递保证）
     * 
     * @param config Kafka 配置
     * @param topic 主题名称
     * @param deliveryGuarantee 投递保证
     * @return Kafka Sink
     */
    public static KafkaSink<String> createStringSink(KafkaConfig config, String topic, 
                                                     DeliveryGuarantee deliveryGuarantee) {
        log.info("创建字符串类型 Kafka Sink: topic={}, deliveryGuarantee={}, config={}", 
                topic, deliveryGuarantee, config);
        
        validateConfig(config);
        validateTopic(topic);

        KafkaRecordSerializationSchema<String> recordSerializer = KafkaRecordSerializationSchema.builder()
                .setTopic(topic)
                .setValueSerializationSchema(new SimpleStringSchema())
                .build();

        return KafkaSink.<String>builder()
                .setBootstrapServers(config.getBootstrapServers())
                .setRecordSerializer(recordSerializer)
                .setDeliveryGuarantee(deliveryGuarantee)
                .setKafkaProducerConfig(config.getProducerProperties())
                .build();
    }

    /**
     * 创建自定义序列化器的 Kafka Sink
     * 
     * @param config Kafka 配置
     * @param topic 主题名称
     * @param valueSerializer 值序列化器
     * @param <T> 数据类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> createSink(KafkaConfig config, String topic, 
                                              SerializationSchema<T> valueSerializer) {
        return createSink(config, topic, valueSerializer, DeliveryGuarantee.AT_LEAST_ONCE);
    }

    /**
     * 创建自定义序列化器的 Kafka Sink（指定投递保证）
     * 
     * @param config Kafka 配置
     * @param topic 主题名称
     * @param valueSerializer 值序列化器
     * @param deliveryGuarantee 投递保证
     * @param <T> 数据类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> createSink(KafkaConfig config, String topic, 
                                              SerializationSchema<T> valueSerializer,
                                              DeliveryGuarantee deliveryGuarantee) {
        log.info("创建自定义序列化器 Kafka Sink: topic={}, serializer={}, deliveryGuarantee={}, config={}", 
                topic, valueSerializer.getClass().getSimpleName(), deliveryGuarantee, config);
        
        validateConfig(config);
        validateTopic(topic);

        KafkaRecordSerializationSchema<T> recordSerializer = KafkaRecordSerializationSchema.builder()
                .setTopic(topic)
                .setValueSerializationSchema(valueSerializer)
                .build();

        return KafkaSink.<T>builder()
                .setBootstrapServers(config.getBootstrapServers())
                .setRecordSerializer(recordSerializer)
                .setDeliveryGuarantee(deliveryGuarantee)
                .setKafkaProducerConfig(config.getProducerProperties())
                .build();
    }

    /**
     * 创建带键值序列化器的 Kafka Sink
     * 
     * @param config Kafka 配置
     * @param topic 主题名称
     * @param keySerializer 键序列化器
     * @param valueSerializer 值序列化器
     * @param <T> 数据类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> createSinkWithKey(KafkaConfig config, String topic,
                                                     SerializationSchema<T> keySerializer,
                                                     SerializationSchema<T> valueSerializer) {
        return createSinkWithKey(config, topic, keySerializer, valueSerializer, DeliveryGuarantee.AT_LEAST_ONCE);
    }

    /**
     * 创建带键值序列化器的 Kafka Sink（指定投递保证）
     * 
     * @param config Kafka 配置
     * @param topic 主题名称
     * @param keySerializer 键序列化器
     * @param valueSerializer 值序列化器
     * @param deliveryGuarantee 投递保证
     * @param <T> 数据类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> createSinkWithKey(KafkaConfig config, String topic,
                                                     SerializationSchema<T> keySerializer,
                                                     SerializationSchema<T> valueSerializer,
                                                     DeliveryGuarantee deliveryGuarantee) {
        log.info("创建带键值序列化器 Kafka Sink: topic={}, keySerializer={}, valueSerializer={}, deliveryGuarantee={}, config={}", 
                topic, keySerializer.getClass().getSimpleName(), valueSerializer.getClass().getSimpleName(), 
                deliveryGuarantee, config);
        
        validateConfig(config);
        validateTopic(topic);

        KafkaRecordSerializationSchema<T> recordSerializer = KafkaRecordSerializationSchema.builder()
                .setTopic(topic)
                .setKeySerializationSchema(keySerializer)
                .setValueSerializationSchema(valueSerializer)
                .build();

        return KafkaSink.<T>builder()
                .setBootstrapServers(config.getBootstrapServers())
                .setRecordSerializer(recordSerializer)
                .setDeliveryGuarantee(deliveryGuarantee)
                .setKafkaProducerConfig(config.getProducerProperties())
                .build();
    }

    /**
     * 创建自定义记录序列化器的 Kafka Sink
     * 
     * @param config Kafka 配置
     * @param recordSerializer 记录序列化器
     * @param <T> 数据类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> createSinkWithRecordSerializer(
            KafkaConfig config, KafkaRecordSerializationSchema<T> recordSerializer) {
        return createSinkWithRecordSerializer(config, recordSerializer, DeliveryGuarantee.AT_LEAST_ONCE);
    }

    /**
     * 创建自定义记录序列化器的 Kafka Sink（指定投递保证）
     * 
     * @param config Kafka 配置
     * @param recordSerializer 记录序列化器
     * @param deliveryGuarantee 投递保证
     * @param <T> 数据类型
     * @return Kafka Sink
     */
    public static <T> KafkaSink<T> createSinkWithRecordSerializer(
            KafkaConfig config, KafkaRecordSerializationSchema<T> recordSerializer,
            DeliveryGuarantee deliveryGuarantee) {
        log.info("创建自定义记录序列化器 Kafka Sink: recordSerializer={}, deliveryGuarantee={}, config={}", 
                recordSerializer.getClass().getSimpleName(), deliveryGuarantee, config);
        
        validateConfig(config);

        return KafkaSink.<T>builder()
                .setBootstrapServers(config.getBootstrapServers())
                .setRecordSerializer(recordSerializer)
                .setDeliveryGuarantee(deliveryGuarantee)
                .setKafkaProducerConfig(config.getProducerProperties())
                .build();
    }

    /**
     * 创建字节数组类型的 Kafka Sink
     * 
     * @param config Kafka 配置
     * @param topic 主题名称
     * @return Kafka Sink
     */
    public static KafkaSink<byte[]> createByteArraySink(KafkaConfig config, String topic) {
        return createByteArraySink(config, topic, DeliveryGuarantee.AT_LEAST_ONCE);
    }

    /**
     * 创建字节数组类型的 Kafka Sink（指定投递保证）
     * 
     * @param config Kafka 配置
     * @param topic 主题名称
     * @param deliveryGuarantee 投递保证
     * @return Kafka Sink
     */
    public static KafkaSink<byte[]> createByteArraySink(KafkaConfig config, String topic, 
                                                        DeliveryGuarantee deliveryGuarantee) {
        log.info("创建字节数组类型 Kafka Sink: topic={}, deliveryGuarantee={}, config={}", 
                topic, deliveryGuarantee, config);
        
        validateConfig(config);
        validateTopic(topic);

        KafkaRecordSerializationSchema<byte[]> recordSerializer = KafkaRecordSerializationSchema.builder()
                .setTopic(topic)
                .setValueSerializationSchema(new SerializationSchema<byte[]>() {
                    @Override
                    public byte[] serialize(byte[] element) {
                        return element; // 直接返回原始字节数据
                    }
                })
                .build();

        return KafkaSink.<byte[]>builder()
                .setBootstrapServers(config.getBootstrapServers())
                .setRecordSerializer(recordSerializer)
                .setDeliveryGuarantee(deliveryGuarantee)
                .setKafkaProducerConfig(config.getProducerProperties())
                .build();
    }

    /**
     * 创建带有自定义属性的 Kafka Sink 构建器
     * 
     * @param config Kafka 配置
     * @param <T> 数据类型
     * @return Kafka Sink 构建器
     */
    public static <T> KafkaSinkBuilder<T> createBuilder(KafkaConfig config) {
        validateConfig(config);

        return KafkaSink.<T>builder()
                .setBootstrapServers(config.getBootstrapServers())
                .setKafkaProducerConfig(config.getProducerProperties());
    }

    /**
     * 根据投递保证字符串获取投递保证枚举
     * 
     * @param guarantee 投递保证字符串
     * @return 投递保证枚举
     */
    public static DeliveryGuarantee getDeliveryGuarantee(String guarantee) {
        if (guarantee == null || guarantee.trim().isEmpty()) {
            return DeliveryGuarantee.AT_LEAST_ONCE;
        }

        switch (guarantee.toLowerCase()) {
            case "none":
                return DeliveryGuarantee.NONE;
            case "at_least_once":
            case "at-least-once":
                return DeliveryGuarantee.AT_LEAST_ONCE;
            case "exactly_once":
            case "exactly-once":
                return DeliveryGuarantee.EXACTLY_ONCE;
            default:
                log.warn("未知的投递保证: {}，使用默认保证 AT_LEAST_ONCE", guarantee);
                return DeliveryGuarantee.AT_LEAST_ONCE;
        }
    }

    /**
     * 验证 Kafka 配置
     * 
     * @param config Kafka 配置
     * @throws IllegalArgumentException 配置无效时抛出
     */
    private static void validateConfig(KafkaConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("Kafka 配置不能为空");
        }
        if (!config.isValid()) {
            throw new IllegalArgumentException("Kafka 配置无效: " + config);
        }
    }

    /**
     * 验证主题名称
     * 
     * @param topic 主题名称
     * @throws IllegalArgumentException 主题名称无效时抛出
     */
    private static void validateTopic(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            throw new IllegalArgumentException("Kafka 主题名称不能为空");
        }
    }
}
