package com.geeksec.flink.common.exception;

import lombok.Getter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Flink 作业异常基类
 * 用于 Flink 作业中的异常处理，提供丰富的错误信息和上下文
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public class FlinkJobException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /** 错误码枚举 */
    private final ErrorCode errorCode;

    /** 错误码字符串（兼容旧版本） */
    private final String errorCodeString;

    /** 格式化参数 */
    private final Object[] args;

    /** 异常发生时间 */
    private final LocalDateTime timestamp;

    /** 上下文信息 */
    private final Map<String, Object> context;

    /** 是否可重试 */
    private final boolean retryable;

    /** 建议的重试延迟（毫秒） */
    private final long suggestedRetryDelay;

    // ==================== 构造函数 ====================

    /**
     * 默认构造函数
     */
    public FlinkJobException() {
        super();
        this.errorCode = ErrorCode.UNKNOWN_ERROR;
        this.errorCodeString = errorCode.getCode();
        this.args = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 基于消息的构造函数
     *
     * @param message 异常消息
     */
    public FlinkJobException(String message) {
        super(message);
        this.errorCode = ErrorCode.UNKNOWN_ERROR;
        this.errorCodeString = errorCode.getCode();
        this.args = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 基于错误码的构造函数
     *
     * @param errorCode 错误码枚举
     */
    public FlinkJobException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.errorCodeString = errorCode.getCode();
        this.args = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 基于错误码和参数的构造函数
     *
     * @param errorCode 错误码枚举
     * @param args 格式化参数
     */
    public FlinkJobException(ErrorCode errorCode, Object... args) {
        super(errorCode.formatMessage(args));
        this.errorCode = errorCode;
        this.errorCodeString = errorCode.getCode();
        this.args = args != null ? args.clone() : null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 基于错误码和原因的构造函数
     *
     * @param errorCode 错误码枚举
     * @param cause 原因异常
     */
    public FlinkJobException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.errorCodeString = errorCode.getCode();
        this.args = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 基于错误码、参数和原因的构造函数
     *
     * @param errorCode 错误码枚举
     * @param cause 原因异常
     * @param args 格式化参数
     */
    public FlinkJobException(ErrorCode errorCode, Throwable cause, Object... args) {
        super(errorCode.formatMessage(args), cause);
        this.errorCode = errorCode;
        this.errorCodeString = errorCode.getCode();
        this.args = args != null ? args.clone() : null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    // ==================== 兼容旧版本的构造函数 ====================

    /**
     * 兼容旧版本：基于错误码字符串的构造函数
     *
     * @param errorCodeString 错误码字符串
     * @param message 异常消息
     */
    public FlinkJobException(String errorCodeString, String message) {
        super(message);
        this.errorCode = ErrorCode.fromCode(errorCodeString);
        this.errorCodeString = errorCodeString;
        this.args = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 兼容旧版本：基于错误码字符串和参数的构造函数
     *
     * @param errorCodeString 错误码字符串
     * @param message 异常消息
     * @param args 格式化参数
     */
    public FlinkJobException(String errorCodeString, String message, Object... args) {
        super(message);
        this.errorCode = ErrorCode.fromCode(errorCodeString);
        this.errorCodeString = errorCodeString;
        this.args = args != null ? args.clone() : null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 兼容旧版本：基于消息和原因的构造函数
     *
     * @param message 异常消息
     * @param cause 原因异常
     */
    public FlinkJobException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCode.UNKNOWN_ERROR;
        this.errorCodeString = errorCode.getCode();
        this.args = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 兼容旧版本：基于错误码字符串、消息和原因的构造函数
     *
     * @param errorCodeString 错误码字符串
     * @param message 异常消息
     * @param cause 原因异常
     */
    public FlinkJobException(String errorCodeString, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCode.fromCode(errorCodeString);
        this.errorCodeString = errorCodeString;
        this.args = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    /**
     * 基于原因的构造函数
     *
     * @param cause 原因异常
     */
    public FlinkJobException(Throwable cause) {
        super(cause);
        this.errorCode = ErrorCode.UNKNOWN_ERROR;
        this.errorCodeString = errorCode.getCode();
        this.args = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
        this.retryable = false;
        this.suggestedRetryDelay = 0L;
    }

    // ==================== 构建器模式 ====================

    /**
     * 创建异常构建器
     *
     * @param errorCode 错误码
     * @return 异常构建器
     */
    public static Builder builder(ErrorCode errorCode) {
        return new Builder(errorCode);
    }

    /**
     * 异常构建器
     */
    public static class Builder {
        private final ErrorCode errorCode;
        private Object[] args;
        private Throwable cause;
        private final Map<String, Object> context = new HashMap<>();
        private boolean retryable = false;
        private long suggestedRetryDelay = 0L;

        private Builder(ErrorCode errorCode) {
            this.errorCode = errorCode;
        }

        public Builder args(Object... args) {
            this.args = args;
            return this;
        }

        public Builder cause(Throwable cause) {
            this.cause = cause;
            return this;
        }

        public Builder context(String key, Object value) {
            this.context.put(key, value);
            return this;
        }

        public Builder context(Map<String, Object> context) {
            this.context.putAll(context);
            return this;
        }

        public Builder retryable(boolean retryable) {
            this.retryable = retryable;
            return this;
        }

        public Builder suggestedRetryDelay(long delayMillis) {
            this.suggestedRetryDelay = delayMillis;
            return this;
        }

        public FlinkJobException build() {
            return new FlinkJobException(this);
        }
    }

    /**
     * 基于构建器的私有构造函数
     *
     * @param builder 构建器
     */
    private FlinkJobException(Builder builder) {
        super(builder.errorCode.formatMessage(builder.args), builder.cause);
        this.errorCode = builder.errorCode;
        this.errorCodeString = builder.errorCode.getCode();
        this.args = builder.args != null ? builder.args.clone() : null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>(builder.context);
        this.retryable = builder.retryable;
        this.suggestedRetryDelay = builder.suggestedRetryDelay;
    }

    // ==================== 便捷方法 ====================

    /**
     * 获取参数副本
     *
     * @return 参数数组副本
     */
    public Object[] getArgs() {
        return args != null ? args.clone() : null;
    }

    /**
     * 获取上下文信息副本
     *
     * @return 上下文信息副本
     */
    public Map<String, Object> getContext() {
        return new HashMap<>(context);
    }

    /**
     * 获取上下文信息
     *
     * @param key 键
     * @return 值
     */
    public Object getContextValue(String key) {
        return context.get(key);
    }

    /**
     * 获取格式化的错误信息
     *
     * @return 格式化的错误信息
     */
    public String getFormattedMessage() {
        return errorCode.formatMessage(args);
    }

    /**
     * 获取完整的错误描述
     *
     * @return 完整的错误描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("[%s] %s", errorCode.getCode(), getFormattedMessage()));

        if (!context.isEmpty()) {
            sb.append(" | 上下文: ").append(context);
        }

        if (retryable) {
            sb.append(" | 可重试");
            if (suggestedRetryDelay > 0) {
                sb.append("(建议延迟: ").append(suggestedRetryDelay).append("ms)");
            }
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return getFullDescription();
    }
}
