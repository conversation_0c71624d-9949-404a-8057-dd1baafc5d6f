package com.geeksec.flink.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;

/**
 * 数据转换工具类
 * 提供 Flink 作业中常用的数据类型转换功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class ConvertUtils {

    private ConvertUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 基础类型转换 ====================

    /**
     * 安全地将对象转换为字符串
     * 
     * @param obj 对象
     * @return 字符串，null 返回 null
     */
    public static String toString(Object obj) {
        return obj != null ? obj.toString() : null;
    }

    /**
     * 安全地将对象转换为字符串
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 字符串，null 返回默认值
     */
    public static String toString(Object obj, String defaultValue) {
        return obj != null ? obj.toString() : defaultValue;
    }

    /**
     * 安全地将对象转换为整数
     * 
     * @param obj 对象
     * @return 整数，转换失败返回 null
     */
    public static Integer toInteger(Object obj) {
        return toInteger(obj, null);
    }

    /**
     * 安全地将对象转换为整数
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 整数，转换失败返回默认值
     */
    public static Integer toInteger(Object obj, Integer defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        if (obj instanceof Integer) {
            return (Integer) obj;
        }
        
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }
        
        try {
            return Integer.valueOf(obj.toString().trim());
        } catch (NumberFormatException e) {
            log.debug("转换为整数失败: {}", obj, e);
            return defaultValue;
        }
    }

    /**
     * 安全地将对象转换为长整数
     * 
     * @param obj 对象
     * @return 长整数，转换失败返回 null
     */
    public static Long toLong(Object obj) {
        return toLong(obj, null);
    }

    /**
     * 安全地将对象转换为长整数
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 长整数，转换失败返回默认值
     */
    public static Long toLong(Object obj, Long defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        if (obj instanceof Long) {
            return (Long) obj;
        }
        
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }
        
        try {
            return Long.valueOf(obj.toString().trim());
        } catch (NumberFormatException e) {
            log.debug("转换为长整数失败: {}", obj, e);
            return defaultValue;
        }
    }

    /**
     * 安全地将对象转换为双精度浮点数
     * 
     * @param obj 对象
     * @return 双精度浮点数，转换失败返回 null
     */
    public static Double toDouble(Object obj) {
        return toDouble(obj, null);
    }

    /**
     * 安全地将对象转换为双精度浮点数
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 双精度浮点数，转换失败返回默认值
     */
    public static Double toDouble(Object obj, Double defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        if (obj instanceof Double) {
            return (Double) obj;
        }
        
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        }
        
        try {
            return Double.valueOf(obj.toString().trim());
        } catch (NumberFormatException e) {
            log.debug("转换为双精度浮点数失败: {}", obj, e);
            return defaultValue;
        }
    }

    /**
     * 安全地将对象转换为布尔值
     * 
     * @param obj 对象
     * @return 布尔值，转换失败返回 null
     */
    public static Boolean toBoolean(Object obj) {
        return toBoolean(obj, null);
    }

    /**
     * 安全地将对象转换为布尔值
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 布尔值，转换失败返回默认值
     */
    public static Boolean toBoolean(Object obj, Boolean defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        
        String str = obj.toString().trim().toLowerCase();
        if ("true".equals(str) || "1".equals(str) || "yes".equals(str) || "on".equals(str)) {
            return true;
        } else if ("false".equals(str) || "0".equals(str) || "no".equals(str) || "off".equals(str)) {
            return false;
        }
        
        return defaultValue;
    }

    /**
     * 安全地将对象转换为 BigDecimal
     * 
     * @param obj 对象
     * @return BigDecimal，转换失败返回 null
     */
    public static BigDecimal toBigDecimal(Object obj) {
        return toBigDecimal(obj, null);
    }

    /**
     * 安全地将对象转换为 BigDecimal
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return BigDecimal，转换失败返回默认值
     */
    public static BigDecimal toBigDecimal(Object obj, BigDecimal defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        }
        
        if (obj instanceof Number) {
            return BigDecimal.valueOf(((Number) obj).doubleValue());
        }
        
        try {
            return new BigDecimal(obj.toString().trim());
        } catch (NumberFormatException e) {
            log.debug("转换为 BigDecimal 失败: {}", obj, e);
            return defaultValue;
        }
    }

    // ==================== 数组和集合转换 ====================

    /**
     * 将对象转换为字符串数组
     * 
     * @param obj 对象
     * @param delimiter 分隔符
     * @return 字符串数组
     */
    public static String[] toStringArray(Object obj, String delimiter) {
        if (obj == null) {
            return new String[0];
        }
        
        if (obj instanceof String[]) {
            return (String[]) obj;
        }
        
        if (obj instanceof Collection) {
            Collection<?> collection = (Collection<?>) obj;
            return collection.stream()
                    .map(ConvertUtils::toString)
                    .toArray(String[]::new);
        }
        
        String str = obj.toString();
        if (StringUtils.isBlank(str)) {
            return new String[0];
        }
        
        return str.split(delimiter);
    }

    /**
     * 将对象转换为整数数组
     * 
     * @param obj 对象
     * @param delimiter 分隔符
     * @return 整数数组
     */
    public static Integer[] toIntegerArray(Object obj, String delimiter) {
        String[] strArray = toStringArray(obj, delimiter);
        return Arrays.stream(strArray)
                .map(s -> toInteger(s, null))
                .filter(Objects::nonNull)
                .toArray(Integer[]::new);
    }

    /**
     * 将对象转换为 List
     * 
     * @param obj 对象
     * @param delimiter 分隔符（如果对象是字符串）
     * @param converter 转换函数
     * @param <T> 目标类型
     * @return List
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> toList(Object obj, String delimiter, Function<String, T> converter) {
        if (obj == null) {
            return new ArrayList<>();
        }
        
        if (obj instanceof List) {
            return (List<T>) obj;
        }
        
        if (obj instanceof Collection) {
            return new ArrayList<>((Collection<T>) obj);
        }
        
        String[] strArray = toStringArray(obj, delimiter);
        return Arrays.stream(strArray)
                .map(converter)
                .filter(Objects::nonNull)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    // ==================== Row 转换 ====================

    /**
     * 将 Map 转换为 Row
     * 
     * @param map 映射
     * @param fieldNames 字段名数组
     * @return Row 对象
     */
    public static Row mapToRow(Map<String, Object> map, String[] fieldNames) {
        if (map == null || fieldNames == null) {
            return null;
        }
        
        Row row = Row.withNames();
        for (String fieldName : fieldNames) {
            row.setField(fieldName, map.get(fieldName));
        }
        return row;
    }

    /**
     * 将 Row 转换为 Map
     * 
     * @param row Row 对象
     * @return 映射
     */
    public static Map<String, Object> rowToMap(Row row) {
        if (row == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> map = new HashMap<>();
        String[] fieldNames = row.getFieldNames(true);
        if (fieldNames != null) {
            for (String fieldName : fieldNames) {
                map.put(fieldName, row.getField(fieldName));
            }
        }
        return map;
    }

    /**
     * 从 Row 中安全地获取字段值
     * 
     * @param row Row 对象
     * @param fieldName 字段名
     * @param type 目标类型
     * @param <T> 类型参数
     * @return 字段值
     */
    @SuppressWarnings("unchecked")
    public static <T> T getFieldValue(Row row, String fieldName, Class<T> type) {
        if (row == null || StringUtils.isBlank(fieldName)) {
            return null;
        }
        
        try {
            Object value = row.getFieldAs(fieldName);
            if (value == null) {
                return null;
            }
            
            if (type.isAssignableFrom(value.getClass())) {
                return (T) value;
            }
            
            // 尝试类型转换
            if (type == String.class) {
                return (T) toString(value);
            } else if (type == Integer.class) {
                return (T) toInteger(value);
            } else if (type == Long.class) {
                return (T) toLong(value);
            } else if (type == Double.class) {
                return (T) toDouble(value);
            } else if (type == Boolean.class) {
                return (T) toBoolean(value);
            } else if (type == BigDecimal.class) {
                return (T) toBigDecimal(value);
            }
            
            return null;
        } catch (Exception e) {
            log.debug("获取字段值失败: fieldName={}, type={}", fieldName, type.getSimpleName(), e);
            return null;
        }
    }

    // ==================== 数值处理 ====================

    /**
     * 格式化数值为指定小数位数
     * 
     * @param number 数值
     * @param scale 小数位数
     * @return 格式化后的数值
     */
    public static BigDecimal formatNumber(Number number, int scale) {
        if (number == null) {
            return null;
        }
        
        BigDecimal decimal = toBigDecimal(number);
        return decimal != null ? decimal.setScale(scale, RoundingMode.HALF_UP) : null;
    }

    /**
     * 计算百分比
     * 
     * @param part 部分值
     * @param total 总值
     * @param scale 小数位数
     * @return 百分比
     */
    public static BigDecimal calculatePercentage(Number part, Number total, int scale) {
        if (part == null || total == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal partDecimal = toBigDecimal(part);
        BigDecimal totalDecimal = toBigDecimal(total);
        
        if (partDecimal == null || totalDecimal == null || totalDecimal.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        return partDecimal.divide(totalDecimal, scale + 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .setScale(scale, RoundingMode.HALF_UP);
    }

    // ==================== 单位转换 ====================

    /**
     * 字节数转换为人类可读格式
     * 
     * @param bytes 字节数
     * @return 人类可读格式
     */
    public static String formatBytes(long bytes) {
        if (bytes < 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB", "PB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }

    /**
     * 毫秒转换为人类可读的时间格式
     * 
     * @param millis 毫秒数
     * @return 人类可读格式
     */
    public static String formatDuration(long millis) {
        if (millis < 0) {
            return "0ms";
        }
        
        long seconds = millis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%dd %dh %dm %ds", days, hours % 24, minutes % 60, seconds % 60);
        } else if (hours > 0) {
            return String.format("%dh %dm %ds", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds % 60);
        } else if (seconds > 0) {
            return String.format("%ds", seconds);
        } else {
            return String.format("%dms", millis);
        }
    }

    // ==================== 便捷方法 ====================

    /**
     * 安全地执行转换操作
     * 
     * @param obj 对象
     * @param converter 转换函数
     * @param defaultValue 默认值
     * @param <T> 目标类型
     * @return 转换结果
     */
    public static <T> T safeConvert(Object obj, Function<Object, T> converter, T defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        try {
            T result = converter.apply(obj);
            return result != null ? result : defaultValue;
        } catch (Exception e) {
            log.debug("安全转换失败: {}", obj, e);
            return defaultValue;
        }
    }

    /**
     * 批量转换对象
     * 
     * @param objects 对象数组
     * @param converter 转换函数
     * @param <T> 目标类型
     * @return 转换结果列表
     */
    public static <T> List<T> batchConvert(Object[] objects, Function<Object, T> converter) {
        if (objects == null) {
            return new ArrayList<>();
        }
        
        return Arrays.stream(objects)
                .map(obj -> safeConvert(obj, converter, null))
                .filter(Objects::nonNull)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
}
