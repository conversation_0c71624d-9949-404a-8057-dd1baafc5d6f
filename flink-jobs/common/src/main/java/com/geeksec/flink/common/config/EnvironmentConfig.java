package com.geeksec.flink.common.config;

import com.geeksec.flink.common.utils.StringUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 环境配置管理类
 * 提供环境检测、配置加载和环境特定的配置管理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class EnvironmentConfig {

    private EnvironmentConfig() {
        // 工具类，禁止实例化
    }

    // ==================== 环境类型枚举 ====================

    /**
     * 环境类型枚举
     */
    @Getter
    public enum Environment {
        /** 开发环境 */
        DEVELOPMENT("dev", "development", "开发环境"),
        
        /** 测试环境 */
        TEST("test", "testing", "测试环境"),
        
        /** 预生产环境 */
        STAGING("staging", "stage", "预生产环境"),
        
        /** 生产环境 */
        PRODUCTION("prod", "production", "生产环境"),
        
        /** 本地环境 */
        LOCAL("local", "localhost", "本地环境"),
        
        /** 未知环境 */
        UNKNOWN("unknown", "unknown", "未知环境");

        /** 环境代码 */
        private final String code;
        
        /** 环境别名 */
        private final String alias;
        
        /** 环境描述 */
        private final String description;

        Environment(String code, String alias, String description) {
            this.code = code;
            this.alias = alias;
            this.description = description;
        }

        /**
         * 根据字符串获取环境类型
         * 
         * @param value 环境字符串
         * @return 环境类型
         */
        public static Environment fromString(String value) {
            if (StringUtils.isBlank(value)) {
                return UNKNOWN;
            }
            
            String lowerValue = value.toLowerCase().trim();
            
            return Arrays.stream(values())
                    .filter(env -> env.getCode().equals(lowerValue) || env.getAlias().equals(lowerValue))
                    .findFirst()
                    .orElse(UNKNOWN);
        }

        /**
         * 是否为生产环境
         * 
         * @return 是否为生产环境
         */
        public boolean isProduction() {
            return this == PRODUCTION;
        }

        /**
         * 是否为开发环境
         * 
         * @return 是否为开发环境
         */
        public boolean isDevelopment() {
            return this == DEVELOPMENT || this == LOCAL;
        }

        /**
         * 是否为测试环境
         * 
         * @return 是否为测试环境
         */
        public boolean isTest() {
            return this == TEST || this == STAGING;
        }
    }

    // ==================== 环境变量常量 ====================

    /** 环境变量：环境类型 */
    public static final String ENV_ENVIRONMENT = "ENVIRONMENT";
    
    /** 环境变量：Spring 配置文件 */
    public static final String ENV_SPRING_PROFILES_ACTIVE = "SPRING_PROFILES_ACTIVE";
    
    /** 环境变量：应用名称 */
    public static final String ENV_APPLICATION_NAME = "APPLICATION_NAME";
    
    /** 环境变量：应用版本 */
    public static final String ENV_APPLICATION_VERSION = "APPLICATION_VERSION";
    
    /** 环境变量：配置文件路径 */
    public static final String ENV_CONFIG_PATH = "CONFIG_PATH";
    
    /** 环境变量：日志级别 */
    public static final String ENV_LOG_LEVEL = "LOG_LEVEL";
    
    /** 环境变量：调试模式 */
    public static final String ENV_DEBUG_MODE = "DEBUG_MODE";
    
    /** 环境变量：主机名 */
    public static final String ENV_HOSTNAME = "HOSTNAME";
    
    /** 环境变量：Pod 名称（Kubernetes） */
    public static final String ENV_POD_NAME = "POD_NAME";
    
    /** 环境变量：命名空间（Kubernetes） */
    public static final String ENV_NAMESPACE = "NAMESPACE";

    // ==================== 当前环境信息 ====================

    /** 当前环境类型 */
    private static volatile Environment currentEnvironment;
    
    /** 环境信息缓存 */
    private static volatile Map<String, String> environmentInfo;

    /**
     * 获取当前环境类型
     * 
     * @return 当前环境类型
     */
    public static Environment getCurrentEnvironment() {
        if (currentEnvironment == null) {
            synchronized (EnvironmentConfig.class) {
                if (currentEnvironment == null) {
                    currentEnvironment = detectEnvironment();
                }
            }
        }
        return currentEnvironment;
    }

    /**
     * 设置当前环境类型（主要用于测试）
     * 
     * @param environment 环境类型
     */
    public static void setCurrentEnvironment(Environment environment) {
        currentEnvironment = environment;
        // 清空缓存，强制重新加载环境信息
        environmentInfo = null;
    }

    /**
     * 检测当前环境类型
     * 
     * @return 环境类型
     */
    private static Environment detectEnvironment() {
        // 1. 检查 ENVIRONMENT 环境变量
        String envVar = System.getenv(ENV_ENVIRONMENT);
        if (StringUtils.isNotBlank(envVar)) {
            Environment env = Environment.fromString(envVar);
            if (env != Environment.UNKNOWN) {
                log.info("通过环境变量 {} 检测到环境: {}", ENV_ENVIRONMENT, env.getDescription());
                return env;
            }
        }

        // 2. 检查 Spring Profiles
        String springProfiles = System.getenv(ENV_SPRING_PROFILES_ACTIVE);
        if (StringUtils.isBlank(springProfiles)) {
            springProfiles = System.getProperty("spring.profiles.active");
        }
        if (StringUtils.isNotBlank(springProfiles)) {
            Environment env = Environment.fromString(springProfiles);
            if (env != Environment.UNKNOWN) {
                log.info("通过 Spring Profiles 检测到环境: {}", env.getDescription());
                return env;
            }
        }

        // 3. 检查主机名模式
        String hostname = getHostname();
        if (StringUtils.isNotBlank(hostname)) {
            String lowerHostname = hostname.toLowerCase();
            if (lowerHostname.contains("prod") || lowerHostname.contains("production")) {
                log.info("通过主机名模式检测到生产环境: {}", hostname);
                return Environment.PRODUCTION;
            } else if (lowerHostname.contains("test") || lowerHostname.contains("testing")) {
                log.info("通过主机名模式检测到测试环境: {}", hostname);
                return Environment.TEST;
            } else if (lowerHostname.contains("dev") || lowerHostname.contains("development")) {
                log.info("通过主机名模式检测到开发环境: {}", hostname);
                return Environment.DEVELOPMENT;
            } else if (lowerHostname.contains("staging") || lowerHostname.contains("stage")) {
                log.info("通过主机名模式检测到预生产环境: {}", hostname);
                return Environment.STAGING;
            } else if (lowerHostname.contains("local") || lowerHostname.equals("localhost")) {
                log.info("通过主机名模式检测到本地环境: {}", hostname);
                return Environment.LOCAL;
            }
        }

        // 4. 默认为开发环境
        log.warn("无法检测环境类型，默认使用开发环境");
        return Environment.DEVELOPMENT;
    }

    /**
     * 获取环境信息
     * 
     * @return 环境信息映射
     */
    public static Map<String, String> getEnvironmentInfo() {
        if (environmentInfo == null) {
            synchronized (EnvironmentConfig.class) {
                if (environmentInfo == null) {
                    environmentInfo = collectEnvironmentInfo();
                }
            }
        }
        return new HashMap<>(environmentInfo);
    }

    /**
     * 收集环境信息
     * 
     * @return 环境信息映射
     */
    private static Map<String, String> collectEnvironmentInfo() {
        Map<String, String> info = new HashMap<>();
        
        // 基本环境信息
        info.put("environment", getCurrentEnvironment().getCode());
        info.put("environment.description", getCurrentEnvironment().getDescription());
        info.put("hostname", getHostname());
        info.put("application.name", getApplicationName());
        info.put("application.version", getApplicationVersion());
        
        // Java 运行时信息
        info.put("java.version", System.getProperty("java.version"));
        info.put("java.vendor", System.getProperty("java.vendor"));
        info.put("java.home", System.getProperty("java.home"));
        
        // 操作系统信息
        info.put("os.name", System.getProperty("os.name"));
        info.put("os.version", System.getProperty("os.version"));
        info.put("os.arch", System.getProperty("os.arch"));
        
        // 用户信息
        info.put("user.name", System.getProperty("user.name"));
        info.put("user.dir", System.getProperty("user.dir"));
        info.put("user.home", System.getProperty("user.home"));
        
        // Kubernetes 信息（如果存在）
        Optional.ofNullable(System.getenv(ENV_POD_NAME))
                .ifPresent(value -> info.put("kubernetes.pod.name", value));
        Optional.ofNullable(System.getenv(ENV_NAMESPACE))
                .ifPresent(value -> info.put("kubernetes.namespace", value));
        
        // 调试信息
        info.put("debug.mode", String.valueOf(isDebugMode()));
        info.put("log.level", getLogLevel());
        
        return info;
    }

    // ==================== 环境特定配置 ====================

    /**
     * 获取主机名
     * 
     * @return 主机名
     */
    public static String getHostname() {
        // 优先使用环境变量
        String hostname = System.getenv(ENV_HOSTNAME);
        if (StringUtils.isNotBlank(hostname)) {
            return hostname;
        }
        
        // 尝试使用系统属性
        hostname = System.getProperty("hostname");
        if (StringUtils.isNotBlank(hostname)) {
            return hostname;
        }
        
        // 尝试使用 InetAddress
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            log.debug("获取主机名失败", e);
            return "unknown";
        }
    }

    /**
     * 获取应用名称
     * 
     * @return 应用名称
     */
    public static String getApplicationName() {
        String appName = System.getenv(ENV_APPLICATION_NAME);
        if (StringUtils.isNotBlank(appName)) {
            return appName;
        }
        
        appName = System.getProperty("application.name");
        if (StringUtils.isNotBlank(appName)) {
            return appName;
        }
        
        return "flink-job";
    }

    /**
     * 获取应用版本
     * 
     * @return 应用版本
     */
    public static String getApplicationVersion() {
        String version = System.getenv(ENV_APPLICATION_VERSION);
        if (StringUtils.isNotBlank(version)) {
            return version;
        }
        
        version = System.getProperty("application.version");
        if (StringUtils.isNotBlank(version)) {
            return version;
        }
        
        return "unknown";
    }

    /**
     * 获取配置文件路径
     * 
     * @return 配置文件路径
     */
    public static String getConfigPath() {
        String configPath = System.getenv(ENV_CONFIG_PATH);
        if (StringUtils.isNotBlank(configPath)) {
            return configPath;
        }
        
        configPath = System.getProperty("config.path");
        if (StringUtils.isNotBlank(configPath)) {
            return configPath;
        }
        
        // 根据环境返回默认路径
        Environment env = getCurrentEnvironment();
        switch (env) {
            case PRODUCTION:
                return "/opt/flink/conf/application.properties";
            case TEST:
            case STAGING:
                return "/opt/flink/conf/application-test.properties";
            case DEVELOPMENT:
            case LOCAL:
            default:
                return "/opt/flink/conf/application-dev.properties";
        }
    }

    /**
     * 获取日志级别
     * 
     * @return 日志级别
     */
    public static String getLogLevel() {
        String logLevel = System.getenv(ENV_LOG_LEVEL);
        if (StringUtils.isNotBlank(logLevel)) {
            return logLevel.toUpperCase();
        }
        
        logLevel = System.getProperty("log.level");
        if (StringUtils.isNotBlank(logLevel)) {
            return logLevel.toUpperCase();
        }
        
        // 根据环境返回默认日志级别
        Environment env = getCurrentEnvironment();
        switch (env) {
            case PRODUCTION:
                return "WARN";
            case TEST:
            case STAGING:
                return "INFO";
            case DEVELOPMENT:
            case LOCAL:
            default:
                return "DEBUG";
        }
    }

    /**
     * 是否为调试模式
     * 
     * @return 是否为调试模式
     */
    public static boolean isDebugMode() {
        String debugMode = System.getenv(ENV_DEBUG_MODE);
        if (StringUtils.isNotBlank(debugMode)) {
            return Boolean.parseBoolean(debugMode);
        }
        
        debugMode = System.getProperty("debug.mode");
        if (StringUtils.isNotBlank(debugMode)) {
            return Boolean.parseBoolean(debugMode);
        }
        
        // 开发环境默认开启调试模式
        return getCurrentEnvironment().isDevelopment();
    }

    /**
     * 是否启用性能监控
     * 
     * @return 是否启用性能监控
     */
    public static boolean isPerformanceMonitoringEnabled() {
        // 生产环境和测试环境默认启用性能监控
        Environment env = getCurrentEnvironment();
        return env.isProduction() || env.isTest();
    }

    /**
     * 是否启用详细日志
     * 
     * @return 是否启用详细日志
     */
    public static boolean isVerboseLoggingEnabled() {
        // 开发环境默认启用详细日志
        return getCurrentEnvironment().isDevelopment();
    }

    /**
     * 获取环境特定的配置前缀
     * 
     * @return 配置前缀
     */
    public static String getConfigPrefix() {
        return "flink." + getCurrentEnvironment().getCode();
    }

    /**
     * 打印环境信息
     */
    public static void printEnvironmentInfo() {
        log.info("==================== 环境信息 ====================");
        Map<String, String> info = getEnvironmentInfo();
        info.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> log.info("{}: {}", entry.getKey(), entry.getValue()));
        log.info("================================================");
    }
}
