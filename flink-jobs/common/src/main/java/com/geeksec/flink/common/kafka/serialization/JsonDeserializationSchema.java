package com.geeksec.flink.common.kafka.serialization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * JSON 反序列化器
 * 将 JSON 字节数组反序列化为对象
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class JsonDeserializationSchema<T> implements DeserializationSchema<T> {

    private static final long serialVersionUID = 1L;

    /** 目标类型 */
    private final Class<T> targetClass;

    /** Jackson ObjectMapper */
    private transient ObjectMapper objectMapper;

    /** 是否忽略未知属性 */
    private final boolean ignoreUnknownProperties;

    /** 是否允许空值 */
    private final boolean allowNullValues;

    /**
     * 构造函数
     * 
     * @param targetClass 目标类型
     */
    public JsonDeserializationSchema(Class<T> targetClass) {
        this(targetClass, true, true);
    }

    /**
     * 构造函数
     * 
     * @param targetClass 目标类型
     * @param ignoreUnknownProperties 是否忽略未知属性
     * @param allowNullValues 是否允许空值
     */
    public JsonDeserializationSchema(Class<T> targetClass, boolean ignoreUnknownProperties, boolean allowNullValues) {
        this.targetClass = targetClass;
        this.ignoreUnknownProperties = ignoreUnknownProperties;
        this.allowNullValues = allowNullValues;
    }

    @Override
    public void open(InitializationContext context) throws Exception {
        this.objectMapper = createObjectMapper();
    }

    @Override
    public T deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            if (allowNullValues) {
                log.debug("接收到空消息，返回 null");
                return null;
            } else {
                throw new IOException("接收到空消息，但不允许空值");
            }
        }

        try {
            if (objectMapper == null) {
                objectMapper = createObjectMapper();
            }
            
            String json = new String(message, StandardCharsets.UTF_8);
            log.debug("反序列化 JSON: {}", json);
            
            return objectMapper.readValue(json, targetClass);
        } catch (JsonProcessingException e) {
            log.error("JSON 反序列化失败: {}", new String(message, StandardCharsets.UTF_8), e);
            throw new IOException("JSON 反序列化失败", e);
        }
    }

    @Override
    public boolean isEndOfStream(T nextElement) {
        return false;
    }

    @Override
    public TypeInformation<T> getProducedType() {
        return TypeInformation.of(targetClass);
    }

    /**
     * 创建 ObjectMapper
     * 
     * @return ObjectMapper 实例
     */
    private ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 注册 Java 8 时间模块
        mapper.registerModule(new JavaTimeModule());
        
        // 配置未知属性处理
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, !ignoreUnknownProperties);
        
        // 配置空值处理
        mapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, !allowNullValues);
        mapper.configure(DeserializationFeature.FAIL_ON_NULL_CREATOR_PROPERTIES, !allowNullValues);
        
        // 配置数字处理
        mapper.configure(DeserializationFeature.FAIL_ON_NUMBERS_FOR_ENUMS, false);
        mapper.configure(DeserializationFeature.READ_ENUMS_USING_TO_STRING, true);
        
        // 配置日期处理
        mapper.configure(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE, false);
        
        return mapper;
    }

    /**
     * 创建忽略未知属性的反序列化器
     * 
     * @param targetClass 目标类型
     * @param <T> 数据类型
     * @return JSON 反序列化器
     */
    public static <T> JsonDeserializationSchema<T> ignoreUnknownProperties(Class<T> targetClass) {
        return new JsonDeserializationSchema<>(targetClass, true, true);
    }

    /**
     * 创建严格模式的反序列化器
     * 
     * @param targetClass 目标类型
     * @param <T> 数据类型
     * @return JSON 反序列化器
     */
    public static <T> JsonDeserializationSchema<T> strict(Class<T> targetClass) {
        return new JsonDeserializationSchema<>(targetClass, false, false);
    }

    /**
     * 创建自定义配置的反序列化器
     * 
     * @param targetClass 目标类型
     * @param ignoreUnknownProperties 是否忽略未知属性
     * @param allowNullValues 是否允许空值
     * @param <T> 数据类型
     * @return JSON 反序列化器
     */
    public static <T> JsonDeserializationSchema<T> create(Class<T> targetClass, 
                                                          boolean ignoreUnknownProperties, 
                                                          boolean allowNullValues) {
        return new JsonDeserializationSchema<>(targetClass, ignoreUnknownProperties, allowNullValues);
    }
}
