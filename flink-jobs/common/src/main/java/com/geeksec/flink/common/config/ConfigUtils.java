package com.geeksec.flink.common.config;

import com.geeksec.flink.common.exception.ErrorCode;
import com.geeksec.flink.common.exception.FlinkJobException;
import com.geeksec.flink.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * 配置工具类
 * 提供配置加载、验证、转换等通用功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class ConfigUtils {

    private ConfigUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 常用正则表达式 ====================

    /** 环境变量名正则表达式 */
    private static final Pattern ENV_VAR_PATTERN = Pattern.compile("^[A-Z][A-Z0-9_]*$");

    /** 配置键名正则表达式 */
    private static final Pattern CONFIG_KEY_PATTERN = Pattern.compile("^[a-z][a-z0-9._-]*$");

    /** URL 正则表达式 */
    private static final Pattern URL_PATTERN = Pattern.compile("^(https?|ftp)://[^\\s/$.?#].[^\\s]*$");

    // ==================== 配置加载 ====================

    /**
     * 从多个源加载配置
     * 
     * @param sources 配置源列表（按优先级排序，后面的会覆盖前面的）
     * @return 合并后的配置
     */
    public static ParameterTool loadFromSources(ConfigSource... sources) {
        if (sources == null || sources.length == 0) {
            return ParameterTool.fromMap(Collections.emptyMap());
        }

        ParameterTool result = null;
        for (ConfigSource source : sources) {
            try {
                ParameterTool config = source.load();
                if (result == null) {
                    result = config;
                } else {
                    result = result.mergeWith(config);
                }
                log.debug("成功加载配置源: {}", source.getDescription());
            } catch (Exception e) {
                log.warn("加载配置源失败: {}", source.getDescription(), e);
                if (source.isRequired()) {
                    throw new FlinkJobException(ErrorCode.CONFIGURATION_ERROR, e,
                            "必需的配置源加载失败: %s", source.getDescription());
                }
            }
        }

        return result != null ? result : ParameterTool.fromMap(Collections.emptyMap());
    }

    /**
     * 从属性文件加载配置
     * 
     * @param filePath 文件路径
     * @return 配置对象
     */
    public static ParameterTool loadFromPropertiesFile(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            throw new FlinkJobException(ErrorCode.INVALID_PARAMETER, "配置文件路径不能为空");
        }

        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                throw new FlinkJobException(ErrorCode.FILE_NOT_FOUND, "配置文件不存在: %s", filePath);
            }

            return ParameterTool.fromPropertiesFile(filePath);
        } catch (IOException e) {
            throw new FlinkJobException(ErrorCode.FILE_READ_FAILED, e, "读取配置文件失败: %s", filePath);
        }
    }

    /**
     * 从类路径资源加载配置
     * 
     * @param resourcePath 资源路径
     * @return 配置对象
     */
    public static ParameterTool loadFromResource(String resourcePath) {
        if (StringUtils.isBlank(resourcePath)) {
            throw new FlinkJobException(ErrorCode.INVALID_PARAMETER, "资源路径不能为空");
        }

        try (InputStream inputStream = ConfigUtils.class.getClassLoader().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new FlinkJobException(ErrorCode.FILE_NOT_FOUND, "资源文件不存在: %s", resourcePath);
            }

            Properties properties = new Properties();
            properties.load(inputStream);
            return ParameterTool.fromMap(propertiesToMap(properties));
        } catch (IOException e) {
            throw new FlinkJobException(ErrorCode.FILE_READ_FAILED, e, "读取资源文件失败: %s", resourcePath);
        }
    }

    /**
     * 从环境变量加载配置
     * 
     * @param prefix 环境变量前缀（可选）
     * @return 配置对象
     */
    public static ParameterTool loadFromEnvironment(String prefix) {
        Map<String, String> envMap = System.getenv();
        if (StringUtils.isBlank(prefix)) {
            return ParameterTool.fromMap(envMap);
        }

        Map<String, String> filteredMap = new HashMap<>();
        String prefixWithDot = prefix.endsWith(".") ? prefix : prefix + ".";
        
        for (Map.Entry<String, String> entry : envMap.entrySet()) {
            String key = entry.getKey();
            if (key.startsWith(prefixWithDot)) {
                String newKey = key.substring(prefixWithDot.length());
                filteredMap.put(newKey, entry.getValue());
            }
        }

        return ParameterTool.fromMap(filteredMap);
    }

    /**
     * 从系统属性加载配置
     * 
     * @param prefix 系统属性前缀（可选）
     * @return 配置对象
     */
    public static ParameterTool loadFromSystemProperties(String prefix) {
        Properties sysProps = System.getProperties();
        if (StringUtils.isBlank(prefix)) {
            return ParameterTool.fromMap(propertiesToMap(sysProps));
        }

        Map<String, String> filteredMap = new HashMap<>();
        String prefixWithDot = prefix.endsWith(".") ? prefix : prefix + ".";
        
        for (String key : sysProps.stringPropertyNames()) {
            if (key.startsWith(prefixWithDot)) {
                String newKey = key.substring(prefixWithDot.length());
                filteredMap.put(newKey, sysProps.getProperty(key));
            }
        }

        return ParameterTool.fromMap(filteredMap);
    }

    // ==================== 配置获取和转换 ====================

    /**
     * 安全地获取字符串配置值
     * 
     * @param config 配置对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getString(ParameterTool config, String key, String defaultValue) {
        if (config == null || StringUtils.isBlank(key)) {
            return defaultValue;
        }
        return config.get(key, defaultValue);
    }

    /**
     * 安全地获取整数配置值
     * 
     * @param config 配置对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static int getInt(ParameterTool config, String key, int defaultValue) {
        if (config == null || StringUtils.isBlank(key)) {
            return defaultValue;
        }
        return config.getInt(key, defaultValue);
    }

    /**
     * 安全地获取长整数配置值
     * 
     * @param config 配置对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static long getLong(ParameterTool config, String key, long defaultValue) {
        if (config == null || StringUtils.isBlank(key)) {
            return defaultValue;
        }
        return config.getLong(key, defaultValue);
    }

    /**
     * 安全地获取布尔配置值
     * 
     * @param config 配置对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static boolean getBoolean(ParameterTool config, String key, boolean defaultValue) {
        if (config == null || StringUtils.isBlank(key)) {
            return defaultValue;
        }
        return config.getBoolean(key, defaultValue);
    }

    /**
     * 获取时间间隔配置值
     * 
     * @param config 配置对象
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 时间间隔
     */
    public static Duration getDuration(ParameterTool config, String key, Duration defaultValue) {
        String value = getString(config, key, null);
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }

        try {
            // 支持多种格式：10s, 5m, 1h, 2d
            return parseDuration(value);
        } catch (Exception e) {
            log.warn("解析时间间隔配置失败: key={}, value={}, 使用默认值: {}", key, value, defaultValue, e);
            return defaultValue;
        }
    }

    /**
     * 获取列表配置值
     * 
     * @param config 配置对象
     * @param key 配置键
     * @param delimiter 分隔符
     * @param defaultValue 默认值
     * @return 列表值
     */
    public static List<String> getList(ParameterTool config, String key, String delimiter, List<String> defaultValue) {
        String value = getString(config, key, null);
        if (StringUtils.isBlank(value)) {
            return defaultValue != null ? defaultValue : new ArrayList<>();
        }

        return Arrays.asList(value.split(Pattern.quote(delimiter)));
    }

    /**
     * 获取映射配置值
     * 
     * @param config 配置对象
     * @param prefix 前缀
     * @return 映射值
     */
    public static Map<String, String> getMap(ParameterTool config, String prefix) {
        if (config == null || StringUtils.isBlank(prefix)) {
            return new HashMap<>();
        }

        Map<String, String> result = new HashMap<>();
        String prefixWithDot = prefix.endsWith(".") ? prefix : prefix + ".";
        
        for (String key : config.getProperties().stringPropertyNames()) {
            if (key.startsWith(prefixWithDot)) {
                String newKey = key.substring(prefixWithDot.length());
                result.put(newKey, config.get(key));
            }
        }

        return result;
    }

    // ==================== 配置验证 ====================

    /**
     * 验证必需的配置项
     * 
     * @param config 配置对象
     * @param requiredKeys 必需的配置键
     * @return 验证结果
     */
    public static ConfigValidationResult validateRequired(ParameterTool config, String... requiredKeys) {
        ConfigValidationResult result = new ConfigValidationResult();
        
        if (config == null) {
            result.addError("配置对象不能为空");
            return result;
        }

        if (requiredKeys != null) {
            for (String key : requiredKeys) {
                if (StringUtils.isBlank(key)) {
                    result.addWarning("配置键不能为空");
                    continue;
                }
                
                if (!config.has(key) || StringUtils.isBlank(config.get(key))) {
                    result.addError("缺少必需的配置项: %s", key);
                }
            }
        }

        return result;
    }

    /**
     * 验证配置值的范围
     * 
     * @param config 配置对象
     * @param key 配置键
     * @param min 最小值
     * @param max 最大值
     * @return 验证结果
     */
    public static ConfigValidationResult validateRange(ParameterTool config, String key, int min, int max) {
        ConfigValidationResult result = new ConfigValidationResult();
        
        if (config == null || !config.has(key)) {
            return result;
        }

        try {
            int value = config.getInt(key);
            if (value < min || value > max) {
                result.addError("配置项 %s 的值 %d 超出范围 [%d, %d]", key, value, min, max);
            }
        } catch (NumberFormatException e) {
            result.addError("配置项 %s 的值不是有效的整数", key);
        }

        return result;
    }

    /**
     * 验证 URL 配置
     * 
     * @param config 配置对象
     * @param key 配置键
     * @return 验证结果
     */
    public static ConfigValidationResult validateUrl(ParameterTool config, String key) {
        ConfigValidationResult result = new ConfigValidationResult();
        
        if (config == null || !config.has(key)) {
            return result;
        }

        String url = config.get(key);
        if (StringUtils.isNotBlank(url) && !URL_PATTERN.matcher(url).matches()) {
            result.addError("配置项 %s 的值不是有效的 URL: %s", key, url);
        }

        return result;
    }

    // ==================== 工具方法 ====================

    /**
     * 解析时间间隔字符串
     * 
     * @param durationStr 时间间隔字符串
     * @return 时间间隔对象
     */
    private static Duration parseDuration(String durationStr) {
        if (StringUtils.isBlank(durationStr)) {
            throw new IllegalArgumentException("时间间隔字符串不能为空");
        }

        String str = durationStr.trim().toLowerCase();
        
        // 尝试解析数字和单位
        if (str.matches("\\d+")) {
            // 纯数字，默认为毫秒
            return Duration.ofMillis(Long.parseLong(str));
        } else if (str.matches("\\d+ms")) {
            return Duration.ofMillis(Long.parseLong(str.substring(0, str.length() - 2)));
        } else if (str.matches("\\d+s")) {
            return Duration.ofSeconds(Long.parseLong(str.substring(0, str.length() - 1)));
        } else if (str.matches("\\d+m")) {
            return Duration.ofMinutes(Long.parseLong(str.substring(0, str.length() - 1)));
        } else if (str.matches("\\d+h")) {
            return Duration.ofHours(Long.parseLong(str.substring(0, str.length() - 1)));
        } else if (str.matches("\\d+d")) {
            return Duration.ofDays(Long.parseLong(str.substring(0, str.length() - 1)));
        } else {
            // 尝试使用 Duration.parse
            return Duration.parse(durationStr);
        }
    }

    /**
     * 将 Properties 转换为 Map
     * 
     * @param properties Properties 对象
     * @return Map 对象
     */
    private static Map<String, String> propertiesToMap(Properties properties) {
        Map<String, String> map = new HashMap<>();
        for (String key : properties.stringPropertyNames()) {
            map.put(key, properties.getProperty(key));
        }
        return map;
    }

    /**
     * 配置源接口
     */
    public interface ConfigSource {
        /**
         * 加载配置
         * 
         * @return 配置对象
         * @throws Exception 加载异常
         */
        ParameterTool load() throws Exception;

        /**
         * 获取描述信息
         * 
         * @return 描述信息
         */
        String getDescription();

        /**
         * 是否为必需的配置源
         * 
         * @return 是否必需
         */
        default boolean isRequired() {
            return false;
        }
    }

    // ==================== 预定义配置源 ====================

    /**
     * 文件配置源
     * 
     * @param filePath 文件路径
     * @param required 是否必需
     * @return 配置源
     */
    public static ConfigSource fileSource(String filePath, boolean required) {
        return new ConfigSource() {
            @Override
            public ParameterTool load() throws Exception {
                return loadFromPropertiesFile(filePath);
            }

            @Override
            public String getDescription() {
                return "文件: " + filePath;
            }

            @Override
            public boolean isRequired() {
                return required;
            }
        };
    }

    /**
     * 资源配置源
     * 
     * @param resourcePath 资源路径
     * @param required 是否必需
     * @return 配置源
     */
    public static ConfigSource resourceSource(String resourcePath, boolean required) {
        return new ConfigSource() {
            @Override
            public ParameterTool load() throws Exception {
                return loadFromResource(resourcePath);
            }

            @Override
            public String getDescription() {
                return "资源: " + resourcePath;
            }

            @Override
            public boolean isRequired() {
                return required;
            }
        };
    }

    /**
     * 环境变量配置源
     * 
     * @return 配置源
     */
    public static ConfigSource environmentSource() {
        return new ConfigSource() {
            @Override
            public ParameterTool load() throws Exception {
                return ParameterTool.fromMap(System.getenv());
            }

            @Override
            public String getDescription() {
                return "环境变量";
            }
        };
    }

    /**
     * 系统属性配置源
     * 
     * @return 配置源
     */
    public static ConfigSource systemPropertiesSource() {
        return new ConfigSource() {
            @Override
            public ParameterTool load() throws Exception {
                return ParameterTool.fromSystemProperties();
            }

            @Override
            public String getDescription() {
                return "系统属性";
            }
        };
    }
}
