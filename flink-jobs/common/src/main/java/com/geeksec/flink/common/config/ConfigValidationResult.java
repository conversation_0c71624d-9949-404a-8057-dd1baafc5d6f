package com.geeksec.flink.common.config;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 配置验证结果
 * 用于存储配置验证过程中的错误和警告信息
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
public class ConfigValidationResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 是否验证通过 */
    private boolean valid = true;
    
    /** 错误信息列表 */
    private List<String> errorMessages = new ArrayList<>();
    
    /** 警告信息列表 */
    private List<String> warningMessages = new ArrayList<>();
    
    /**
     * 添加错误信息
     * 
     * @param message 错误信息
     */
    public void addError(String message) {
        this.valid = false;
        this.errorMessages.add(message);
    }
    
    /**
     * 添加错误信息（带格式化参数）
     * 
     * @param format 错误信息格式
     * @param args 格式化参数
     */
    public void addError(String format, Object... args) {
        addError(String.format(format, args));
    }
    
    /**
     * 添加警告信息
     * 
     * @param message 警告信息
     */
    public void addWarning(String message) {
        this.warningMessages.add(message);
    }
    
    /**
     * 添加警告信息（带格式化参数）
     * 
     * @param format 警告信息格式
     * @param args 格式化参数
     */
    public void addWarning(String format, Object... args) {
        addWarning(String.format(format, args));
    }
    
    /**
     * 检查是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasErrors() {
        return !errorMessages.isEmpty();
    }
    
    /**
     * 检查是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return !warningMessages.isEmpty();
    }
    
    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errorMessages.size();
    }
    
    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warningMessages.size();
    }
    
    /**
     * 获取所有错误信息的字符串表示
     * 
     * @return 错误信息字符串
     */
    public String getErrorSummary() {
        if (errorMessages.isEmpty()) {
            return "无错误";
        }
        return String.join("; ", errorMessages);
    }
    
    /**
     * 获取所有警告信息的字符串表示
     * 
     * @return 警告信息字符串
     */
    public String getWarningSummary() {
        if (warningMessages.isEmpty()) {
            return "无警告";
        }
        return String.join("; ", warningMessages);
    }
    
    /**
     * 获取完整的验证结果摘要
     * 
     * @return 验证结果摘要
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("配置验证结果: ").append(valid ? "通过" : "失败").append("\n");
        
        if (hasErrors()) {
            summary.append("错误 (").append(getErrorCount()).append("): ")
                   .append(getErrorSummary()).append("\n");
        }
        
        if (hasWarnings()) {
            summary.append("警告 (").append(getWarningCount()).append("): ")
                   .append(getWarningSummary()).append("\n");
        }
        
        return summary.toString();
    }
    
    /**
     * 合并另一个验证结果
     * 
     * @param other 另一个验证结果
     */
    public void merge(ConfigValidationResult other) {
        if (other == null) {
            return;
        }
        
        if (!other.isValid()) {
            this.valid = false;
        }
        
        this.errorMessages.addAll(other.getErrorMessages());
        this.warningMessages.addAll(other.getWarningMessages());
    }
    
    /**
     * 清空所有验证结果
     */
    public void clear() {
        this.valid = true;
        this.errorMessages.clear();
        this.warningMessages.clear();
    }
    
    /**
     * 创建一个成功的验证结果
     * 
     * @return 成功的验证结果
     */
    public static ConfigValidationResult success() {
        return new ConfigValidationResult();
    }
    
    /**
     * 创建一个失败的验证结果
     * 
     * @param errorMessage 错误信息
     * @return 失败的验证结果
     */
    public static ConfigValidationResult failure(String errorMessage) {
        ConfigValidationResult result = new ConfigValidationResult();
        result.addError(errorMessage);
        return result;
    }
    
    /**
     * 创建一个带警告的验证结果
     * 
     * @param warningMessage 警告信息
     * @return 带警告的验证结果
     */
    public static ConfigValidationResult warning(String warningMessage) {
        ConfigValidationResult result = new ConfigValidationResult();
        result.addWarning(warningMessage);
        return result;
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}
