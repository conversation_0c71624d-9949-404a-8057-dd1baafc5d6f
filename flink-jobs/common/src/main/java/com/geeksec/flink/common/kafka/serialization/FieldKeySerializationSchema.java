package com.geeksec.flink.common.kafka.serialization;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.types.Row;

import java.nio.charset.StandardCharsets;
import java.util.function.Function;

/**
 * 字段键序列化器
 * 从数据对象中提取指定字段作为 Kafka 消息的键
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class FieldKeySerializationSchema<T> implements SerializationSchema<T> {

    private static final long serialVersionUID = 1L;

    /** 键提取函数 */
    private final Function<T, String> keyExtractor;

    /** 默认键值（当提取失败时使用） */
    private final String defaultKey;

    /**
     * 构造函数
     * 
     * @param keyExtractor 键提取函数
     */
    public FieldKeySerializationSchema(Function<T, String> keyExtractor) {
        this(keyExtractor, "default");
    }

    /**
     * 构造函数
     * 
     * @param keyExtractor 键提取函数
     * @param defaultKey 默认键值
     */
    public FieldKeySerializationSchema(Function<T, String> keyExtractor, String defaultKey) {
        this.keyExtractor = keyExtractor;
        this.defaultKey = defaultKey;
    }

    @Override
    public byte[] serialize(T element) {
        if (element == null) {
            log.warn("尝试从空对象提取键，使用默认键: {}", defaultKey);
            return defaultKey.getBytes(StandardCharsets.UTF_8);
        }

        try {
            String key = keyExtractor.apply(element);
            if (key == null || key.trim().isEmpty()) {
                log.debug("提取的键为空，使用默认键: {}", defaultKey);
                return defaultKey.getBytes(StandardCharsets.UTF_8);
            }
            return key.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("提取键失败，使用默认键: {}, 错误: {}", defaultKey, e.getMessage());
            return defaultKey.getBytes(StandardCharsets.UTF_8);
        }
    }

    /**
     * 创建 Row 类型的会话 ID 键序列化器
     * 
     * @param sessionIdField 会话 ID 字段名或索引
     * @return 键序列化器
     */
    public static FieldKeySerializationSchema<Row> forRowSessionId(String sessionIdField) {
        return new FieldKeySerializationSchema<>(row -> {
            try {
                // 尝试按字段名获取
                Object sessionId = row.getFieldAs(sessionIdField);
                return sessionId != null ? sessionId.toString() : null;
            } catch (Exception e) {
                // 如果按字段名失败，尝试按索引获取
                try {
                    int index = Integer.parseInt(sessionIdField);
                    Object sessionId = row.getField(index);
                    return sessionId != null ? sessionId.toString() : null;
                } catch (Exception ex) {
                    return null;
                }
            }
        }, "unknown-session");
    }

    /**
     * 创建 Row 类型的会话 ID 键序列化器（使用索引）
     * 
     * @param sessionIdIndex 会话 ID 字段索引
     * @return 键序列化器
     */
    public static FieldKeySerializationSchema<Row> forRowSessionId(int sessionIdIndex) {
        return new FieldKeySerializationSchema<>(row -> {
            try {
                Object sessionId = row.getField(sessionIdIndex);
                return sessionId != null ? sessionId.toString() : null;
            } catch (Exception e) {
                return null;
            }
        }, "unknown-session");
    }

    /**
     * 创建字符串类型的键序列化器
     * 
     * @return 键序列化器
     */
    public static FieldKeySerializationSchema<String> forString() {
        return new FieldKeySerializationSchema<>(str -> str, "empty-string");
    }

    /**
     * 创建对象哈希码键序列化器
     * 
     * @param <T> 数据类型
     * @return 键序列化器
     */
    public static <T> FieldKeySerializationSchema<T> forHashCode() {
        return new FieldKeySerializationSchema<>(obj -> String.valueOf(obj.hashCode()), "0");
    }

    /**
     * 创建对象字符串表示键序列化器
     * 
     * @param <T> 数据类型
     * @return 键序列化器
     */
    public static <T> FieldKeySerializationSchema<T> forToString() {
        return new FieldKeySerializationSchema<>(Object::toString, "null");
    }

    /**
     * 创建自定义字段提取器的键序列化器
     * 
     * @param fieldExtractor 字段提取器
     * @param defaultKey 默认键值
     * @param <T> 数据类型
     * @return 键序列化器
     */
    public static <T> FieldKeySerializationSchema<T> forField(Function<T, String> fieldExtractor, String defaultKey) {
        return new FieldKeySerializationSchema<>(fieldExtractor, defaultKey);
    }

    /**
     * 创建反射字段提取器的键序列化器
     * 
     * @param fieldName 字段名
     * @param targetClass 目标类
     * @param <T> 数据类型
     * @return 键序列化器
     */
    public static <T> FieldKeySerializationSchema<T> forReflectionField(String fieldName, Class<T> targetClass) {
        return new FieldKeySerializationSchema<>(obj -> {
            try {
                var field = targetClass.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(obj);
                return value != null ? value.toString() : null;
            } catch (Exception e) {
                return null;
            }
        }, "reflection-error");
    }

    /**
     * 创建 getter 方法提取器的键序列化器
     * 
     * @param getterName getter 方法名
     * @param targetClass 目标类
     * @param <T> 数据类型
     * @return 键序列化器
     */
    public static <T> FieldKeySerializationSchema<T> forGetterMethod(String getterName, Class<T> targetClass) {
        return new FieldKeySerializationSchema<>(obj -> {
            try {
                var method = targetClass.getMethod(getterName);
                Object value = method.invoke(obj);
                return value != null ? value.toString() : null;
            } catch (Exception e) {
                return null;
            }
        }, "getter-error");
    }
}
