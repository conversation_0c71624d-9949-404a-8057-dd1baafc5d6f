package com.geeksec.flink.common.kafka.serialization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;

import java.nio.charset.StandardCharsets;

/**
 * JSON 序列化器
 * 将对象序列化为 JSON 字节数组
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class JsonSerializationSchema<T> implements SerializationSchema<T> {

    private static final long serialVersionUID = 1L;

    /** Jackson ObjectMapper */
    private transient ObjectMapper objectMapper;

    /** 是否格式化输出 */
    private final boolean prettyPrint;

    /** 是否忽略空值 */
    private final boolean ignoreNullValues;

    /**
     * 默认构造函数
     */
    public JsonSerializationSchema() {
        this(false, true);
    }

    /**
     * 构造函数
     * 
     * @param prettyPrint 是否格式化输出
     * @param ignoreNullValues 是否忽略空值
     */
    public JsonSerializationSchema(boolean prettyPrint, boolean ignoreNullValues) {
        this.prettyPrint = prettyPrint;
        this.ignoreNullValues = ignoreNullValues;
    }

    @Override
    public void open(InitializationContext context) throws Exception {
        this.objectMapper = createObjectMapper();
    }

    @Override
    public byte[] serialize(T element) {
        if (element == null) {
            log.warn("尝试序列化空对象");
            return new byte[0];
        }

        try {
            if (objectMapper == null) {
                objectMapper = createObjectMapper();
            }
            
            String json = objectMapper.writeValueAsString(element);
            return json.getBytes(StandardCharsets.UTF_8);
        } catch (JsonProcessingException e) {
            log.error("JSON 序列化失败: {}", element, e);
            // 返回错误信息的 JSON
            String errorJson = String.format("{\"error\":\"序列化失败\",\"message\":\"%s\",\"element\":\"%s\"}", 
                    e.getMessage(), element.toString());
            return errorJson.getBytes(StandardCharsets.UTF_8);
        }
    }

    /**
     * 创建 ObjectMapper
     * 
     * @return ObjectMapper 实例
     */
    private ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 注册 Java 8 时间模块
        mapper.registerModule(new JavaTimeModule());
        
        // 禁用将日期写为时间戳
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        // 配置格式化输出
        if (prettyPrint) {
            mapper.enable(SerializationFeature.INDENT_OUTPUT);
        }
        
        // 配置空值处理
        if (ignoreNullValues) {
            mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
        }
        
        // 配置未知属性处理
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        
        return mapper;
    }

    /**
     * 创建格式化输出的序列化器
     * 
     * @param <T> 数据类型
     * @return JSON 序列化器
     */
    public static <T> JsonSerializationSchema<T> prettyPrint() {
        return new JsonSerializationSchema<>(true, true);
    }

    /**
     * 创建包含空值的序列化器
     * 
     * @param <T> 数据类型
     * @return JSON 序列化器
     */
    public static <T> JsonSerializationSchema<T> includeNulls() {
        return new JsonSerializationSchema<>(false, false);
    }

    /**
     * 创建自定义配置的序列化器
     * 
     * @param prettyPrint 是否格式化输出
     * @param ignoreNullValues 是否忽略空值
     * @param <T> 数据类型
     * @return JSON 序列化器
     */
    public static <T> JsonSerializationSchema<T> create(boolean prettyPrint, boolean ignoreNullValues) {
        return new JsonSerializationSchema<>(prettyPrint, ignoreNullValues);
    }
}
