package com.geeksec.flink.common.exception;

/**
 * 数据处理异常
 * 用于数据处理过程中的异常情况
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class DataProcessingException extends FlinkJobException {

    private static final long serialVersionUID = 1L;

    /**
     * 默认构造函数
     */
    public DataProcessingException() {
        super(ErrorCode.DATA_PARSING_FAILED);
    }

    /**
     * 基于消息的构造函数
     * 
     * @param message 异常消息
     */
    public DataProcessingException(String message) {
        super(ErrorCode.DATA_PARSING_FAILED, null, message);
    }

    /**
     * 基于错误码的构造函数
     * 
     * @param errorCode 错误码
     */
    public DataProcessingException(ErrorCode errorCode) {
        super(errorCode);
    }

    /**
     * 基于错误码和参数的构造函数
     * 
     * @param errorCode 错误码
     * @param args 格式化参数
     */
    public DataProcessingException(ErrorCode errorCode, Object... args) {
        super(errorCode, args);
    }

    /**
     * 基于错误码和原因的构造函数
     * 
     * @param errorCode 错误码
     * @param cause 原因异常
     */
    public DataProcessingException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    /**
     * 基于错误码、原因和参数的构造函数
     * 
     * @param errorCode 错误码
     * @param cause 原因异常
     * @param args 格式化参数
     */
    public DataProcessingException(ErrorCode errorCode, Throwable cause, Object... args) {
        super(errorCode, cause, args);
    }

    /**
     * 基于消息和原因的构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public DataProcessingException(String message, Throwable cause) {
        super(ErrorCode.DATA_PARSING_FAILED, cause, message);
    }

    // ==================== 便捷工厂方法 ====================

    /**
     * 创建数据解析失败异常
     * 
     * @param dataType 数据类型
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException parsingFailed(String dataType, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_PARSING_FAILED, cause, 
                "解析 %s 数据失败", dataType);
    }

    /**
     * 创建数据转换失败异常
     * 
     * @param fromType 源类型
     * @param toType 目标类型
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException transformationFailed(String fromType, String toType, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_TRANSFORMATION_FAILED, cause,
                "数据转换失败：从 %s 转换为 %s", fromType, toType);
    }

    /**
     * 创建数据验证失败异常
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @param reason 失败原因
     * @return 数据处理异常
     */
    public static DataProcessingException validationFailed(String fieldName, Object value, String reason) {
        return new DataProcessingException(ErrorCode.DATA_VALIDATION_FAILED,
                "字段 %s 验证失败：值=%s，原因=%s", fieldName, value, reason);
    }

    /**
     * 创建数据清洗失败异常
     * 
     * @param step 清洗步骤
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException cleaningFailed(String step, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_CLEANING_FAILED, cause,
                "数据清洗失败：步骤=%s", step);
    }

    /**
     * 创建数据聚合失败异常
     * 
     * @param aggregationType 聚合类型
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException aggregationFailed(String aggregationType, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_AGGREGATION_FAILED, cause,
                "数据聚合失败：类型=%s", aggregationType);
    }

    /**
     * 创建数据分组失败异常
     * 
     * @param groupKey 分组键
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException groupingFailed(String groupKey, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_GROUPING_FAILED, cause,
                "数据分组失败：分组键=%s", groupKey);
    }

    /**
     * 创建数据排序失败异常
     * 
     * @param sortKey 排序键
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException sortingFailed(String sortKey, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_SORTING_FAILED, cause,
                "数据排序失败：排序键=%s", sortKey);
    }

    /**
     * 创建数据过滤失败异常
     * 
     * @param filterCondition 过滤条件
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException filteringFailed(String filterCondition, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_FILTERING_FAILED, cause,
                "数据过滤失败：条件=%s", filterCondition);
    }

    /**
     * 创建数据映射失败异常
     * 
     * @param mappingRule 映射规则
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException mappingFailed(String mappingRule, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_MAPPING_FAILED, cause,
                "数据映射失败：规则=%s", mappingRule);
    }

    /**
     * 创建数据合并失败异常
     * 
     * @param mergeStrategy 合并策略
     * @param cause 原因异常
     * @return 数据处理异常
     */
    public static DataProcessingException mergingFailed(String mergeStrategy, Throwable cause) {
        return new DataProcessingException(ErrorCode.DATA_MERGING_FAILED, cause,
                "数据合并失败：策略=%s", mergeStrategy);
    }
}
