package com.geeksec.flink.common.config;

import com.geeksec.flink.common.constants.FlinkConfigConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.api.java.utils.ParameterTool;

import java.io.IOException;
import java.util.Optional;

/**
 * Flink 配置加载器
 * 提供统一的 Flink 配置加载和管理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class FlinkConfigLoader {

    private FlinkConfigLoader() {
        // 工具类，禁止实例化
    }

    /**
     * 加载指定作业的配置
     * 
     * @param jobName 作业名称
     * @return Flink 配置对象
     */
    public static Configuration loadConfig(String jobName) {
        log.info("开始加载 Flink 作业配置: {}", jobName);
        
        // 获取参数工具
        ParameterTool parameterTool = ConfigurationManager.getConfig();
        
        // 创建 Flink 配置
        Configuration config = new Configuration();
        
        // 设置作业名称
        if (jobName != null && !jobName.trim().isEmpty()) {
            config.setString("pipeline.name", jobName);
        }
        
        // 设置基础配置
        configureBasicSettings(config, parameterTool);
        
        // 设置检查点配置
        configureCheckpointing(config, parameterTool);
        
        // 设置重启策略配置
        configureRestartStrategy(config, parameterTool);
        
        // 设置并行度配置
        configureParallelism(config, parameterTool);
        
        // 设置 REST API 配置
        configureRestApi(config, parameterTool);
        
        log.info("Flink 作业配置加载完成: {}", jobName);
        return config;
    }
    
    /**
     * 加载默认配置
     * 
     * @return Flink 配置对象
     */
    public static Configuration loadDefaultConfig() {
        return loadConfig("flink-job");
    }
    
    /**
     * 配置基础设置
     * 
     * @param config Flink 配置对象
     * @param parameterTool 参数工具
     */
    private static void configureBasicSettings(Configuration config, ParameterTool parameterTool) {
        // 设置默认并行度
        int parallelism = parameterTool.getInt(
            FlinkConfigConstants.JOB_PARALLELISM, 
            FlinkConfigConstants.DEFAULT_JOB_PARALLELISM
        );
        config.setInteger("parallelism.default", parallelism);
        
        // 设置状态后端
        String stateBackend = parameterTool.get("state.backend", "rocksdb");
        config.setString("state.backend", stateBackend);
        
        // 设置状态后端存储路径
        String checkpointDir = parameterTool.get("state.checkpoints.dir", "file:///tmp/flink-checkpoints");
        config.setString("state.checkpoints.dir", checkpointDir);
        
        // 设置保存点目录
        String savepointDir = parameterTool.get("state.savepoints.dir", "file:///tmp/flink-savepoints");
        config.setString("state.savepoints.dir", savepointDir);
    }
    
    /**
     * 配置检查点设置
     * 
     * @param config Flink 配置对象
     * @param parameterTool 参数工具
     */
    private static void configureCheckpointing(Configuration config, ParameterTool parameterTool) {
        // 检查点间隔
        long checkpointInterval = parameterTool.getLong(
            FlinkConfigConstants.CHECKPOINT_INTERVAL,
            FlinkConfigConstants.DEFAULT_CHECKPOINT_INTERVAL
        );
        config.setLong("execution.checkpointing.interval", checkpointInterval);
        
        // 检查点超时
        long checkpointTimeout = parameterTool.getLong(
            FlinkConfigConstants.CHECKPOINT_TIMEOUT,
            FlinkConfigConstants.DEFAULT_CHECKPOINT_TIMEOUT
        );
        config.setLong("execution.checkpointing.timeout", checkpointTimeout);
        
        // 检查点最小间隔
        long minPauseBetweenCheckpoints = parameterTool.getLong(
            FlinkConfigConstants.CHECKPOINT_MIN_PAUSE,
            FlinkConfigConstants.DEFAULT_CHECKPOINT_MIN_PAUSE
        );
        config.setLong("execution.checkpointing.min-pause", minPauseBetweenCheckpoints);
        
        // 最大并发检查点数
        int maxConcurrentCheckpoints = parameterTool.getInt(
            FlinkConfigConstants.CHECKPOINT_MAX_CONCURRENT,
            FlinkConfigConstants.DEFAULT_CHECKPOINT_MAX_CONCURRENT
        );
        config.setInteger("execution.checkpointing.max-concurrent-checkpoints", maxConcurrentCheckpoints);
        
        // 启用外部化检查点
        config.setString("execution.checkpointing.externalized-checkpoint-retention", "RETAIN_ON_CANCELLATION");
    }
    
    /**
     * 配置重启策略设置
     * 
     * @param config Flink 配置对象
     * @param parameterTool 参数工具
     */
    private static void configureRestartStrategy(Configuration config, ParameterTool parameterTool) {
        // 重启策略类型
        String restartStrategy = parameterTool.get("restart-strategy", "fixed-delay");
        config.setString("restart-strategy", restartStrategy);
        
        if ("fixed-delay".equals(restartStrategy)) {
            // 重启尝试次数
            int restartAttempts = parameterTool.getInt(
                FlinkConfigConstants.RESTART_ATTEMPTS,
                FlinkConfigConstants.DEFAULT_RESTART_ATTEMPTS
            );
            config.setInteger("restart-strategy.fixed-delay.attempts", restartAttempts);
            
            // 重启延迟
            long restartDelay = parameterTool.getLong(
                FlinkConfigConstants.RESTART_DELAY,
                FlinkConfigConstants.DEFAULT_RESTART_DELAY
            );
            config.setLong("restart-strategy.fixed-delay.delay", restartDelay);
        } else if ("failure-rate".equals(restartStrategy)) {
            // 故障率重启策略配置
            int maxFailuresPerInterval = parameterTool.getInt("restart-strategy.failure-rate.max-failures-per-interval", 3);
            config.setInteger("restart-strategy.failure-rate.max-failures-per-interval", maxFailuresPerInterval);
            
            long failureRateInterval = parameterTool.getLong(
                FlinkConfigConstants.RESTART_FAILURE_RATE_INTERVAL,
                FlinkConfigConstants.DEFAULT_RESTART_FAILURE_RATE_INTERVAL
            );
            config.setLong("restart-strategy.failure-rate.failure-rate-interval", failureRateInterval);
            
            long restartDelay = parameterTool.getLong(
                FlinkConfigConstants.RESTART_DELAY,
                FlinkConfigConstants.DEFAULT_RESTART_DELAY
            );
            config.setLong("restart-strategy.failure-rate.delay", restartDelay);
        }
    }
    
    /**
     * 配置并行度设置
     * 
     * @param config Flink 配置对象
     * @param parameterTool 参数工具
     */
    private static void configureParallelism(Configuration config, ParameterTool parameterTool) {
        // 最大并行度
        int maxParallelism = parameterTool.getInt("parallelism.max", FlinkConfigConstants.MAX_JOB_PARALLELISM);
        config.setInteger("parallelism.max", maxParallelism);
    }
    
    /**
     * 配置 REST API 设置
     * 
     * @param config Flink 配置对象
     * @param parameterTool 参数工具
     */
    private static void configureRestApi(Configuration config, ParameterTool parameterTool) {
        // REST API 端口
        int restPort = parameterTool.getInt("rest.port", 8081);
        config.setInteger(RestOptions.PORT, restPort);
        
        // REST API 绑定地址
        String restBindAddress = parameterTool.get("rest.bind-address", "0.0.0.0");
        config.setString(RestOptions.BIND_ADDRESS, restBindAddress);
    }
    
    /**
     * 验证配置的有效性
     * 
     * @param config Flink 配置对象
     * @return 配置验证结果
     */
    public static ConfigValidationResult validateConfig(Configuration config) {
        ConfigValidationResult result = new ConfigValidationResult();
        
        if (config == null) {
            result.addError("Flink 配置对象不能为空");
            return result;
        }
        
        // 验证并行度配置
        validateParallelismConfig(config, result);
        
        // 验证检查点配置
        validateCheckpointConfig(config, result);
        
        // 验证重启策略配置
        validateRestartStrategyConfig(config, result);
        
        return result;
    }
    
    /**
     * 验证并行度配置
     * 
     * @param config Flink 配置对象
     * @param result 验证结果
     */
    private static void validateParallelismConfig(Configuration config, ConfigValidationResult result) {
        Optional<Integer> parallelism = config.getOptional("parallelism.default", Integer.class);
        if (parallelism.isPresent()) {
            int value = parallelism.get();
            if (value <= 0) {
                result.addError("默认并行度必须大于 0，当前值: %d", value);
            } else if (value > FlinkConfigConstants.MAX_JOB_PARALLELISM) {
                result.addWarning("默认并行度 %d 超过推荐最大值 %d", value, FlinkConfigConstants.MAX_JOB_PARALLELISM);
            }
        }
    }
    
    /**
     * 验证检查点配置
     * 
     * @param config Flink 配置对象
     * @param result 验证结果
     */
    private static void validateCheckpointConfig(Configuration config, ConfigValidationResult result) {
        Optional<Long> interval = config.getOptional("execution.checkpointing.interval", Long.class);
        if (interval.isPresent()) {
            long value = interval.get();
            if (value <= 0) {
                result.addError("检查点间隔必须大于 0，当前值: %d", value);
            } else if (value < 10000) {
                result.addWarning("检查点间隔 %d ms 可能过短，建议至少 10 秒", value);
            }
        }
        
        Optional<Long> timeout = config.getOptional("execution.checkpointing.timeout", Long.class);
        if (timeout.isPresent() && interval.isPresent()) {
            long timeoutValue = timeout.get();
            long intervalValue = interval.get();
            if (timeoutValue <= intervalValue) {
                result.addError("检查点超时时间 %d ms 应该大于检查点间隔 %d ms", timeoutValue, intervalValue);
            }
        }
    }
    
    /**
     * 验证重启策略配置
     * 
     * @param config Flink 配置对象
     * @param result 验证结果
     */
    private static void validateRestartStrategyConfig(Configuration config, ConfigValidationResult result) {
        Optional<String> strategy = config.getOptional("restart-strategy", String.class);
        if (strategy.isPresent()) {
            String strategyValue = strategy.get();
            if (!"fixed-delay".equals(strategyValue) && 
                !"failure-rate".equals(strategyValue) && 
                !"none".equals(strategyValue)) {
                result.addError("不支持的重启策略: %s", strategyValue);
            }
        }
    }
}
