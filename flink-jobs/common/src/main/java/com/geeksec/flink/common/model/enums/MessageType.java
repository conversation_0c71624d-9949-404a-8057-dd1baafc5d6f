package com.geeksec.flink.common.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 消息类型枚举
 * 定义系统中所有协议消息的类型
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum MessageType {
    
    // ==================== 会话相关消息 ====================
    
    /** 单会话消息 */
    SINGLE_SESSION(31, "SINGLE_SESSION", "单会话", "会话统计和元数据信息", ProtocolCategory.SESSION),
    
    /** MAC 连接消息 */
    MAC_CONNECTION(1000, "MAC_CONNECTION", "MAC连接", "MAC地址连接信息", ProtocolCategory.SESSION),
    
    /** 无IP连接消息 */
    NO_IP_CONNECTION(1001, "NO_IP_CONNECTION", "无IP连接", "无IP地址的连接信息", ProtocolCategory.SESSION),
    
    // ==================== 应用层协议消息 ====================
    
    /** DNS 消息 */
    DNS(5, "DNS", "DNS", "域名解析协议", ProtocolCategory.APPLICATION),
    
    /** HTTP 头消息 */
    HTTP_HEADER(81, "HTTP_HEADER", "HTTP头", "HTTP请求响应头信息", ProtocolCategory.APPLICATION),
    
    /** SSL 消息 */
    SSL(30, "SSL", "SSL/TLS", "安全套接字层协议", ProtocolCategory.APPLICATION),
    
    /** SSH 消息 */
    SSH(1003, "SSH", "SSH", "安全外壳协议", ProtocolCategory.APPLICATION),
    
    /** FTP 消息 */
    FTP(1009, "FTP", "FTP", "文件传输协议", ProtocolCategory.APPLICATION),
    
    /** SMTP 消息 */
    SMTP(1010, "SMTP", "SMTP", "简单邮件传输协议", ProtocolCategory.APPLICATION),
    
    /** POP3 消息 */
    POP3(1011, "POP3", "POP3", "邮局协议版本3", ProtocolCategory.APPLICATION),
    
    /** IMAP 消息 */
    IMAP(1012, "IMAP", "IMAP", "互联网消息访问协议", ProtocolCategory.APPLICATION),
    
    /** NTP 消息 */
    NTP(1008, "NTP", "NTP", "网络时间协议", ProtocolCategory.APPLICATION),
    
    // ==================== 远程访问协议消息 ====================
    
    /** RDP 消息 */
    RDP(1002, "RDP", "RDP", "远程桌面协议", ProtocolCategory.REMOTE_ACCESS),
    
    /** VNC 消息 */
    VNC(1006, "VNC", "VNC", "虚拟网络计算", ProtocolCategory.REMOTE_ACCESS),
    
    /** Telnet 消息 */
    TELNET(1005, "TELNET", "Telnet", "远程登录协议", ProtocolCategory.REMOTE_ACCESS),
    
    /** RLogin 消息 */
    RLOGIN(1004, "RLOGIN", "RLogin", "远程登录协议", ProtocolCategory.REMOTE_ACCESS),
    
    /** XDMCP 消息 */
    XDMCP(1007, "XDMCP", "XDMCP", "X显示管理器控制协议", ProtocolCategory.REMOTE_ACCESS),
    
    // ==================== 工控协议消息 ====================
    
    /** S7 消息 */
    S7(41, "S7", "S7", "西门子S7通信协议", ProtocolCategory.INDUSTRIAL),
    
    /** Modbus 消息 */
    MODBUS(42, "MODBUS", "Modbus", "Modbus工业协议", ProtocolCategory.INDUSTRIAL),
    
    /** EIP 消息 */
    EIP(43, "EIP", "EIP", "以太网/IP协议", ProtocolCategory.INDUSTRIAL),
    
    /** IEC104 消息 */
    IEC104(44, "IEC104", "IEC104", "IEC 60870-5-104协议", ProtocolCategory.INDUSTRIAL),
    
    /** OPC 消息 */
    OPC(45, "OPC", "OPC", "OLE过程控制协议", ProtocolCategory.INDUSTRIAL),
    
    // ==================== 隧道协议消息 ====================
    
    /** L2TP 消息 */
    L2TP(27, "L2TP", "L2TP", "第二层隧道协议", ProtocolCategory.TUNNEL),
    
    /** ESP 消息 */
    ESP(62, "ESP", "ESP", "封装安全载荷协议", ProtocolCategory.TUNNEL),
    
    // ==================== 数据库协议消息 ====================
    
    /** MySQL 消息 */
    MYSQL(1013, "MYSQL", "MySQL", "MySQL数据库协议", ProtocolCategory.DATABASE),
    
    /** Oracle 消息 */
    ORACLE(1014, "ORACLE", "Oracle", "Oracle数据库协议", ProtocolCategory.DATABASE),
    
    /** PostgreSQL 消息 */
    POSTGRESQL(1015, "POSTGRESQL", "PostgreSQL", "PostgreSQL数据库协议", ProtocolCategory.DATABASE),
    
    /** MongoDB 消息 */
    MONGODB(1016, "MONGODB", "MongoDB", "MongoDB数据库协议", ProtocolCategory.DATABASE),
    
    /** Redis 消息 */
    REDIS(1017, "REDIS", "Redis", "Redis数据库协议", ProtocolCategory.DATABASE),
    
    // ==================== 文件共享协议消息 ====================
    
    /** SMB 消息 */
    SMB(1018, "SMB", "SMB", "服务器消息块协议", ProtocolCategory.FILE_SHARING),
    
    /** NFS 消息 */
    NFS(1019, "NFS", "NFS", "网络文件系统", ProtocolCategory.FILE_SHARING),
    
    // ==================== 其他协议消息 ====================
    
    /** 未知协议 */
    UNKNOWN(0, "UNKNOWN", "未知", "未识别的协议类型", ProtocolCategory.OTHER);

    /** 协议类型代码 */
    private final int code;
    
    /** 协议类型名称 */
    private final String name;
    
    /** 显示名称 */
    private final String displayName;
    
    /** 描述信息 */
    private final String description;
    
    /** 协议分类 */
    private final ProtocolCategory category;

    /** 代码映射 */
    private static final Map<Integer, MessageType> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(MessageType::getCode, Function.identity()));

    /** 名称映射 */
    private static final Map<String, MessageType> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(MessageType::getName, Function.identity()));

    /**
     * 构造函数
     * 
     * @param code 协议类型代码
     * @param name 协议类型名称
     * @param displayName 显示名称
     * @param description 描述信息
     * @param category 协议分类
     */
    MessageType(int code, String name, String displayName, String description, ProtocolCategory category) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
        this.description = description;
        this.category = category;
    }

    /**
     * 根据代码获取消息类型
     * 
     * @param code 协议类型代码
     * @return 消息类型，如果不存在则返回 UNKNOWN
     */
    public static MessageType fromCode(int code) {
        return CODE_MAP.getOrDefault(code, UNKNOWN);
    }

    /**
     * 根据名称获取消息类型
     * 
     * @param name 协议类型名称
     * @return 消息类型，如果不存在则返回 UNKNOWN
     */
    public static MessageType fromName(String name) {
        if (name == null) {
            return UNKNOWN;
        }
        return NAME_MAP.getOrDefault(name.toUpperCase(), UNKNOWN);
    }

    /**
     * 根据字符串获取消息类型（支持多种格式）
     * 
     * @param value 字符串值
     * @return 消息类型，如果不存在则返回 UNKNOWN
     */
    public static MessageType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return UNKNOWN;
        }

        // 尝试按名称匹配
        MessageType type = fromName(value);
        if (type != UNKNOWN) {
            return type;
        }

        // 尝试按代码匹配
        try {
            int code = Integer.parseInt(value);
            return fromCode(code);
        } catch (NumberFormatException ignored) {
            // 忽略数字解析异常
        }

        // 默认返回未知
        return UNKNOWN;
    }

    /**
     * 获取指定分类的所有消息类型
     * 
     * @param category 协议分类
     * @return 消息类型数组
     */
    public static MessageType[] getByCategory(ProtocolCategory category) {
        return Arrays.stream(values())
                .filter(type -> type.getCategory() == category)
                .toArray(MessageType[]::new);
    }

    /**
     * 判断是否为应用层协议
     * 
     * @return 是否为应用层协议
     */
    public boolean isApplicationProtocol() {
        return category == ProtocolCategory.APPLICATION;
    }

    /**
     * 判断是否为工控协议
     * 
     * @return 是否为工控协议
     */
    public boolean isIndustrialProtocol() {
        return category == ProtocolCategory.INDUSTRIAL;
    }

    /**
     * 判断是否为远程访问协议
     * 
     * @return 是否为远程访问协议
     */
    public boolean isRemoteAccessProtocol() {
        return category == ProtocolCategory.REMOTE_ACCESS;
    }

    /**
     * 判断是否为数据库协议
     * 
     * @return 是否为数据库协议
     */
    public boolean isDatabaseProtocol() {
        return category == ProtocolCategory.DATABASE;
    }

    /**
     * 判断是否为会话相关协议
     * 
     * @return 是否为会话相关协议
     */
    public boolean isSessionProtocol() {
        return category == ProtocolCategory.SESSION;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * 协议分类枚举
     */
    @Getter
    public enum ProtocolCategory {
        /** 会话相关 */
        SESSION("会话相关"),
        
        /** 应用层协议 */
        APPLICATION("应用层协议"),
        
        /** 远程访问协议 */
        REMOTE_ACCESS("远程访问协议"),
        
        /** 工控协议 */
        INDUSTRIAL("工控协议"),
        
        /** 隧道协议 */
        TUNNEL("隧道协议"),
        
        /** 数据库协议 */
        DATABASE("数据库协议"),
        
        /** 文件共享协议 */
        FILE_SHARING("文件共享协议"),
        
        /** 其他协议 */
        OTHER("其他协议");

        /** 分类名称 */
        private final String name;

        /**
         * 构造函数
         * 
         * @param name 分类名称
         */
        ProtocolCategory(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
}
