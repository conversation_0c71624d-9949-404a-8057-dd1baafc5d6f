package com.geeksec.flink.common.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.flink.common.model.enums.MessageType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.types.Row;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流数据包装类
 * 用于在流处理中携带消息类型信息和元数据
 * 支持会话数据和各种协议数据的统一处理
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class StreamData implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 消息类型 */
    @JsonProperty("message_type")
    private MessageType messageType;

    /** 消息数据 */
    @JsonProperty("data")
    private Row data;

    /** 数据源信息 */
    @JsonProperty("source_info")
    private SourceInfo sourceInfo;

    /** 处理时间戳 */
    @JsonProperty("processing_timestamp")
    private LocalDateTime processingTimestamp;

    /** 事件时间戳 */
    @JsonProperty("event_timestamp")
    private LocalDateTime eventTimestamp;

    /** 数据质量信息 */
    @JsonProperty("quality_info")
    private QualityInfo qualityInfo;

    /** 扩展元数据 */
    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    /**
     * 创建流数据包装对象
     *
     * @param messageType 消息类型
     * @param data 消息数据
     * @return 流数据包装对象
     */
    public static StreamData of(MessageType messageType, Row data) {
        StreamData streamData = new StreamData();
        streamData.setMessageType(messageType);
        streamData.setData(data);
        streamData.setProcessingTimestamp(LocalDateTime.now());
        streamData.setQualityInfo(QualityInfo.createDefault());
        return streamData;
    }

    /**
     * 创建带源信息的流数据包装对象
     *
     * @param messageType 消息类型
     * @param data 消息数据
     * @param sourceInfo 数据源信息
     * @return 流数据包装对象
     */
    public static StreamData of(MessageType messageType, Row data, SourceInfo sourceInfo) {
        StreamData streamData = of(messageType, data);
        streamData.setSourceInfo(sourceInfo);
        return streamData;
    }

    /**
     * 创建带完整信息的流数据包装对象
     *
     * @param messageType 消息类型
     * @param data 消息数据
     * @param sourceInfo 数据源信息
     * @param eventTimestamp 事件时间戳
     * @return 流数据包装对象
     */
    public static StreamData of(MessageType messageType, Row data, SourceInfo sourceInfo, LocalDateTime eventTimestamp) {
        StreamData streamData = of(messageType, data, sourceInfo);
        streamData.setEventTimestamp(eventTimestamp);
        return streamData;
    }

    /**
     * 从数据中获取 session_id
     *
     * @return session_id
     */
    public String getSessionId() {
        return getFieldValue("session_id", String.class);
    }

    /**
     * 从数据中获取源IP
     *
     * @return 源IP
     */
    public String getSourceIp() {
        return getFieldValue("src_ip", String.class);
    }

    /**
     * 从数据中获取目标IP
     *
     * @return 目标IP
     */
    public String getDestinationIp() {
        return getFieldValue("dst_ip", String.class);
    }

    /**
     * 从数据中获取源端口
     *
     * @return 源端口
     */
    public Integer getSourcePort() {
        return getFieldValue("src_port", Integer.class);
    }

    /**
     * 从数据中获取目标端口
     *
     * @return 目标端口
     */
    public Integer getDestinationPort() {
        return getFieldValue("dst_port", Integer.class);
    }

    /**
     * 从数据中获取协议类型
     *
     * @return 协议类型
     */
    public String getProtocol() {
        return getFieldValue("protocol", String.class);
    }

    /**
     * 从数据中获取字段值
     *
     * @param fieldName 字段名
     * @param type 字段类型
     * @param <T> 类型参数
     * @return 字段值
     */
    @SuppressWarnings("unchecked")
    public <T> T getFieldValue(String fieldName, Class<T> type) {
        if (data == null) {
            return null;
        }
        try {
            Object value = data.getFieldAs(fieldName);
            if (value != null && type.isAssignableFrom(value.getClass())) {
                return (T) value;
            }
        } catch (Exception e) {
            // 忽略字段获取异常
        }
        return null;
    }

    /**
     * 向数据中设置字段值
     *
     * @param fieldName 字段名
     * @param value 字段值
     */
    public void setFieldValue(String fieldName, Object value) {
        if (data != null) {
            try {
                data.setField(fieldName, value);
            } catch (Exception e) {
                // 忽略字段设置异常
            }
        }
    }

    /**
     * 添加元数据
     *
     * @param key 元数据键
     * @param value 元数据值
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new java.util.HashMap<>();
        }
        metadata.put(key, value);
    }

    /**
     * 获取元数据
     *
     * @param key 元数据键
     * @return 元数据值
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }

    /**
     * 获取元数据（指定类型）
     *
     * @param key 元数据键
     * @param type 元数据类型
     * @param <T> 类型参数
     * @return 元数据值
     */
    @SuppressWarnings("unchecked")
    public <T> T getMetadata(String key, Class<T> type) {
        Object value = getMetadata(key);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }
        return null;
    }

    /**
     * 检查是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return messageType != null && data != null && qualityInfo != null && qualityInfo.isValid();
    }

    /**
     * 获取数据年龄（从事件时间到处理时间的间隔）
     *
     * @return 数据年龄（毫秒）
     */
    public long getDataAgeMillis() {
        if (eventTimestamp == null || processingTimestamp == null) {
            return 0;
        }
        return java.time.Duration.between(eventTimestamp, processingTimestamp).toMillis();
    }

    /**
     * 数据源信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SourceInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /** 数据源名称 */
        @JsonProperty("source_name")
        private String sourceName;

        /** 数据源类型 */
        @JsonProperty("source_type")
        private String sourceType;

        /** Kafka 主题 */
        @JsonProperty("kafka_topic")
        private String kafkaTopic;

        /** Kafka 分区 */
        @JsonProperty("kafka_partition")
        private Integer kafkaPartition;

        /** Kafka 偏移量 */
        @JsonProperty("kafka_offset")
        private Long kafkaOffset;

        /** 数据源时间戳 */
        @JsonProperty("source_timestamp")
        private LocalDateTime sourceTimestamp;

        /**
         * 创建 Kafka 源信息
         *
         * @param topic Kafka 主题
         * @param partition Kafka 分区
         * @param offset Kafka 偏移量
         * @return 源信息
         */
        public static SourceInfo kafka(String topic, Integer partition, Long offset) {
            SourceInfo sourceInfo = new SourceInfo();
            sourceInfo.setSourceName("kafka");
            sourceInfo.setSourceType("kafka");
            sourceInfo.setKafkaTopic(topic);
            sourceInfo.setKafkaPartition(partition);
            sourceInfo.setKafkaOffset(offset);
            sourceInfo.setSourceTimestamp(LocalDateTime.now());
            return sourceInfo;
        }
    }

    /**
     * 数据质量信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QualityInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /** 数据完整性评分（0-100） */
        @JsonProperty("completeness_score")
        private Integer completenessScore;

        /** 数据准确性评分（0-100） */
        @JsonProperty("accuracy_score")
        private Integer accuracyScore;

        /** 数据一致性评分（0-100） */
        @JsonProperty("consistency_score")
        private Integer consistencyScore;

        /** 数据及时性评分（0-100） */
        @JsonProperty("timeliness_score")
        private Integer timelinessScore;

        /** 质量检查时间 */
        @JsonProperty("quality_check_time")
        private LocalDateTime qualityCheckTime;

        /** 质量问题列表 */
        @JsonProperty("quality_issues")
        private java.util.List<String> qualityIssues;

        /**
         * 创建默认质量信息
         *
         * @return 默认质量信息
         */
        public static QualityInfo createDefault() {
            QualityInfo qualityInfo = new QualityInfo();
            qualityInfo.setCompletenessScore(100);
            qualityInfo.setAccuracyScore(100);
            qualityInfo.setConsistencyScore(100);
            qualityInfo.setTimelinessScore(100);
            qualityInfo.setQualityCheckTime(LocalDateTime.now());
            qualityInfo.setQualityIssues(new java.util.ArrayList<>());
            return qualityInfo;
        }

        /**
         * 计算总体质量评分
         *
         * @return 总体质量评分
         */
        public double getOverallScore() {
            if (completenessScore == null || accuracyScore == null || 
                consistencyScore == null || timelinessScore == null) {
                return 0.0;
            }
            return (completenessScore + accuracyScore + consistencyScore + timelinessScore) / 4.0;
        }

        /**
         * 检查质量是否合格
         *
         * @return 是否合格
         */
        public boolean isValid() {
            return getOverallScore() >= 60.0; // 60分及格
        }

        /**
         * 添加质量问题
         *
         * @param issue 质量问题
         */
        public void addQualityIssue(String issue) {
            if (qualityIssues == null) {
                qualityIssues = new java.util.ArrayList<>();
            }
            qualityIssues.add(issue);
        }
    }
}
