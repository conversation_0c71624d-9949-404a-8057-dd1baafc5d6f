package com.geeksec.flink.common.metrics;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.metrics.*;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;

/**
 * 指标工具类
 * 提供 Flink 指标的创建、管理和操作功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class MetricUtils {

    private MetricUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 指标创建 ====================

    /**
     * 创建计数器指标
     * 
     * @param metricGroup 指标分组
     * @param name 指标名称
     * @return 计数器
     */
    public static Counter createCounter(MetricGroup metricGroup, String name) {
        try {
            return metricGroup.counter(name);
        } catch (Exception e) {
            log.warn("创建计数器指标失败: {}", name, e);
            return new SimpleCounter();
        }
    }

    /**
     * 创建仪表指标
     * 
     * @param metricGroup 指标分组
     * @param name 指标名称
     * @param gauge 仪表实现
     * @param <T> 值类型
     * @return 仪表
     */
    public static <T> Gauge<T> createGauge(MetricGroup metricGroup, String name, Gauge<T> gauge) {
        try {
            metricGroup.gauge(name, gauge);
            return gauge;
        } catch (Exception e) {
            log.warn("创建仪表指标失败: {}", name, e);
            return gauge;
        }
    }

    /**
     * 创建仪表指标（使用供应商）
     * 
     * @param metricGroup 指标分组
     * @param name 指标名称
     * @param supplier 值供应商
     * @param <T> 值类型
     * @return 仪表
     */
    public static <T> Gauge<T> createGauge(MetricGroup metricGroup, String name, Supplier<T> supplier) {
        Gauge<T> gauge = supplier::get;
        return createGauge(metricGroup, name, gauge);
    }

    /**
     * 创建直方图指标
     * 
     * @param metricGroup 指标分组
     * @param name 指标名称
     * @return 直方图
     */
    public static Histogram createHistogram(MetricGroup metricGroup, String name) {
        try {
            return metricGroup.histogram(name, new DropwizardHistogramWrapper(
                    new com.codahale.metrics.Histogram(new com.codahale.metrics.ExponentiallyDecayingReservoir())));
        } catch (Exception e) {
            log.warn("创建直方图指标失败: {}", name, e);
            return new SimpleHistogram();
        }
    }

    /**
     * 创建计量器指标
     * 
     * @param metricGroup 指标分组
     * @param name 指标名称
     * @return 计量器
     */
    public static Meter createMeter(MetricGroup metricGroup, String name) {
        try {
            return metricGroup.meter(name, new MeterView(createCounter(metricGroup, name + "_count")));
        } catch (Exception e) {
            log.warn("创建计量器指标失败: {}", name, e);
            return new SimpleMeter();
        }
    }

    // ==================== 业务指标创建 ====================

    /**
     * 创建处理指标集合
     * 
     * @param metricGroup 指标分组
     * @return 处理指标集合
     */
    public static ProcessingMetrics createProcessingMetrics(MetricGroup metricGroup) {
        return new ProcessingMetrics(
                createCounter(metricGroup, MetricConstants.COUNTER_TOTAL_RECORDS),
                createCounter(metricGroup, MetricConstants.COUNTER_SUCCESS_RECORDS),
                createCounter(metricGroup, MetricConstants.COUNTER_FAILED_RECORDS),
                createCounter(metricGroup, MetricConstants.COUNTER_SKIPPED_RECORDS),
                createHistogram(metricGroup, MetricConstants.HISTOGRAM_PROCESSING_LATENCY),
                createMeter(metricGroup, MetricConstants.METER_THROUGHPUT)
        );
    }

    /**
     * 创建缓存指标集合
     * 
     * @param metricGroup 指标分组
     * @param cache 缓存对象
     * @return 缓存指标集合
     */
    public static CacheMetrics createCacheMetrics(MetricGroup metricGroup, Object cache) {
        return new CacheMetrics(
                createCounter(metricGroup, MetricConstants.COUNTER_CACHE_HITS),
                createCounter(metricGroup, MetricConstants.COUNTER_CACHE_MISSES),
                createCounter(metricGroup, MetricConstants.COUNTER_CACHE_LOADS),
                createCounter(metricGroup, MetricConstants.COUNTER_CACHE_EVICTIONS),
                createGauge(metricGroup, MetricConstants.GAUGE_CACHE_SIZE, () -> getCacheSize(cache)),
                createGauge(metricGroup, MetricConstants.GAUGE_CACHE_HIT_RATE, () -> getCacheHitRate(cache)),
                createHistogram(metricGroup, MetricConstants.HISTOGRAM_CACHE_LOAD_TIME)
        );
    }

    /**
     * 创建错误指标集合
     * 
     * @param metricGroup 指标分组
     * @return 错误指标集合
     */
    public static ErrorMetrics createErrorMetrics(MetricGroup metricGroup) {
        return new ErrorMetrics(
                createCounter(metricGroup, MetricConstants.COUNTER_ERRORS),
                createCounter(metricGroup, MetricConstants.COUNTER_WARNINGS),
                createCounter(metricGroup, MetricConstants.COUNTER_RETRIES),
                createCounter(metricGroup, MetricConstants.COUNTER_SERIALIZATION_ERRORS),
                createCounter(metricGroup, MetricConstants.COUNTER_DESERIALIZATION_ERRORS),
                createCounter(metricGroup, MetricConstants.COUNTER_PARSING_ERRORS),
                createCounter(metricGroup, MetricConstants.COUNTER_VALIDATION_ERRORS),
                createCounter(metricGroup, MetricConstants.COUNTER_TIMEOUT_ERRORS),
                createCounter(metricGroup, MetricConstants.COUNTER_CONNECTION_ERRORS)
        );
    }

    // ==================== 指标操作 ====================

    /**
     * 安全地增加计数器
     * 
     * @param counter 计数器
     * @param value 增加值
     */
    public static void safeIncrement(Counter counter, long value) {
        if (counter != null) {
            try {
                counter.inc(value);
            } catch (Exception e) {
                log.debug("计数器增加失败", e);
            }
        }
    }

    /**
     * 安全地增加计数器（增加1）
     * 
     * @param counter 计数器
     */
    public static void safeIncrement(Counter counter) {
        safeIncrement(counter, 1L);
    }

    /**
     * 安全地更新直方图
     * 
     * @param histogram 直方图
     * @param value 值
     */
    public static void safeUpdate(Histogram histogram, long value) {
        if (histogram != null) {
            try {
                histogram.update(value);
            } catch (Exception e) {
                log.debug("直方图更新失败", e);
            }
        }
    }

    /**
     * 安全地标记计量器
     * 
     * @param meter 计量器
     * @param events 事件数
     */
    public static void safeMark(Meter meter, long events) {
        if (meter != null) {
            try {
                meter.markEvent(events);
            } catch (Exception e) {
                log.debug("计量器标记失败", e);
            }
        }
    }

    /**
     * 安全地标记计量器（标记1个事件）
     * 
     * @param meter 计量器
     */
    public static void safeMark(Meter meter) {
        safeMark(meter, 1L);
    }

    // ==================== 性能测量 ====================

    /**
     * 测量执行时间并更新直方图
     * 
     * @param histogram 直方图
     * @param runnable 可执行操作
     */
    public static void measureTime(Histogram histogram, Runnable runnable) {
        long startTime = System.currentTimeMillis();
        try {
            runnable.run();
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            safeUpdate(histogram, duration);
        }
    }

    /**
     * 测量执行时间并更新直方图（返回结果）
     * 
     * @param histogram 直方图
     * @param supplier 供应商
     * @param <T> 返回类型
     * @return 执行结果
     */
    public static <T> T measureTime(Histogram histogram, Supplier<T> supplier) {
        long startTime = System.currentTimeMillis();
        try {
            return supplier.get();
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            safeUpdate(histogram, duration);
        }
    }

    // ==================== 缓存相关方法 ====================

    /**
     * 获取缓存大小
     * 
     * @param cache 缓存对象
     * @return 缓存大小
     */
    private static Long getCacheSize(Object cache) {
        if (cache == null) {
            return 0L;
        }
        
        try {
            // 尝试 Caffeine Cache
            if (cache.getClass().getName().contains("caffeine")) {
                return (Long) cache.getClass().getMethod("estimatedSize").invoke(cache);
            }
            
            // 尝试 Guava Cache
            if (cache.getClass().getName().contains("guava")) {
                return (Long) cache.getClass().getMethod("size").invoke(cache);
            }
            
            // 尝试 Map
            if (cache instanceof java.util.Map) {
                return (long) ((java.util.Map<?, ?>) cache).size();
            }
        } catch (Exception e) {
            log.debug("获取缓存大小失败", e);
        }
        
        return 0L;
    }

    /**
     * 获取缓存命中率
     * 
     * @param cache 缓存对象
     * @return 缓存命中率
     */
    private static Double getCacheHitRate(Object cache) {
        if (cache == null) {
            return 0.0;
        }
        
        try {
            // 尝试 Caffeine Cache
            if (cache.getClass().getName().contains("caffeine")) {
                Object stats = cache.getClass().getMethod("stats").invoke(cache);
                return (Double) stats.getClass().getMethod("hitRate").invoke(stats);
            }
            
            // 尝试 Guava Cache
            if (cache.getClass().getName().contains("guava")) {
                Object stats = cache.getClass().getMethod("stats").invoke(cache);
                return (Double) stats.getClass().getMethod("hitRate").invoke(stats);
            }
        } catch (Exception e) {
            log.debug("获取缓存命中率失败", e);
        }
        
        return 0.0;
    }

    // ==================== 简单实现类 ====================

    /**
     * 简单计数器实现
     */
    private static class SimpleCounter implements Counter {
        private final AtomicLong count = new AtomicLong(0);

        @Override
        public void inc() {
            count.incrementAndGet();
        }

        @Override
        public void inc(long n) {
            count.addAndGet(n);
        }

        @Override
        public void dec() {
            count.decrementAndGet();
        }

        @Override
        public void dec(long n) {
            count.addAndGet(-n);
        }

        @Override
        public long getCount() {
            return count.get();
        }
    }

    /**
     * 简单直方图实现
     */
    private static class SimpleHistogram implements Histogram {
        private final AtomicLong count = new AtomicLong(0);
        private final AtomicLong sum = new AtomicLong(0);

        @Override
        public void update(long value) {
            count.incrementAndGet();
            sum.addAndGet(value);
        }

        @Override
        public long getCount() {
            return count.get();
        }

        @Override
        public HistogramStatistics getStatistics() {
            return new HistogramStatistics() {
                @Override
                public double getQuantile(double quantile) {
                    return 0.0;
                }

                @Override
                public long[] getValues() {
                    return new long[0];
                }

                @Override
                public int size() {
                    return (int) count.get();
                }

                @Override
                public double getMean() {
                    long c = count.get();
                    return c > 0 ? (double) sum.get() / c : 0.0;
                }

                @Override
                public double getStdDev() {
                    return 0.0;
                }

                @Override
                public long getMax() {
                    return 0;
                }

                @Override
                public long getMin() {
                    return 0;
                }
            };
        }
    }

    /**
     * 简单计量器实现
     */
    private static class SimpleMeter implements Meter {
        private final AtomicLong count = new AtomicLong(0);

        @Override
        public void markEvent() {
            count.incrementAndGet();
        }

        @Override
        public void markEvent(long n) {
            count.addAndGet(n);
        }

        @Override
        public double getRate() {
            return 0.0;
        }

        @Override
        public long getCount() {
            return count.get();
        }
    }

    // ==================== 指标集合类 ====================

    /**
     * 处理指标集合
     */
    public static class ProcessingMetrics {
        public final Counter totalRecords;
        public final Counter successRecords;
        public final Counter failedRecords;
        public final Counter skippedRecords;
        public final Histogram processingLatency;
        public final Meter throughput;

        public ProcessingMetrics(Counter totalRecords, Counter successRecords, Counter failedRecords,
                               Counter skippedRecords, Histogram processingLatency, Meter throughput) {
            this.totalRecords = totalRecords;
            this.successRecords = successRecords;
            this.failedRecords = failedRecords;
            this.skippedRecords = skippedRecords;
            this.processingLatency = processingLatency;
            this.throughput = throughput;
        }

        public void recordSuccess(long processingTime) {
            safeIncrement(totalRecords);
            safeIncrement(successRecords);
            safeUpdate(processingLatency, processingTime);
            safeMark(throughput);
        }

        public void recordFailure(long processingTime) {
            safeIncrement(totalRecords);
            safeIncrement(failedRecords);
            safeUpdate(processingLatency, processingTime);
        }

        public void recordSkipped() {
            safeIncrement(totalRecords);
            safeIncrement(skippedRecords);
        }
    }

    /**
     * 缓存指标集合
     */
    public static class CacheMetrics {
        public final Counter hits;
        public final Counter misses;
        public final Counter loads;
        public final Counter evictions;
        public final Gauge<Long> size;
        public final Gauge<Double> hitRate;
        public final Histogram loadTime;

        public CacheMetrics(Counter hits, Counter misses, Counter loads, Counter evictions,
                          Gauge<Long> size, Gauge<Double> hitRate, Histogram loadTime) {
            this.hits = hits;
            this.misses = misses;
            this.loads = loads;
            this.evictions = evictions;
            this.size = size;
            this.hitRate = hitRate;
            this.loadTime = loadTime;
        }

        public void recordHit() {
            safeIncrement(hits);
        }

        public void recordMiss() {
            safeIncrement(misses);
        }

        public void recordLoad(long loadTime) {
            safeIncrement(loads);
            safeUpdate(this.loadTime, loadTime);
        }

        public void recordEviction() {
            safeIncrement(evictions);
        }
    }

    /**
     * 错误指标集合
     */
    public static class ErrorMetrics {
        public final Counter errors;
        public final Counter warnings;
        public final Counter retries;
        public final Counter serializationErrors;
        public final Counter deserializationErrors;
        public final Counter parsingErrors;
        public final Counter validationErrors;
        public final Counter timeoutErrors;
        public final Counter connectionErrors;

        public ErrorMetrics(Counter errors, Counter warnings, Counter retries,
                          Counter serializationErrors, Counter deserializationErrors,
                          Counter parsingErrors, Counter validationErrors,
                          Counter timeoutErrors, Counter connectionErrors) {
            this.errors = errors;
            this.warnings = warnings;
            this.retries = retries;
            this.serializationErrors = serializationErrors;
            this.deserializationErrors = deserializationErrors;
            this.parsingErrors = parsingErrors;
            this.validationErrors = validationErrors;
            this.timeoutErrors = timeoutErrors;
            this.connectionErrors = connectionErrors;
        }

        public void recordError() {
            safeIncrement(errors);
        }

        public void recordWarning() {
            safeIncrement(warnings);
        }

        public void recordRetry() {
            safeIncrement(retries);
        }

        public void recordSerializationError() {
            safeIncrement(serializationErrors);
            safeIncrement(errors);
        }

        public void recordDeserializationError() {
            safeIncrement(deserializationErrors);
            safeIncrement(errors);
        }

        public void recordParsingError() {
            safeIncrement(parsingErrors);
            safeIncrement(errors);
        }

        public void recordValidationError() {
            safeIncrement(validationErrors);
            safeIncrement(errors);
        }

        public void recordTimeoutError() {
            safeIncrement(timeoutErrors);
            safeIncrement(errors);
        }

        public void recordConnectionError() {
            safeIncrement(connectionErrors);
            safeIncrement(errors);
        }
    }
}
