package com.geeksec.flink.common.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 告警类型枚举
 * 定义系统中所有告警的类型分类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum AlarmType {
    
    // ==================== 会话相关告警 ====================
    
    /** 会话标签告警 */
    SESSION_LABEL("SESSION_LABEL", "会话打标", "会话行为异常标记", AlarmCategory.SESSION),
    
    /** 异常会话告警 */
    ABNORMAL_SESSION("ABNORMAL_SESSION", "异常会话", "会话行为模式异常", AlarmCategory.SESSION),
    
    // ==================== 恶意软件相关告警 ====================
    
    /** 远控木马 */
    RAT("RAT", "远控木马", "远程访问木马检测", AlarmCategory.MALWARE),
    
    /** 挖矿病毒 */
    MINING("MINING", "挖矿病毒", "加密货币挖矿恶意软件", AlarmCategory.MALWARE),
    
    /** WebShell 通信 */
    WEBSHELL("WEBSHELL", "WebShell通信", "Web后门通信检测", AlarmCategory.MALWARE),
    
    // ==================== 攻击行为相关告警 ====================
    
    /** 扫描行为 */
    SCAN("SCAN", "扫描行为", "网络端口或服务扫描", AlarmCategory.ATTACK),
    
    /** 加密通道攻击 */
    ENCRYPTED_ATTACK("ENCRYPTED_ATTACK", "加密通道攻击行为", "通过加密通道进行的攻击", AlarmCategory.ATTACK),
    
    /** 特定协议攻击工具 */
    PROTOCOL_ATTACK_TOOL("PROTOCOL_ATTACK_TOOL", "特定协议攻击工具", "针对特定协议的攻击工具", AlarmCategory.ATTACK),
    
    // ==================== 违规行为相关告警 ====================
    
    /** 违规外联 */
    ILLEGAL_CONNECTION("ILLEGAL_CONNECTION", "违规外联", "违反安全策略的外部连接", AlarmCategory.VIOLATION),
    
    /** 未授权访问 */
    UNAUTHORIZED_ACCESS("UNAUTHORIZED_ACCESS", "未授权访问", "未经授权的系统访问", AlarmCategory.VIOLATION),
    
    // ==================== 隐蔽通信相关告警 ====================
    
    /** 加密隐蔽隧道通信 */
    ENCRYPTED_TUNNEL("ENCRYPTED_TUNNEL", "加密隐蔽隧道通信", "加密的隐蔽通信隧道", AlarmCategory.COVERT),
    
    /** DNS 隧道 */
    DNS_TUNNEL("DNS_TUNNEL", "DNS隧道", "通过DNS协议建立的隐蔽通道", AlarmCategory.COVERT),
    
    /** TCP 隧道 */
    TCP_TUNNEL("TCP_TUNNEL", "TCP隧道", "TCP协议隐蔽通信隧道", AlarmCategory.COVERT),
    
    /** HTTP 隧道 */
    HTTP_TUNNEL("HTTP_TUNNEL", "HTTP隧道", "HTTP协议隐蔽通信隧道", AlarmCategory.COVERT),
    
    /** ICMP 隧道 */
    ICMP_TUNNEL("ICMP_TUNNEL", "ICMP隧道", "ICMP协议隐蔽通信隧道", AlarmCategory.COVERT),
    
    /** SSL 隧道 */
    SSL_TUNNEL("SSL_TUNNEL", "SSL隧道", "SSL协议隐蔽通信隧道", AlarmCategory.COVERT),
    
    // ==================== 工具相关告警 ====================
    
    /** 黑客工具 */
    HACKING_TOOL("HACKING_TOOL", "黑客工具", "恶意黑客工具使用", AlarmCategory.TOOL),
    
    /** 加密工具 */
    ENCRYPTED_TOOL("ENCRYPTED_TOOL", "加密工具", "加密相关工具使用", AlarmCategory.TOOL),
    
    /** 未知远程控制工具 */
    UNKNOWN_REMOTE_CONTROL_TOOL("UNKNOWN_REMOTE_CONTROL_TOOL", "未知远程控制工具", "未识别的远程控制工具", AlarmCategory.TOOL),
    
    /** ToDesk 远程控制 */
    TODESK("TODESK", "ToDesk", "ToDesk远程控制软件", AlarmCategory.TOOL),
    
    // ==================== 证书相关告警 ====================
    
    /** 证书异常 */
    CERTIFICATE_ANOMALY("CERTIFICATE_ANOMALY", "证书异常", "SSL/TLS证书异常", AlarmCategory.CERTIFICATE),
    
    /** 证书过期 */
    CERTIFICATE_EXPIRED("CERTIFICATE_EXPIRED", "证书过期", "SSL/TLS证书已过期", AlarmCategory.CERTIFICATE),
    
    /** 自签名证书 */
    SELF_SIGNED_CERTIFICATE("SELF_SIGNED_CERTIFICATE", "自签名证书", "使用自签名证书", AlarmCategory.CERTIFICATE),
    
    // ==================== 其他告警 ====================
    
    /** 未知威胁 */
    UNKNOWN_THREAT("UNKNOWN_THREAT", "未知威胁", "未分类的安全威胁", AlarmCategory.OTHER);

    /** 告警类型代码 */
    private final String code;
    
    /** 显示名称 */
    private final String displayName;
    
    /** 描述信息 */
    private final String description;
    
    /** 告警分类 */
    private final AlarmCategory category;

    /** 代码映射 */
    private static final Map<String, AlarmType> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(AlarmType::getCode, Function.identity()));

    /** 显示名称映射 */
    private static final Map<String, AlarmType> DISPLAY_NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(AlarmType::getDisplayName, Function.identity()));

    /**
     * 构造函数
     * 
     * @param code 告警类型代码
     * @param displayName 显示名称
     * @param description 描述信息
     * @param category 告警分类
     */
    AlarmType(String code, String displayName, String description, AlarmCategory category) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
        this.category = category;
    }

    /**
     * 根据代码获取告警类型
     * 
     * @param code 告警类型代码
     * @return 告警类型，如果不存在则返回 null
     */
    public static AlarmType fromCode(String code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据显示名称获取告警类型
     * 
     * @param displayName 显示名称
     * @return 告警类型，如果不存在则返回 null
     */
    public static AlarmType fromDisplayName(String displayName) {
        return DISPLAY_NAME_MAP.get(displayName);
    }

    /**
     * 根据字符串获取告警类型（支持多种格式）
     * 
     * @param value 字符串值
     * @return 告警类型，如果不存在则返回 UNKNOWN_THREAT
     */
    public static AlarmType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return UNKNOWN_THREAT;
        }

        // 尝试按代码匹配
        AlarmType type = fromCode(value.toUpperCase());
        if (type != null) {
            return type;
        }

        // 尝试按显示名称匹配
        type = fromDisplayName(value);
        if (type != null) {
            return type;
        }

        // 默认返回未知威胁
        return UNKNOWN_THREAT;
    }

    /**
     * 获取指定分类的所有告警类型
     * 
     * @param category 告警分类
     * @return 告警类型列表
     */
    public static AlarmType[] getByCategory(AlarmCategory category) {
        return Arrays.stream(values())
                .filter(type -> type.getCategory() == category)
                .toArray(AlarmType[]::new);
    }

    /**
     * 判断是否为恶意软件相关告警
     * 
     * @return 是否为恶意软件告警
     */
    public boolean isMalwareRelated() {
        return category == AlarmCategory.MALWARE;
    }

    /**
     * 判断是否为攻击行为相关告警
     * 
     * @return 是否为攻击行为告警
     */
    public boolean isAttackRelated() {
        return category == AlarmCategory.ATTACK;
    }

    /**
     * 判断是否为隐蔽通信相关告警
     * 
     * @return 是否为隐蔽通信告警
     */
    public boolean isCovertRelated() {
        return category == AlarmCategory.COVERT;
    }

    /**
     * 判断是否为工具相关告警
     * 
     * @return 是否为工具告警
     */
    public boolean isToolRelated() {
        return category == AlarmCategory.TOOL;
    }

    /**
     * 判断是否为证书相关告警
     * 
     * @return 是否为证书告警
     */
    public boolean isCertificateRelated() {
        return category == AlarmCategory.CERTIFICATE;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * 告警分类枚举
     */
    @Getter
    public enum AlarmCategory {
        /** 会话相关 */
        SESSION("会话相关"),
        
        /** 恶意软件 */
        MALWARE("恶意软件"),
        
        /** 攻击行为 */
        ATTACK("攻击行为"),
        
        /** 违规行为 */
        VIOLATION("违规行为"),
        
        /** 隐蔽通信 */
        COVERT("隐蔽通信"),
        
        /** 工具相关 */
        TOOL("工具相关"),
        
        /** 证书相关 */
        CERTIFICATE("证书相关"),
        
        /** 其他 */
        OTHER("其他");

        /** 分类名称 */
        private final String name;

        /**
         * 构造函数
         * 
         * @param name 分类名称
         */
        AlarmCategory(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
}
