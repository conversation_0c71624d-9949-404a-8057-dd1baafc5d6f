package com.geeksec.flink.common.serialization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.geeksec.flink.common.exception.ErrorCode;
import com.geeksec.flink.common.exception.FlinkJobException;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 序列化工具类
 * 提供 JSON、Java 对象序列化等通用功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class SerializationUtils {

    private SerializationUtils() {
        // 工具类，禁止实例化
    }

    // ==================== ObjectMapper 缓存 ====================

    /** ObjectMapper 缓存 */
    private static final Map<String, ObjectMapper> MAPPER_CACHE = new ConcurrentHashMap<>();

    /** 默认 ObjectMapper */
    private static volatile ObjectMapper defaultMapper;

    /**
     * 获取默认的 ObjectMapper
     * 
     * @return ObjectMapper 实例
     */
    public static ObjectMapper getDefaultObjectMapper() {
        if (defaultMapper == null) {
            synchronized (SerializationUtils.class) {
                if (defaultMapper == null) {
                    defaultMapper = createDefaultObjectMapper();
                }
            }
        }
        return defaultMapper;
    }

    /**
     * 创建默认的 ObjectMapper
     * 
     * @return ObjectMapper 实例
     */
    private static ObjectMapper createDefaultObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 注册 Java 8 时间模块
        mapper.registerModule(new JavaTimeModule());
        
        // 序列化配置
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        
        // 反序列化配置
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.disable(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES);
        mapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        
        return mapper;
    }

    /**
     * 获取或创建指定配置的 ObjectMapper
     * 
     * @param config 配置键
     * @param factory ObjectMapper 工厂
     * @return ObjectMapper 实例
     */
    public static ObjectMapper getOrCreateObjectMapper(String config, ObjectMapperFactory factory) {
        return MAPPER_CACHE.computeIfAbsent(config, k -> factory.create());
    }

    /**
     * ObjectMapper 工厂接口
     */
    @FunctionalInterface
    public interface ObjectMapperFactory {
        ObjectMapper create();
    }

    // ==================== JSON 序列化 ====================

    /**
     * 将对象序列化为 JSON 字符串
     * 
     * @param obj 对象
     * @return JSON 字符串
     * @throws FlinkJobException 序列化失败时抛出
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            return getDefaultObjectMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new FlinkJobException(ErrorCode.JSON_SERIALIZATION_FAILED, e, 
                    "对象序列化为 JSON 失败: %s", obj.getClass().getSimpleName());
        }
    }

    /**
     * 将对象序列化为格式化的 JSON 字符串
     * 
     * @param obj 对象
     * @return 格式化的 JSON 字符串
     * @throws FlinkJobException 序列化失败时抛出
     */
    public static String toPrettyJson(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            ObjectMapper mapper = getOrCreateObjectMapper("pretty", () -> {
                ObjectMapper m = createDefaultObjectMapper();
                m.enable(SerializationFeature.INDENT_OUTPUT);
                return m;
            });
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new FlinkJobException(ErrorCode.JSON_SERIALIZATION_FAILED, e, 
                    "对象序列化为格式化 JSON 失败: %s", obj.getClass().getSimpleName());
        }
    }

    /**
     * 将对象序列化为 JSON 字节数组
     * 
     * @param obj 对象
     * @return JSON 字节数组
     * @throws FlinkJobException 序列化失败时抛出
     */
    public static byte[] toJsonBytes(Object obj) {
        if (obj == null) {
            return new byte[0];
        }
        
        try {
            return getDefaultObjectMapper().writeValueAsBytes(obj);
        } catch (JsonProcessingException e) {
            throw new FlinkJobException(ErrorCode.JSON_SERIALIZATION_FAILED, e, 
                    "对象序列化为 JSON 字节数组失败: %s", obj.getClass().getSimpleName());
        }
    }

    // ==================== JSON 反序列化 ====================

    /**
     * 将 JSON 字符串反序列化为对象
     * 
     * @param json JSON 字符串
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 反序列化后的对象
     * @throws FlinkJobException 反序列化失败时抛出
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return getDefaultObjectMapper().readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new FlinkJobException(ErrorCode.JSON_DESERIALIZATION_FAILED, e, 
                    "JSON 字符串反序列化失败: 目标类型=%s", clazz.getSimpleName());
        }
    }

    /**
     * 将 JSON 字符串反序列化为对象（使用 TypeReference）
     * 
     * @param json JSON 字符串
     * @param typeRef 类型引用
     * @param <T> 类型参数
     * @return 反序列化后的对象
     * @throws FlinkJobException 反序列化失败时抛出
     */
    public static <T> T fromJson(String json, TypeReference<T> typeRef) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return getDefaultObjectMapper().readValue(json, typeRef);
        } catch (JsonProcessingException e) {
            throw new FlinkJobException(ErrorCode.JSON_DESERIALIZATION_FAILED, e, 
                    "JSON 字符串反序列化失败: 目标类型=%s", typeRef.getType());
        }
    }

    /**
     * 将 JSON 字节数组反序列化为对象
     * 
     * @param jsonBytes JSON 字节数组
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 反序列化后的对象
     * @throws FlinkJobException 反序列化失败时抛出
     */
    public static <T> T fromJsonBytes(byte[] jsonBytes, Class<T> clazz) {
        if (jsonBytes == null || jsonBytes.length == 0) {
            return null;
        }
        
        try {
            return getDefaultObjectMapper().readValue(jsonBytes, clazz);
        } catch (IOException e) {
            throw new FlinkJobException(ErrorCode.JSON_DESERIALIZATION_FAILED, e, 
                    "JSON 字节数组反序列化失败: 目标类型=%s", clazz.getSimpleName());
        }
    }

    /**
     * 安全地将 JSON 字符串反序列化为对象
     * 
     * @param json JSON 字符串
     * @param clazz 目标类型
     * @param defaultValue 默认值
     * @param <T> 类型参数
     * @return 反序列化后的对象或默认值
     */
    public static <T> T fromJsonSafe(String json, Class<T> clazz, T defaultValue) {
        try {
            return fromJson(json, clazz);
        } catch (Exception e) {
            log.debug("JSON 反序列化失败，使用默认值: json={}, class={}", json, clazz.getSimpleName(), e);
            return defaultValue;
        }
    }

    // ==================== Java 对象序列化 ====================

    /**
     * 将对象序列化为字节数组
     * 
     * @param obj 对象
     * @return 字节数组
     * @throws FlinkJobException 序列化失败时抛出
     */
    public static byte[] serialize(Serializable obj) {
        if (obj == null) {
            return new byte[0];
        }
        
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ObjectOutputStream oos = new ObjectOutputStream(baos)) {
            oos.writeObject(obj);
            return baos.toByteArray();
        } catch (IOException e) {
            throw new FlinkJobException(ErrorCode.OBJECT_SERIALIZATION_FAILED, e, 
                    "对象序列化失败: %s", obj.getClass().getSimpleName());
        }
    }

    /**
     * 将字节数组反序列化为对象
     * 
     * @param bytes 字节数组
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 反序列化后的对象
     * @throws FlinkJobException 反序列化失败时抛出
     */
    @SuppressWarnings("unchecked")
    public static <T> T deserialize(byte[] bytes, Class<T> clazz) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        
        try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
             ObjectInputStream ois = new ObjectInputStream(bais)) {
            Object obj = ois.readObject();
            if (clazz.isInstance(obj)) {
                return (T) obj;
            } else {
                throw new FlinkJobException(ErrorCode.OBJECT_DESERIALIZATION_FAILED, 
                        "反序列化对象类型不匹配: 期望=%s, 实际=%s", 
                        clazz.getSimpleName(), obj.getClass().getSimpleName());
            }
        } catch (IOException | ClassNotFoundException e) {
            throw new FlinkJobException(ErrorCode.OBJECT_DESERIALIZATION_FAILED, e, 
                    "对象反序列化失败: 目标类型=%s", clazz.getSimpleName());
        }
    }

    // ==================== Base64 编码 ====================

    /**
     * 将对象序列化并进行 Base64 编码
     * 
     * @param obj 对象
     * @return Base64 编码的字符串
     * @throws FlinkJobException 序列化失败时抛出
     */
    public static String serializeToBase64(Serializable obj) {
        byte[] bytes = serialize(obj);
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 将 Base64 编码的字符串反序列化为对象
     * 
     * @param base64 Base64 编码的字符串
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 反序列化后的对象
     * @throws FlinkJobException 反序列化失败时抛出
     */
    public static <T> T deserializeFromBase64(String base64, Class<T> clazz) {
        if (base64 == null || base64.trim().isEmpty()) {
            return null;
        }
        
        try {
            byte[] bytes = Base64.getDecoder().decode(base64);
            return deserialize(bytes, clazz);
        } catch (IllegalArgumentException e) {
            throw new FlinkJobException(ErrorCode.OBJECT_DESERIALIZATION_FAILED, e, 
                    "Base64 解码失败: %s", base64);
        }
    }

    // ==================== 字符串编码 ====================

    /**
     * 将字符串转换为 UTF-8 字节数组
     * 
     * @param str 字符串
     * @return UTF-8 字节数组
     */
    public static byte[] toUtf8Bytes(String str) {
        if (str == null) {
            return new byte[0];
        }
        return str.getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 将 UTF-8 字节数组转换为字符串
     * 
     * @param bytes UTF-8 字节数组
     * @return 字符串
     */
    public static String fromUtf8Bytes(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        return new String(bytes, StandardCharsets.UTF_8);
    }

    // ==================== 深拷贝 ====================

    /**
     * 通过序列化进行深拷贝
     * 
     * @param obj 原对象
     * @param <T> 类型参数
     * @return 深拷贝后的对象
     * @throws FlinkJobException 拷贝失败时抛出
     */
    @SuppressWarnings("unchecked")
    public static <T extends Serializable> T deepCopy(T obj) {
        if (obj == null) {
            return null;
        }
        
        byte[] bytes = serialize(obj);
        return (T) deserialize(bytes, obj.getClass());
    }

    /**
     * 通过 JSON 进行深拷贝
     * 
     * @param obj 原对象
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 深拷贝后的对象
     * @throws FlinkJobException 拷贝失败时抛出
     */
    public static <T> T deepCopyViaJson(T obj, Class<T> clazz) {
        if (obj == null) {
            return null;
        }
        
        String json = toJson(obj);
        return fromJson(json, clazz);
    }

    // ==================== 工具方法 ====================

    /**
     * 检查对象是否可序列化
     * 
     * @param obj 对象
     * @return 是否可序列化
     */
    public static boolean isSerializable(Object obj) {
        return obj instanceof Serializable;
    }

    /**
     * 计算序列化后的大小
     * 
     * @param obj 对象
     * @return 序列化后的字节数
     */
    public static long getSerializedSize(Serializable obj) {
        if (obj == null) {
            return 0;
        }
        
        try {
            byte[] bytes = serialize(obj);
            return bytes.length;
        } catch (Exception e) {
            log.debug("计算序列化大小失败", e);
            return -1;
        }
    }

    /**
     * 计算 JSON 序列化后的大小
     * 
     * @param obj 对象
     * @return JSON 序列化后的字节数
     */
    public static long getJsonSerializedSize(Object obj) {
        if (obj == null) {
            return 0;
        }
        
        try {
            byte[] bytes = toJsonBytes(obj);
            return bytes.length;
        } catch (Exception e) {
            log.debug("计算 JSON 序列化大小失败", e);
            return -1;
        }
    }
}
