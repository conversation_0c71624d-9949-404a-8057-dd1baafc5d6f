package com.geeksec.flink.common.constants;

/**
 * 字段常量类
 * 定义系统中所有数据字段的常量名称
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public final class FieldConstants {

    private FieldConstants() {
        // 工具类，禁止实例化
    }

    // ==================== 基础字段 ====================

    /** 唯一标识 */
    public static final String FIELD_ID = "id";

    /** 会话ID */
    public static final String FIELD_SESSION_ID = "session_id";

    /** 时间戳 */
    public static final String FIELD_TIMESTAMP = "timestamp";

    /** 创建时间 */
    public static final String FIELD_CREATED_AT = "created_at";

    /** 更新时间 */
    public static final String FIELD_UPDATED_AT = "updated_at";

    // ==================== 网络字段 ====================

    /** 源IP地址 */
    public static final String FIELD_SRC_IP = "src_ip";

    /** 目标IP地址 */
    public static final String FIELD_DST_IP = "dst_ip";

    /** 源端口 */
    public static final String FIELD_SRC_PORT = "src_port";

    /** 目标端口 */
    public static final String FIELD_DST_PORT = "dst_port";

    /** 协议类型 */
    public static final String FIELD_PROTOCOL = "protocol";

    /** 协议版本 */
    public static final String FIELD_PROTOCOL_VERSION = "protocol_version";

    /** 源MAC地址 */
    public static final String FIELD_SRC_MAC = "src_mac";

    /** 目标MAC地址 */
    public static final String FIELD_DST_MAC = "dst_mac";

    /** VLAN ID */
    public static final String FIELD_VLAN_ID = "vlan_id";

    // ==================== 流量统计字段 ====================

    /** 字节数 */
    public static final String FIELD_BYTES = "bytes";

    /** 包数 */
    public static final String FIELD_PACKETS = "packets";

    /** 源字节数 */
    public static final String FIELD_SRC_BYTES = "src_bytes";

    /** 目标字节数 */
    public static final String FIELD_DST_BYTES = "dst_bytes";

    /** 源包数 */
    public static final String FIELD_SRC_PACKETS = "src_packets";

    /** 目标包数 */
    public static final String FIELD_DST_PACKETS = "dst_packets";

    /** 持续时间 */
    public static final String FIELD_DURATION = "duration";

    /** 开始时间 */
    public static final String FIELD_START_TIME = "start_time";

    /** 结束时间 */
    public static final String FIELD_END_TIME = "end_time";

    // ==================== HTTP 字段 ====================

    /** HTTP 方法 */
    public static final String FIELD_HTTP_METHOD = "http_method";

    /** HTTP 状态码 */
    public static final String FIELD_HTTP_STATUS_CODE = "http_status_code";

    /** HTTP 用户代理 */
    public static final String FIELD_HTTP_USER_AGENT = "http_user_agent";

    /** HTTP 主机 */
    public static final String FIELD_HTTP_HOST = "http_host";

    /** HTTP URI */
    public static final String FIELD_HTTP_URI = "http_uri";

    /** HTTP 引用页 */
    public static final String FIELD_HTTP_REFERER = "http_referer";

    /** HTTP 内容类型 */
    public static final String FIELD_HTTP_CONTENT_TYPE = "http_content_type";

    /** HTTP 内容长度 */
    public static final String FIELD_HTTP_CONTENT_LENGTH = "http_content_length";

    // ==================== DNS 字段 ====================

    /** DNS 查询名称 */
    public static final String FIELD_DNS_QUERY_NAME = "dns_query_name";

    /** DNS 查询类型 */
    public static final String FIELD_DNS_QUERY_TYPE = "dns_query_type";

    /** DNS 响应代码 */
    public static final String FIELD_DNS_RESPONSE_CODE = "dns_response_code";

    /** DNS 答案 */
    public static final String FIELD_DNS_ANSWERS = "dns_answers";

    /** DNS TTL */
    public static final String FIELD_DNS_TTL = "dns_ttl";

    // ==================== SSL/TLS 字段 ====================

    /** SSL 版本 */
    public static final String FIELD_SSL_VERSION = "ssl_version";

    /** SSL 密码套件 */
    public static final String FIELD_SSL_CIPHER_SUITE = "ssl_cipher_suite";

    /** SSL 服务器名称 */
    public static final String FIELD_SSL_SERVER_NAME = "ssl_server_name";

    /** SSL 证书主题 */
    public static final String FIELD_SSL_CERT_SUBJECT = "ssl_cert_subject";

    /** SSL 证书颁发者 */
    public static final String FIELD_SSL_CERT_ISSUER = "ssl_cert_issuer";

    /** SSL 证书序列号 */
    public static final String FIELD_SSL_CERT_SERIAL = "ssl_cert_serial";

    /** SSL 证书指纹 */
    public static final String FIELD_SSL_CERT_FINGERPRINT = "ssl_cert_fingerprint";

    /** SSL 证书有效期开始 */
    public static final String FIELD_SSL_CERT_NOT_BEFORE = "ssl_cert_not_before";

    /** SSL 证书有效期结束 */
    public static final String FIELD_SSL_CERT_NOT_AFTER = "ssl_cert_not_after";

    // ==================== 指纹字段 ====================

    /** TCP 客户端指纹 */
    public static final String FIELD_TCP_CLIENT_FINGERPRINT = "tcp_client_fingerprint";

    /** TCP 服务端指纹 */
    public static final String FIELD_TCP_SERVER_FINGERPRINT = "tcp_server_fingerprint";

    /** HTTP 客户端指纹 */
    public static final String FIELD_HTTP_CLIENT_FINGERPRINT = "http_client_fingerprint";

    /** HTTP 服务端指纹 */
    public static final String FIELD_HTTP_SERVER_FINGERPRINT = "http_server_fingerprint";

    /** SSL 客户端指纹 */
    public static final String FIELD_SSL_CLIENT_FINGERPRINT = "ssl_client_fingerprint";

    /** SSL 服务端指纹 */
    public static final String FIELD_SSL_SERVER_FINGERPRINT = "ssl_server_fingerprint";

    // ==================== 告警字段 ====================

    /** 告警ID */
    public static final String FIELD_ALARM_ID = "alarm_id";

    /** 告警名称 */
    public static final String FIELD_ALARM_NAME = "alarm_name";

    /** 告警类型 */
    public static final String FIELD_ALARM_TYPE = "alarm_type";

    /** 告警级别 */
    public static final String FIELD_ALARM_LEVEL = "alarm_level";

    /** 告警状态 */
    public static final String FIELD_ALARM_STATUS = "alarm_status";

    /** 告警描述 */
    public static final String FIELD_ALARM_DESCRIPTION = "alarm_description";

    /** 威胁类型 */
    public static final String FIELD_THREAT_TYPE = "threat_type";

    /** 威胁评分 */
    public static final String FIELD_THREAT_SCORE = "threat_score";

    // ==================== 地理位置字段 ====================

    /** 源IP国家 */
    public static final String FIELD_SRC_COUNTRY = "src_country";

    /** 目标IP国家 */
    public static final String FIELD_DST_COUNTRY = "dst_country";

    /** 源IP城市 */
    public static final String FIELD_SRC_CITY = "src_city";

    /** 目标IP城市 */
    public static final String FIELD_DST_CITY = "dst_city";

    /** 源IP纬度 */
    public static final String FIELD_SRC_LATITUDE = "src_latitude";

    /** 目标IP纬度 */
    public static final String FIELD_DST_LATITUDE = "dst_latitude";

    /** 源IP经度 */
    public static final String FIELD_SRC_LONGITUDE = "src_longitude";

    /** 目标IP经度 */
    public static final String FIELD_DST_LONGITUDE = "dst_longitude";

    // ==================== ASN 字段 ====================

    /** 源IP ASN */
    public static final String FIELD_SRC_ASN = "src_asn";

    /** 目标IP ASN */
    public static final String FIELD_DST_ASN = "dst_asn";

    /** 源IP ASN 组织 */
    public static final String FIELD_SRC_ASN_ORG = "src_asn_org";

    /** 目标IP ASN 组织 */
    public static final String FIELD_DST_ASN_ORG = "dst_asn_org";

    // ==================== 标签字段 ====================

    /** 标签 */
    public static final String FIELD_LABELS = "labels";

    /** 标签代码 */
    public static final String FIELD_LABEL_CODE = "label_code";

    /** 标签名称 */
    public static final String FIELD_LABEL_NAME = "label_name";

    /** 标签类型 */
    public static final String FIELD_LABEL_TYPE = "label_type";

    /** 标签值 */
    public static final String FIELD_LABEL_VALUE = "label_value";

    /** 标签置信度 */
    public static final String FIELD_LABEL_CONFIDENCE = "label_confidence";

    // ==================== 设备识别字段 ====================

    /** 操作系统 */
    public static final String FIELD_OS = "os";

    /** 操作系统版本 */
    public static final String FIELD_OS_VERSION = "os_version";

    /** 应用程序 */
    public static final String FIELD_APPLICATION = "application";

    /** 应用程序版本 */
    public static final String FIELD_APPLICATION_VERSION = "application_version";

    /** 设备类型 */
    public static final String FIELD_DEVICE_TYPE = "device_type";

    /** 设备厂商 */
    public static final String FIELD_DEVICE_VENDOR = "device_vendor";

    // ==================== 处理状态字段 ====================

    /** 处理状态 */
    public static final String FIELD_PROCESSING_STATUS = "processing_status";

    /** 处理时间 */
    public static final String FIELD_PROCESSING_TIME = "processing_time";

    /** 错误信息 */
    public static final String FIELD_ERROR_MESSAGE = "error_message";

    /** 重试次数 */
    public static final String FIELD_RETRY_COUNT = "retry_count";

    // ==================== 扩展字段 ====================

    /** 扩展数据 */
    public static final String FIELD_EXTRA_DATA = "extra_data";

    /** 元数据 */
    public static final String FIELD_METADATA = "metadata";

    /** 原始数据 */
    public static final String FIELD_RAW_DATA = "raw_data";

    /** 数据版本 */
    public static final String FIELD_DATA_VERSION = "data_version";

    /** 数据源 */
    public static final String FIELD_DATA_SOURCE = "data_source";

    // ==================== 字段验证方法 ====================

    /**
     * 检查字段名是否为基础网络字段
     * 
     * @param fieldName 字段名
     * @return 是否为基础网络字段
     */
    public static boolean isBasicNetworkField(String fieldName) {
        return FIELD_SRC_IP.equals(fieldName) || FIELD_DST_IP.equals(fieldName) ||
               FIELD_SRC_PORT.equals(fieldName) || FIELD_DST_PORT.equals(fieldName) ||
               FIELD_PROTOCOL.equals(fieldName);
    }

    /**
     * 检查字段名是否为统计字段
     * 
     * @param fieldName 字段名
     * @return 是否为统计字段
     */
    public static boolean isStatisticsField(String fieldName) {
        return FIELD_BYTES.equals(fieldName) || FIELD_PACKETS.equals(fieldName) ||
               FIELD_SRC_BYTES.equals(fieldName) || FIELD_DST_BYTES.equals(fieldName) ||
               FIELD_SRC_PACKETS.equals(fieldName) || FIELD_DST_PACKETS.equals(fieldName) ||
               FIELD_DURATION.equals(fieldName);
    }

    /**
     * 检查字段名是否为时间字段
     * 
     * @param fieldName 字段名
     * @return 是否为时间字段
     */
    public static boolean isTimeField(String fieldName) {
        return FIELD_TIMESTAMP.equals(fieldName) || FIELD_START_TIME.equals(fieldName) ||
               FIELD_END_TIME.equals(fieldName) || FIELD_CREATED_AT.equals(fieldName) ||
               FIELD_UPDATED_AT.equals(fieldName) || FIELD_PROCESSING_TIME.equals(fieldName);
    }

    /**
     * 检查字段名是否为指纹字段
     * 
     * @param fieldName 字段名
     * @return 是否为指纹字段
     */
    public static boolean isFingerprintField(String fieldName) {
        return FIELD_TCP_CLIENT_FINGERPRINT.equals(fieldName) || FIELD_TCP_SERVER_FINGERPRINT.equals(fieldName) ||
               FIELD_HTTP_CLIENT_FINGERPRINT.equals(fieldName) || FIELD_HTTP_SERVER_FINGERPRINT.equals(fieldName) ||
               FIELD_SSL_CLIENT_FINGERPRINT.equals(fieldName) || FIELD_SSL_SERVER_FINGERPRINT.equals(fieldName);
    }
}
