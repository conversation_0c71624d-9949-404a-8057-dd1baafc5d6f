package com.geeksec.flink.common.database.config;

import com.geeksec.flink.common.constants.FlinkConfigConstants;
import lombok.Data;
import org.apache.flink.api.java.utils.ParameterTool;

import java.io.Serializable;
import java.util.Properties;

/**
 * Redis 配置类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
public class RedisConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主机地址 */
    private String host = FlinkConfigConstants.DEFAULT_REDIS_HOST;

    /** 端口号 */
    private int port = FlinkConfigConstants.DEFAULT_REDIS_PORT;

    /** 密码 */
    private String password;

    /** 数据库索引 */
    private int database = FlinkConfigConstants.DEFAULT_REDIS_DATABASE;

    /** 连接超时时间（毫秒） */
    private int connectionTimeout = FlinkConfigConstants.DEFAULT_REDIS_CONNECTION_TIMEOUT;

    /** Socket 超时时间（毫秒） */
    private int socketTimeout = FlinkConfigConstants.DEFAULT_REDIS_SOCKET_TIMEOUT;

    /** 最大连接数 */
    private int maxTotal = 20;

    /** 最大空闲连接数 */
    private int maxIdle = 10;

    /** 最小空闲连接数 */
    private int minIdle = 2;

    /** 连接池最大等待时间（毫秒） */
    private long maxWaitMillis = 10000L;

    /** 是否在借用连接时测试连接 */
    private boolean testOnBorrow = true;

    /** 是否在归还连接时测试连接 */
    private boolean testOnReturn = true;

    /** 是否在空闲时测试连接 */
    private boolean testWhileIdle = true;

    /** 空闲连接检测间隔（毫秒） */
    private long timeBetweenEvictionRunsMillis = 30000L;

    /** 连接最小空闲时间（毫秒） */
    private long minEvictableIdleTimeMillis = 60000L;

    /** 每次检测的连接数 */
    private int numTestsPerEvictionRun = 3;

    /** 是否启用 JMX 监控 */
    private boolean jmxEnabled = false;

    /** JMX 名称前缀 */
    private String jmxNamePrefix = "redis-pool";

    /** 缓存键前缀 */
    private String keyPrefix = "";

    /** 缓存过期时间（秒），-1表示不过期 */
    private int defaultExpireSeconds = -1;

    /**
     * 默认构造函数
     */
    public RedisConfig() {
        // 使用默认值
    }

    /**
     * 从 ParameterTool 创建配置
     * 
     * @param parameterTool 参数工具
     * @return Redis 配置
     */
    public static RedisConfig fromParameterTool(ParameterTool parameterTool) {
        RedisConfig config = new RedisConfig();
        config.loadFromParameterTool(parameterTool);
        return config;
    }

    /**
     * 从 ParameterTool 加载配置
     * 
     * @param parameterTool 参数工具
     */
    public void loadFromParameterTool(ParameterTool parameterTool) {
        this.host = parameterTool.get(FlinkConfigConstants.REDIS_HOST, this.host);
        this.port = parameterTool.getInt(FlinkConfigConstants.REDIS_PORT, this.port);
        this.password = parameterTool.get(FlinkConfigConstants.REDIS_PASSWORD, this.password);
        this.database = parameterTool.getInt(FlinkConfigConstants.REDIS_DATABASE, this.database);
        this.connectionTimeout = parameterTool.getInt(FlinkConfigConstants.REDIS_CONNECTION_TIMEOUT, this.connectionTimeout);
        this.socketTimeout = parameterTool.getInt(FlinkConfigConstants.REDIS_SOCKET_TIMEOUT, this.socketTimeout);

        // 连接池配置
        this.maxTotal = parameterTool.getInt("redis.pool.max.total", this.maxTotal);
        this.maxIdle = parameterTool.getInt("redis.pool.max.idle", this.maxIdle);
        this.minIdle = parameterTool.getInt("redis.pool.min.idle", this.minIdle);
        this.maxWaitMillis = parameterTool.getLong("redis.pool.max.wait.millis", this.maxWaitMillis);
        this.testOnBorrow = parameterTool.getBoolean("redis.pool.test.on.borrow", this.testOnBorrow);
        this.testOnReturn = parameterTool.getBoolean("redis.pool.test.on.return", this.testOnReturn);
        this.testWhileIdle = parameterTool.getBoolean("redis.pool.test.while.idle", this.testWhileIdle);
        this.timeBetweenEvictionRunsMillis = parameterTool.getLong("redis.pool.time.between.eviction.runs.millis", this.timeBetweenEvictionRunsMillis);
        this.minEvictableIdleTimeMillis = parameterTool.getLong("redis.pool.min.evictable.idle.time.millis", this.minEvictableIdleTimeMillis);
        this.numTestsPerEvictionRun = parameterTool.getInt("redis.pool.num.tests.per.eviction.run", this.numTestsPerEvictionRun);

        // JMX 配置
        this.jmxEnabled = parameterTool.getBoolean("redis.jmx.enabled", this.jmxEnabled);
        this.jmxNamePrefix = parameterTool.get("redis.jmx.name.prefix", this.jmxNamePrefix);

        // 缓存配置
        this.keyPrefix = parameterTool.get("redis.key.prefix", this.keyPrefix);
        this.defaultExpireSeconds = parameterTool.getInt("redis.default.expire.seconds", this.defaultExpireSeconds);
    }

    /**
     * 转换为 Properties 对象
     * 
     * @return Properties 对象
     */
    public Properties toProperties() {
        Properties props = new Properties();
        props.setProperty("redis.host", host);
        props.setProperty("redis.port", String.valueOf(port));
        if (password != null && !password.trim().isEmpty()) {
            props.setProperty("redis.password", password);
        }
        props.setProperty("redis.database", String.valueOf(database));
        props.setProperty("redis.timeout", String.valueOf(connectionTimeout));
        props.setProperty("redis.pool.max-total", String.valueOf(maxTotal));
        props.setProperty("redis.pool.max-idle", String.valueOf(maxIdle));
        props.setProperty("redis.pool.min-idle", String.valueOf(minIdle));
        props.setProperty("redis.pool.max-wait", String.valueOf(maxWaitMillis));
        return props;
    }

    /**
     * 验证配置的有效性
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return host != null && !host.trim().isEmpty() &&
               port > 0 && port <= 65535 &&
               database >= 0 &&
               connectionTimeout > 0 &&
               socketTimeout > 0 &&
               maxTotal > 0 &&
               maxIdle >= 0 &&
               minIdle >= 0 &&
               maxIdle >= minIdle &&
               maxWaitMillis > 0 &&
               timeBetweenEvictionRunsMillis > 0 &&
               minEvictableIdleTimeMillis > 0 &&
               numTestsPerEvictionRun > 0;
    }

    /**
     * 获取连接字符串
     * 
     * @return 连接字符串
     */
    public String getConnectionString() {
        if (password != null && !password.trim().isEmpty()) {
            return String.format("redis://:%s@%s:%d/%d", password, host, port, database);
        } else {
            return String.format("redis://%s:%d/%d", host, port, database);
        }
    }

    /**
     * 创建默认配置
     * 
     * @return 默认 Redis 配置
     */
    public static RedisConfig createDefault() {
        return new RedisConfig();
    }

    /**
     * 创建用于指定数据库的配置
     * 
     * @param database 数据库索引
     * @return Redis 配置
     */
    public static RedisConfig forDatabase(int database) {
        RedisConfig config = new RedisConfig();
        config.setDatabase(database);
        return config;
    }

    /**
     * 创建带密码的配置
     * 
     * @param password 密码
     * @return Redis 配置
     */
    public static RedisConfig withPassword(String password) {
        RedisConfig config = new RedisConfig();
        config.setPassword(password);
        return config;
    }

    /**
     * 创建带键前缀的配置
     * 
     * @param keyPrefix 键前缀
     * @return Redis 配置
     */
    public static RedisConfig withKeyPrefix(String keyPrefix) {
        RedisConfig config = new RedisConfig();
        config.setKeyPrefix(keyPrefix);
        return config;
    }

    /**
     * 获取带前缀的键名
     * 
     * @param key 原始键名
     * @return 带前缀的键名
     */
    public String getPrefixedKey(String key) {
        if (keyPrefix == null || keyPrefix.trim().isEmpty()) {
            return key;
        }
        return keyPrefix + key;
    }

    @Override
    public String toString() {
        return String.format("RedisConfig{host='%s', port=%d, database=%d, keyPrefix='%s'}",
                host, port, database, keyPrefix);
    }
}
