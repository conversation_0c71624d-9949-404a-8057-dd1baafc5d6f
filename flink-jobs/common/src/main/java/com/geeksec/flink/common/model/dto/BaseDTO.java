package com.geeksec.flink.common.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据传输对象基类
 * 提供所有 DTO 的通用属性和方法
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class BaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 唯一标识 */
    @JsonProperty("id")
    private String id;

    /** 创建时间 */
    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /** 更新时间 */
    @JsonProperty("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /** 版本号（用于乐观锁） */
    @JsonProperty("version")
    private Long version;

    /** 是否已删除 */
    @JsonProperty("deleted")
    private Boolean deleted = false;

    /** 扩展属性 */
    @JsonProperty("extra_data")
    private java.util.Map<String, Object> extraData;

    /**
     * 默认构造函数
     */
    protected BaseDTO() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.version = 1L;
    }

    /**
     * 带ID的构造函数
     * 
     * @param id 唯一标识
     */
    protected BaseDTO(String id) {
        this();
        this.id = id;
    }

    /**
     * 更新时间戳
     */
    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
        if (this.version != null) {
            this.version++;
        }
    }

    /**
     * 标记为已删除
     */
    public void markAsDeleted() {
        this.deleted = true;
        updateTimestamp();
    }

    /**
     * 添加扩展属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    public void addExtraData(String key, Object value) {
        if (this.extraData == null) {
            this.extraData = new java.util.HashMap<>();
        }
        this.extraData.put(key, value);
        updateTimestamp();
    }

    /**
     * 获取扩展属性
     * 
     * @param key 属性键
     * @return 属性值
     */
    public Object getExtraData(String key) {
        return this.extraData != null ? this.extraData.get(key) : null;
    }

    /**
     * 获取扩展属性（指定类型）
     * 
     * @param key 属性键
     * @param type 属性类型
     * @param <T> 类型参数
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtraData(String key, Class<T> type) {
        Object value = getExtraData(key);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }
        return null;
    }

    /**
     * 移除扩展属性
     * 
     * @param key 属性键
     * @return 被移除的属性值
     */
    public Object removeExtraData(String key) {
        if (this.extraData != null) {
            Object removed = this.extraData.remove(key);
            if (removed != null) {
                updateTimestamp();
            }
            return removed;
        }
        return null;
    }

    /**
     * 检查是否有扩展属性
     * 
     * @param key 属性键
     * @return 是否存在该属性
     */
    public boolean hasExtraData(String key) {
        return this.extraData != null && this.extraData.containsKey(key);
    }

    /**
     * 清空扩展属性
     */
    public void clearExtraData() {
        if (this.extraData != null && !this.extraData.isEmpty()) {
            this.extraData.clear();
            updateTimestamp();
        }
    }

    /**
     * 检查对象是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return id != null && !id.trim().isEmpty() && !Boolean.TRUE.equals(deleted);
    }

    /**
     * 检查对象是否已删除
     * 
     * @return 是否已删除
     */
    public boolean isDeleted() {
        return Boolean.TRUE.equals(deleted);
    }

    /**
     * 获取对象年龄（从创建到现在的时间）
     * 
     * @return 年龄（秒）
     */
    public long getAgeInSeconds() {
        if (createdAt == null) {
            return 0;
        }
        return java.time.Duration.between(createdAt, LocalDateTime.now()).getSeconds();
    }

    /**
     * 获取最后更新时间间隔
     * 
     * @return 时间间隔（秒）
     */
    public long getLastUpdateAgeInSeconds() {
        if (updatedAt == null) {
            return 0;
        }
        return java.time.Duration.between(updatedAt, LocalDateTime.now()).getSeconds();
    }

    /**
     * 复制基础属性到另一个对象
     * 
     * @param target 目标对象
     */
    public void copyBaseTo(BaseDTO target) {
        if (target != null) {
            target.setId(this.id);
            target.setCreatedAt(this.createdAt);
            target.setUpdatedAt(this.updatedAt);
            target.setVersion(this.version);
            target.setDeleted(this.deleted);
            if (this.extraData != null) {
                target.setExtraData(new java.util.HashMap<>(this.extraData));
            }
        }
    }

    /**
     * 从另一个对象复制基础属性
     * 
     * @param source 源对象
     */
    public void copyBaseFrom(BaseDTO source) {
        if (source != null) {
            this.id = source.getId();
            this.createdAt = source.getCreatedAt();
            this.updatedAt = source.getUpdatedAt();
            this.version = source.getVersion();
            this.deleted = source.getDeleted();
            if (source.getExtraData() != null) {
                this.extraData = new java.util.HashMap<>(source.getExtraData());
            }
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        BaseDTO baseDTO = (BaseDTO) obj;
        return id != null && id.equals(baseDTO.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return String.format("%s{id='%s', createdAt=%s, updatedAt=%s, version=%d, deleted=%s}",
                getClass().getSimpleName(), id, createdAt, updatedAt, version, deleted);
    }
}
