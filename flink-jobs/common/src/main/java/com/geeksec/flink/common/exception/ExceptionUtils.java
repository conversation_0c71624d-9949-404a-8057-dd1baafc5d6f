package com.geeksec.flink.common.exception;

import lombok.extern.slf4j.Slf4j;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 异常处理工具类
 * 提供异常处理、转换和分析的通用功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class ExceptionUtils {

    private ExceptionUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 异常信息提取 ====================

    /**
     * 获取异常的完整堆栈跟踪字符串
     * 
     * @param throwable 异常对象
     * @return 堆栈跟踪字符串
     */
    public static String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return "";
        }
        
        try (StringWriter sw = new StringWriter();
             PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        } catch (Exception e) {
            log.warn("获取堆栈跟踪失败", e);
            return throwable.toString();
        }
    }

    /**
     * 获取异常的根本原因
     * 
     * @param throwable 异常对象
     * @return 根本原因异常
     */
    public static Throwable getRootCause(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        
        Throwable rootCause = throwable;
        while (rootCause.getCause() != null && rootCause.getCause() != rootCause) {
            rootCause = rootCause.getCause();
        }
        return rootCause;
    }

    /**
     * 获取异常链中的所有异常
     * 
     * @param throwable 异常对象
     * @return 异常列表
     */
    public static List<Throwable> getExceptionChain(Throwable throwable) {
        List<Throwable> chain = new ArrayList<>();
        Throwable current = throwable;
        
        while (current != null && !chain.contains(current)) {
            chain.add(current);
            current = current.getCause();
        }
        
        return chain;
    }

    /**
     * 获取异常的简短描述
     * 
     * @param throwable 异常对象
     * @return 简短描述
     */
    public static String getShortDescription(Throwable throwable) {
        if (throwable == null) {
            return "null";
        }
        
        String className = throwable.getClass().getSimpleName();
        String message = throwable.getMessage();
        
        if (message != null && !message.trim().isEmpty()) {
            return String.format("%s: %s", className, message);
        } else {
            return className;
        }
    }

    /**
     * 获取异常的详细描述
     * 
     * @param throwable 异常对象
     * @return 详细描述
     */
    public static String getDetailedDescription(Throwable throwable) {
        if (throwable == null) {
            return "null";
        }
        
        StringBuilder sb = new StringBuilder();
        List<Throwable> chain = getExceptionChain(throwable);
        
        for (int i = 0; i < chain.size(); i++) {
            Throwable t = chain.get(i);
            if (i > 0) {
                sb.append(" -> ");
            }
            sb.append(getShortDescription(t));
        }
        
        return sb.toString();
    }

    // ==================== 异常类型检查 ====================

    /**
     * 检查异常链中是否包含指定类型的异常
     * 
     * @param throwable 异常对象
     * @param exceptionClass 异常类型
     * @return 是否包含指定类型异常
     */
    public static boolean containsException(Throwable throwable, Class<? extends Throwable> exceptionClass) {
        if (throwable == null || exceptionClass == null) {
            return false;
        }
        
        return getExceptionChain(throwable).stream()
                .anyMatch(exceptionClass::isInstance);
    }

    /**
     * 从异常链中查找指定类型的异常
     * 
     * @param throwable 异常对象
     * @param exceptionClass 异常类型
     * @param <T> 异常类型参数
     * @return 找到的异常，如果不存在则返回 null
     */
    @SuppressWarnings("unchecked")
    public static <T extends Throwable> T findException(Throwable throwable, Class<T> exceptionClass) {
        if (throwable == null || exceptionClass == null) {
            return null;
        }
        
        return (T) getExceptionChain(throwable).stream()
                .filter(exceptionClass::isInstance)
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查异常是否为可重试的异常
     * 
     * @param throwable 异常对象
     * @return 是否可重试
     */
    public static boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof FlinkJobException) {
            return ((FlinkJobException) throwable).isRetryable();
        }
        
        // 检查是否为常见的可重试异常
        return containsException(throwable, java.net.SocketTimeoutException.class) ||
               containsException(throwable, java.net.ConnectException.class) ||
               containsException(throwable, java.sql.SQLTransientException.class) ||
               containsException(throwable, org.apache.kafka.common.errors.RetriableException.class);
    }

    // ==================== 异常转换 ====================

    /**
     * 将异常转换为 FlinkJobException
     * 
     * @param throwable 原始异常
     * @return FlinkJobException
     */
    public static FlinkJobException toFlinkJobException(Throwable throwable) {
        if (throwable instanceof FlinkJobException) {
            return (FlinkJobException) throwable;
        }
        
        ErrorCode errorCode = determineErrorCode(throwable);
        return FlinkJobException.builder(errorCode)
                .cause(throwable)
                .retryable(isRetryableException(throwable))
                .build();
    }

    /**
     * 将异常转换为 FlinkJobException（带自定义错误码）
     * 
     * @param throwable 原始异常
     * @param errorCode 错误码
     * @return FlinkJobException
     */
    public static FlinkJobException toFlinkJobException(Throwable throwable, ErrorCode errorCode) {
        if (throwable instanceof FlinkJobException) {
            FlinkJobException fje = (FlinkJobException) throwable;
            if (fje.getErrorCode() == errorCode) {
                return fje;
            }
        }
        
        return FlinkJobException.builder(errorCode)
                .cause(throwable)
                .retryable(isRetryableException(throwable))
                .build();
    }

    /**
     * 根据异常类型确定错误码
     * 
     * @param throwable 异常对象
     * @return 错误码
     */
    private static ErrorCode determineErrorCode(Throwable throwable) {
        if (throwable == null) {
            return ErrorCode.UNKNOWN_ERROR;
        }
        
        // 数据库相关异常
        if (containsException(throwable, java.sql.SQLException.class)) {
            if (containsException(throwable, java.sql.SQLTimeoutException.class)) {
                return ErrorCode.DATABASE_CONNECTION_TIMEOUT;
            }
            return ErrorCode.DATABASE_OPERATION_FAILED;
        }
        
        // 网络相关异常
        if (containsException(throwable, java.net.SocketTimeoutException.class)) {
            return ErrorCode.NETWORK_TIMEOUT;
        }
        if (containsException(throwable, java.net.ConnectException.class)) {
            return ErrorCode.NETWORK_CONNECTION_FAILED;
        }
        
        // Kafka 相关异常
        if (throwable.getClass().getName().contains("kafka")) {
            return ErrorCode.KAFKA_CONNECTION_FAILED;
        }
        
        // 文件系统相关异常
        if (containsException(throwable, java.io.FileNotFoundException.class)) {
            return ErrorCode.FILE_NOT_FOUND;
        }
        if (containsException(throwable, java.io.IOException.class)) {
            return ErrorCode.FILE_READ_FAILED;
        }
        
        // 序列化相关异常
        if (throwable.getClass().getName().contains("JsonProcessingException") ||
            throwable.getClass().getName().contains("JsonParseException")) {
            return ErrorCode.JSON_DESERIALIZATION_FAILED;
        }
        
        // 参数相关异常
        if (containsException(throwable, IllegalArgumentException.class)) {
            return ErrorCode.INVALID_PARAMETER;
        }
        if (containsException(throwable, NullPointerException.class)) {
            return ErrorCode.PARAMETER_NULL;
        }
        
        return ErrorCode.UNKNOWN_ERROR;
    }

    // ==================== 安全执行 ====================

    /**
     * 安全执行操作，捕获异常并转换为 FlinkJobException
     * 
     * @param supplier 操作供应商
     * @param <T> 返回类型
     * @return 操作结果
     * @throws FlinkJobException 操作失败时抛出
     */
    public static <T> T safeExecute(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            throw toFlinkJobException(e);
        }
    }

    /**
     * 安全执行操作，捕获异常并转换为 FlinkJobException（带自定义错误码）
     * 
     * @param supplier 操作供应商
     * @param errorCode 错误码
     * @param <T> 返回类型
     * @return 操作结果
     * @throws FlinkJobException 操作失败时抛出
     */
    public static <T> T safeExecute(Supplier<T> supplier, ErrorCode errorCode) {
        try {
            return supplier.get();
        } catch (Exception e) {
            throw toFlinkJobException(e, errorCode);
        }
    }

    /**
     * 安全执行操作，捕获异常并使用默认值
     * 
     * @param supplier 操作供应商
     * @param defaultValue 默认值
     * @param <T> 返回类型
     * @return 操作结果或默认值
     */
    public static <T> T safeExecuteWithDefault(Supplier<T> supplier, T defaultValue) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.debug("操作执行失败，使用默认值: {}", getShortDescription(e));
            return defaultValue;
        }
    }

    /**
     * 安全执行操作，捕获异常并记录日志
     * 
     * @param runnable 可运行操作
     * @param errorMessage 错误消息
     */
    public static void safeExecuteWithLog(Runnable runnable, String errorMessage) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("{}: {}", errorMessage, getShortDescription(e), e);
        }
    }

    /**
     * 安全执行操作，捕获异常并使用自定义处理器
     * 
     * @param supplier 操作供应商
     * @param exceptionHandler 异常处理器
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T safeExecuteWithHandler(Supplier<T> supplier, Function<Exception, T> exceptionHandler) {
        try {
            return supplier.get();
        } catch (Exception e) {
            return exceptionHandler.apply(e);
        }
    }

    // ==================== 异常包装 ====================

    /**
     * 包装异常并添加上下文信息
     * 
     * @param throwable 原始异常
     * @param contextKey 上下文键
     * @param contextValue 上下文值
     * @return 包装后的异常
     */
    public static FlinkJobException wrapWithContext(Throwable throwable, String contextKey, Object contextValue) {
        FlinkJobException fje = toFlinkJobException(throwable);
        return FlinkJobException.builder(fje.getErrorCode())
                .cause(fje.getCause())
                .args(fje.getArgs())
                .context(fje.getContext())
                .context(contextKey, contextValue)
                .retryable(fje.isRetryable())
                .suggestedRetryDelay(fje.getSuggestedRetryDelay())
                .build();
    }

    /**
     * 包装异常并添加操作描述
     * 
     * @param throwable 原始异常
     * @param operation 操作描述
     * @return 包装后的异常
     */
    public static FlinkJobException wrapWithOperation(Throwable throwable, String operation) {
        return wrapWithContext(throwable, "operation", operation);
    }

    /**
     * 包装异常并添加组件信息
     * 
     * @param throwable 原始异常
     * @param component 组件名称
     * @return 包装后的异常
     */
    public static FlinkJobException wrapWithComponent(Throwable throwable, String component) {
        return wrapWithContext(throwable, "component", component);
    }
}
