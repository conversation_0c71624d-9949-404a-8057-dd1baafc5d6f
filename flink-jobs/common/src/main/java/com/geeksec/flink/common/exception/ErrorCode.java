package com.geeksec.flink.common.exception;

import lombok.Getter;

/**
 * 错误码枚举
 * 定义 Flink 作业中所有可能的错误码和错误信息
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum ErrorCode {

    // ==================== 通用错误 (1000-1999) ====================
    
    /** 未知错误 */
    UNKNOWN_ERROR("1000", "未知错误"),
    
    /** 系统内部错误 */
    INTERNAL_ERROR("1001", "系统内部错误"),
    
    /** 参数错误 */
    INVALID_PARAMETER("1002", "参数错误"),
    
    /** 参数为空 */
    PARAMETER_NULL("1003", "参数不能为空"),
    
    /** 参数格式错误 */
    PARAMETER_FORMAT_ERROR("1004", "参数格式错误"),
    
    /** 操作超时 */
    OPERATION_TIMEOUT("1005", "操作超时"),
    
    /** 资源不足 */
    INSUFFICIENT_RESOURCES("1006", "资源不足"),
    
    /** 权限不足 */
    INSUFFICIENT_PERMISSIONS("1007", "权限不足"),
    
    /** 配置错误 */
    CONFIGURATION_ERROR("1008", "配置错误"),
    
    /** 初始化失败 */
    INITIALIZATION_FAILED("1009", "初始化失败"),

    // ==================== 数据库错误 (2000-2999) ====================
    
    /** 数据库连接失败 */
    DATABASE_CONNECTION_FAILED("2000", "数据库连接失败"),
    
    /** 数据库连接超时 */
    DATABASE_CONNECTION_TIMEOUT("2001", "数据库连接超时"),
    
    /** 数据库操作失败 */
    DATABASE_OPERATION_FAILED("2002", "数据库操作失败"),
    
    /** SQL 执行错误 */
    SQL_EXECUTION_ERROR("2003", "SQL 执行错误"),
    
    /** 事务回滚 */
    TRANSACTION_ROLLBACK("2004", "事务回滚"),
    
    /** 数据不存在 */
    DATA_NOT_FOUND("2005", "数据不存在"),
    
    /** 数据已存在 */
    DATA_ALREADY_EXISTS("2006", "数据已存在"),
    
    /** 数据格式错误 */
    DATA_FORMAT_ERROR("2007", "数据格式错误"),
    
    /** 数据完整性约束违反 */
    DATA_INTEGRITY_VIOLATION("2008", "数据完整性约束违反"),
    
    /** 连接池耗尽 */
    CONNECTION_POOL_EXHAUSTED("2009", "连接池耗尽"),

    // ==================== Kafka 错误 (3000-3999) ====================
    
    /** Kafka 连接失败 */
    KAFKA_CONNECTION_FAILED("3000", "Kafka 连接失败"),
    
    /** Kafka 生产者错误 */
    KAFKA_PRODUCER_ERROR("3001", "Kafka 生产者错误"),
    
    /** Kafka 消费者错误 */
    KAFKA_CONSUMER_ERROR("3002", "Kafka 消费者错误"),
    
    /** Kafka 主题不存在 */
    KAFKA_TOPIC_NOT_FOUND("3003", "Kafka 主题不存在"),
    
    /** Kafka 序列化错误 */
    KAFKA_SERIALIZATION_ERROR("3004", "Kafka 序列化错误"),
    
    /** Kafka 反序列化错误 */
    KAFKA_DESERIALIZATION_ERROR("3005", "Kafka 反序列化错误"),
    
    /** Kafka 偏移量提交失败 */
    KAFKA_OFFSET_COMMIT_FAILED("3006", "Kafka 偏移量提交失败"),
    
    /** Kafka 分区分配失败 */
    KAFKA_PARTITION_ASSIGNMENT_FAILED("3007", "Kafka 分区分配失败"),
    
    /** Kafka 消息发送失败 */
    KAFKA_MESSAGE_SEND_FAILED("3008", "Kafka 消息发送失败"),
    
    /** Kafka 消息消费失败 */
    KAFKA_MESSAGE_CONSUME_FAILED("3009", "Kafka 消息消费失败"),

    // ==================== Flink 作业错误 (4000-4999) ====================
    
    /** Flink 作业启动失败 */
    FLINK_JOB_START_FAILED("4000", "Flink 作业启动失败"),
    
    /** Flink 作业停止失败 */
    FLINK_JOB_STOP_FAILED("4001", "Flink 作业停止失败"),
    
    /** Flink 作业重启失败 */
    FLINK_JOB_RESTART_FAILED("4002", "Flink 作业重启失败"),
    
    /** Flink 检查点失败 */
    FLINK_CHECKPOINT_FAILED("4003", "Flink 检查点失败"),
    
    /** Flink 状态恢复失败 */
    FLINK_STATE_RECOVERY_FAILED("4004", "Flink 状态恢复失败"),
    
    /** Flink 算子初始化失败 */
    FLINK_OPERATOR_INIT_FAILED("4005", "Flink 算子初始化失败"),
    
    /** Flink 数据流处理失败 */
    FLINK_STREAM_PROCESSING_FAILED("4006", "Flink 数据流处理失败"),
    
    /** Flink 窗口操作失败 */
    FLINK_WINDOW_OPERATION_FAILED("4007", "Flink 窗口操作失败"),
    
    /** Flink 状态访问失败 */
    FLINK_STATE_ACCESS_FAILED("4008", "Flink 状态访问失败"),
    
    /** Flink 并行度配置错误 */
    FLINK_PARALLELISM_CONFIG_ERROR("4009", "Flink 并行度配置错误"),

    // ==================== 数据处理错误 (5000-5999) ====================
    
    /** 数据解析失败 */
    DATA_PARSING_FAILED("5000", "数据解析失败"),
    
    /** 数据转换失败 */
    DATA_TRANSFORMATION_FAILED("5001", "数据转换失败"),
    
    /** 数据验证失败 */
    DATA_VALIDATION_FAILED("5002", "数据验证失败"),
    
    /** 数据清洗失败 */
    DATA_CLEANING_FAILED("5003", "数据清洗失败"),
    
    /** 数据聚合失败 */
    DATA_AGGREGATION_FAILED("5004", "数据聚合失败"),
    
    /** 数据分组失败 */
    DATA_GROUPING_FAILED("5005", "数据分组失败"),
    
    /** 数据排序失败 */
    DATA_SORTING_FAILED("5006", "数据排序失败"),
    
    /** 数据过滤失败 */
    DATA_FILTERING_FAILED("5007", "数据过滤失败"),
    
    /** 数据映射失败 */
    DATA_MAPPING_FAILED("5008", "数据映射失败"),
    
    /** 数据合并失败 */
    DATA_MERGING_FAILED("5009", "数据合并失败"),

    // ==================== 网络错误 (6000-6999) ====================
    
    /** 网络连接失败 */
    NETWORK_CONNECTION_FAILED("6000", "网络连接失败"),
    
    /** 网络超时 */
    NETWORK_TIMEOUT("6001", "网络超时"),
    
    /** 网络中断 */
    NETWORK_INTERRUPTED("6002", "网络中断"),
    
    /** HTTP 请求失败 */
    HTTP_REQUEST_FAILED("6003", "HTTP 请求失败"),
    
    /** HTTP 响应错误 */
    HTTP_RESPONSE_ERROR("6004", "HTTP 响应错误"),
    
    /** SSL 握手失败 */
    SSL_HANDSHAKE_FAILED("6005", "SSL 握手失败"),
    
    /** DNS 解析失败 */
    DNS_RESOLUTION_FAILED("6006", "DNS 解析失败"),
    
    /** 代理连接失败 */
    PROXY_CONNECTION_FAILED("6007", "代理连接失败"),
    
    /** 防火墙阻止 */
    FIREWALL_BLOCKED("6008", "防火墙阻止"),
    
    /** 端口不可达 */
    PORT_UNREACHABLE("6009", "端口不可达"),

    // ==================== 文件系统错误 (7000-7999) ====================
    
    /** 文件不存在 */
    FILE_NOT_FOUND("7000", "文件不存在"),
    
    /** 文件读取失败 */
    FILE_READ_FAILED("7001", "文件读取失败"),
    
    /** 文件写入失败 */
    FILE_WRITE_FAILED("7002", "文件写入失败"),
    
    /** 文件删除失败 */
    FILE_DELETE_FAILED("7003", "文件删除失败"),
    
    /** 文件权限不足 */
    FILE_PERMISSION_DENIED("7004", "文件权限不足"),
    
    /** 磁盘空间不足 */
    DISK_SPACE_INSUFFICIENT("7005", "磁盘空间不足"),
    
    /** 目录不存在 */
    DIRECTORY_NOT_FOUND("7006", "目录不存在"),
    
    /** 目录创建失败 */
    DIRECTORY_CREATE_FAILED("7007", "目录创建失败"),
    
    /** 文件格式不支持 */
    FILE_FORMAT_UNSUPPORTED("7008", "文件格式不支持"),
    
    /** 文件损坏 */
    FILE_CORRUPTED("7009", "文件损坏"),

    // ==================== 序列化错误 (8000-8999) ====================
    
    /** JSON 序列化失败 */
    JSON_SERIALIZATION_FAILED("8000", "JSON 序列化失败"),
    
    /** JSON 反序列化失败 */
    JSON_DESERIALIZATION_FAILED("8001", "JSON 反序列化失败"),
    
    /** XML 序列化失败 */
    XML_SERIALIZATION_FAILED("8002", "XML 序列化失败"),
    
    /** XML 反序列化失败 */
    XML_DESERIALIZATION_FAILED("8003", "XML 反序列化失败"),
    
    /** 对象序列化失败 */
    OBJECT_SERIALIZATION_FAILED("8004", "对象序列化失败"),
    
    /** 对象反序列化失败 */
    OBJECT_DESERIALIZATION_FAILED("8005", "对象反序列化失败"),
    
    /** 编码转换失败 */
    ENCODING_CONVERSION_FAILED("8006", "编码转换失败"),
    
    /** 压缩失败 */
    COMPRESSION_FAILED("8007", "压缩失败"),
    
    /** 解压缩失败 */
    DECOMPRESSION_FAILED("8008", "解压缩失败"),
    
    /** 加密失败 */
    ENCRYPTION_FAILED("8009", "加密失败"),

    // ==================== 业务逻辑错误 (9000-9999) ====================
    
    /** 业务规则违反 */
    BUSINESS_RULE_VIOLATION("9000", "业务规则违反"),
    
    /** 状态转换错误 */
    STATE_TRANSITION_ERROR("9001", "状态转换错误"),
    
    /** 工作流执行失败 */
    WORKFLOW_EXECUTION_FAILED("9002", "工作流执行失败"),
    
    /** 任务调度失败 */
    TASK_SCHEDULING_FAILED("9003", "任务调度失败"),
    
    /** 资源锁定失败 */
    RESOURCE_LOCK_FAILED("9004", "资源锁定失败"),
    
    /** 并发冲突 */
    CONCURRENCY_CONFLICT("9005", "并发冲突"),
    
    /** 版本冲突 */
    VERSION_CONFLICT("9006", "版本冲突"),
    
    /** 依赖检查失败 */
    DEPENDENCY_CHECK_FAILED("9007", "依赖检查失败"),
    
    /** 前置条件不满足 */
    PRECONDITION_NOT_MET("9008", "前置条件不满足"),
    
    /** 后置条件验证失败 */
    POSTCONDITION_VALIDATION_FAILED("9009", "后置条件验证失败");

    /** 错误码 */
    private final String code;
    
    /** 错误信息 */
    private final String message;

    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误信息
     */
    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据错误码查找枚举
     * 
     * @param code 错误码
     * @return 错误码枚举，如果不存在则返回 UNKNOWN_ERROR
     */
    public static ErrorCode fromCode(String code) {
        if (code == null) {
            return UNKNOWN_ERROR;
        }
        
        for (ErrorCode errorCode : values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        
        return UNKNOWN_ERROR;
    }

    /**
     * 格式化错误信息
     * 
     * @param args 格式化参数
     * @return 格式化后的错误信息
     */
    public String formatMessage(Object... args) {
        if (args == null || args.length == 0) {
            return message;
        }
        
        try {
            return String.format(message, args);
        } catch (Exception e) {
            return message + " (格式化参数失败)";
        }
    }

    @Override
    public String toString() {
        return String.format("[%s] %s", code, message);
    }
}
