package com.geeksec.flink.common.constants;

/**
 * Flink 作业通用配置常量
 * 定义所有 Flink 作业共享的配置键名和默认值
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public final class FlinkConfigConstants {

    private FlinkConfigConstants() {
        // 工具类，禁止实例化
    }

    // ==================== 作业基础配置 ====================
    
    /** 作业名称配置键 */
    public static final String JOB_NAME = "flink.job.name";
    
    /** 作业并行度配置键 */
    public static final String JOB_PARALLELISM = "flink.job.parallelism";
    
    /** 默认作业并行度 */
    public static final int DEFAULT_JOB_PARALLELISM = 4;
    
    /** 最大作业并行度 */
    public static final int MAX_JOB_PARALLELISM = 64;

    // ==================== 检查点配置 ====================
    
    /** 检查点间隔配置键 */
    public static final String CHECKPOINT_INTERVAL = "flink.checkpoint.interval";
    
    /** 默认检查点间隔（毫秒） */
    public static final long DEFAULT_CHECKPOINT_INTERVAL = 60000L;
    
    /** 检查点超时配置键 */
    public static final String CHECKPOINT_TIMEOUT = "flink.checkpoint.timeout";
    
    /** 默认检查点超时（毫秒） */
    public static final long DEFAULT_CHECKPOINT_TIMEOUT = 300000L;
    
    /** 检查点最小间隔配置键 */
    public static final String CHECKPOINT_MIN_PAUSE = "flink.checkpoint.min.pause";
    
    /** 默认检查点最小间隔（毫秒） */
    public static final long DEFAULT_CHECKPOINT_MIN_PAUSE = 5000L;
    
    /** 最大并发检查点数配置键 */
    public static final String CHECKPOINT_MAX_CONCURRENT = "flink.checkpoint.max.concurrent";
    
    /** 默认最大并发检查点数 */
    public static final int DEFAULT_CHECKPOINT_MAX_CONCURRENT = 1;

    // ==================== 重启策略配置 ====================
    
    /** 重启尝试次数配置键 */
    public static final String RESTART_ATTEMPTS = "flink.restart.attempts";
    
    /** 默认重启尝试次数 */
    public static final int DEFAULT_RESTART_ATTEMPTS = 3;
    
    /** 重启延迟配置键 */
    public static final String RESTART_DELAY = "flink.restart.delay";
    
    /** 默认重启延迟（毫秒） */
    public static final long DEFAULT_RESTART_DELAY = 10000L;
    
    /** 故障率间隔配置键 */
    public static final String RESTART_FAILURE_RATE_INTERVAL = "flink.restart.failure.rate.interval";
    
    /** 默认故障率间隔（毫秒） */
    public static final long DEFAULT_RESTART_FAILURE_RATE_INTERVAL = 600000L;

    // ==================== Kafka 配置 ====================
    
    /** Kafka Bootstrap Servers 配置键 */
    public static final String KAFKA_BOOTSTRAP_SERVERS = "kafka.bootstrap.servers";
    
    /** 默认 Kafka Bootstrap Servers */
    public static final String DEFAULT_KAFKA_BOOTSTRAP_SERVERS = "kafka:9092";
    
    /** Kafka Consumer Group ID 配置键 */
    public static final String KAFKA_GROUP_ID = "kafka.group.id";
    
    /** Kafka 起始偏移量配置键 */
    public static final String KAFKA_STARTING_OFFSETS = "kafka.starting.offsets";
    
    /** 默认 Kafka 起始偏移量 */
    public static final String DEFAULT_KAFKA_STARTING_OFFSETS = "latest";
    
    /** Kafka 自动提交配置键 */
    public static final String KAFKA_AUTO_COMMIT = "kafka.auto.commit";
    
    /** 默认 Kafka 自动提交 */
    public static final boolean DEFAULT_KAFKA_AUTO_COMMIT = true;
    
    /** Kafka 提交间隔配置键 */
    public static final String KAFKA_COMMIT_INTERVAL = "kafka.commit.interval";
    
    /** 默认 Kafka 提交间隔（毫秒） */
    public static final long DEFAULT_KAFKA_COMMIT_INTERVAL = 5000L;

    // ==================== 并行度配置 ====================
    
    /** Kafka 源并行度配置键 */
    public static final String PARALLELISM_KAFKA_SOURCE = "parallelism.kafka.source";
    
    /** 默认 Kafka 源并行度 */
    public static final int DEFAULT_PARALLELISM_KAFKA_SOURCE = 2;
    
    /** 解析操作并行度配置键 */
    public static final String PARALLELISM_PARSING = "parallelism.parsing";
    
    /** 默认解析操作并行度 */
    public static final int DEFAULT_PARALLELISM_PARSING = 4;
    
    /** 数据汇并行度配置键 */
    public static final String PARALLELISM_SINK = "parallelism.sink";
    
    /** 默认数据汇并行度 */
    public static final int DEFAULT_PARALLELISM_SINK = 2;

    // ==================== 数据库配置 ====================
    
    /** PostgreSQL 主机配置键 */
    public static final String POSTGRESQL_HOST = "postgresql.host";
    
    /** 默认 PostgreSQL 主机 */
    public static final String DEFAULT_POSTGRESQL_HOST = "postgresql";
    
    /** PostgreSQL 端口配置键 */
    public static final String POSTGRESQL_PORT = "postgresql.port";
    
    /** 默认 PostgreSQL 端口 */
    public static final int DEFAULT_POSTGRESQL_PORT = 5432;
    
    /** PostgreSQL 数据库名配置键 */
    public static final String POSTGRESQL_DATABASE = "postgresql.database";
    
    /** 默认 PostgreSQL 数据库名 */
    public static final String DEFAULT_POSTGRESQL_DATABASE = "nta";
    
    /** PostgreSQL 用户名配置键 */
    public static final String POSTGRESQL_USERNAME = "postgresql.username";
    
    /** 默认 PostgreSQL 用户名 */
    public static final String DEFAULT_POSTGRESQL_USERNAME = "nta_user";
    
    /** PostgreSQL 密码配置键 */
    public static final String POSTGRESQL_PASSWORD = "postgresql.password";

    // ==================== Doris 配置 ====================
    
    /** Doris FE 节点配置键 */
    public static final String DORIS_FENODES = "doris.fenodes";
    
    /** 默认 Doris FE 节点 */
    public static final String DEFAULT_DORIS_FENODES = "doris-fe:8030";
    
    /** Doris 数据库名配置键 */
    public static final String DORIS_DATABASE = "doris.database";
    
    /** 默认 Doris 数据库名 */
    public static final String DEFAULT_DORIS_DATABASE = "nta";
    
    /** Doris 用户名配置键 */
    public static final String DORIS_USERNAME = "doris.username";
    
    /** 默认 Doris 用户名 */
    public static final String DEFAULT_DORIS_USERNAME = "root";
    
    /** Doris 密码配置键 */
    public static final String DORIS_PASSWORD = "doris.password";
    
    /** 默认 Doris 密码 */
    public static final String DEFAULT_DORIS_PASSWORD = "";

    // ==================== Redis 配置 ====================
    
    /** Redis 主机配置键 */
    public static final String REDIS_HOST = "redis.host";
    
    /** 默认 Redis 主机 */
    public static final String DEFAULT_REDIS_HOST = "redis";
    
    /** Redis 端口配置键 */
    public static final String REDIS_PORT = "redis.port";
    
    /** 默认 Redis 端口 */
    public static final int DEFAULT_REDIS_PORT = 6379;
    
    /** Redis 密码配置键 */
    public static final String REDIS_PASSWORD = "redis.password";
    
    /** Redis 数据库索引配置键 */
    public static final String REDIS_DATABASE = "redis.database";
    
    /** 默认 Redis 数据库索引 */
    public static final int DEFAULT_REDIS_DATABASE = 0;
    
    /** Redis 连接超时配置键 */
    public static final String REDIS_CONNECTION_TIMEOUT = "redis.connection.timeout";
    
    /** 默认 Redis 连接超时（毫秒） */
    public static final int DEFAULT_REDIS_CONNECTION_TIMEOUT = 5000;
    
    /** Redis Socket 超时配置键 */
    public static final String REDIS_SOCKET_TIMEOUT = "redis.socket.timeout";
    
    /** 默认 Redis Socket 超时（毫秒） */
    public static final int DEFAULT_REDIS_SOCKET_TIMEOUT = 5000;

    // ==================== 监控配置 ====================
    
    /** 监控启用配置键 */
    public static final String MONITORING_ENABLED = "monitoring.enabled";
    
    /** 默认监控启用状态 */
    public static final boolean DEFAULT_MONITORING_ENABLED = true;
    
    /** 指标间隔配置键 */
    public static final String METRICS_INTERVAL = "metrics.interval";
    
    /** 默认指标间隔（毫秒） */
    public static final long DEFAULT_METRICS_INTERVAL = 30000L;
    
    /** 性能日志启用配置键 */
    public static final String PERFORMANCE_LOGGING_ENABLED = "performance.logging.enabled";
    
    /** 默认性能日志启用状态 */
    public static final boolean DEFAULT_PERFORMANCE_LOGGING_ENABLED = true;

    // ==================== 调试配置 ====================
    
    /** 调试模式启用配置键 */
    public static final String DEBUG_ENABLED = "debug.enabled";
    
    /** 默认调试模式启用状态 */
    public static final boolean DEFAULT_DEBUG_ENABLED = false;
    
    /** 详细指标启用配置键 */
    public static final String DETAILED_METRICS_ENABLED = "detailed.metrics.enabled";
    
    /** 默认详细指标启用状态 */
    public static final boolean DEFAULT_DETAILED_METRICS_ENABLED = false;

    // ==================== 缓存配置 ====================
    
    /** 缓存最大大小配置键 */
    public static final String CACHE_MAX_SIZE = "cache.max.size";
    
    /** 默认缓存最大大小 */
    public static final int DEFAULT_CACHE_MAX_SIZE = 10000;
    
    /** 缓存过期时间配置键 */
    public static final String CACHE_EXPIRATION_MS = "cache.expiration.ms";
    
    /** 默认缓存过期时间（毫秒） */
    public static final long DEFAULT_CACHE_EXPIRATION_MS = 300000L;

    // ==================== 批处理配置 ====================
    
    /** 批处理启用配置键 */
    public static final String BATCH_ENABLED = "batch.enabled";
    
    /** 默认批处理启用状态 */
    public static final boolean DEFAULT_BATCH_ENABLED = true;
    
    /** 最大批量大小配置键 */
    public static final String BATCH_MAX_SIZE = "batch.max.size";
    
    /** 默认最大批量大小 */
    public static final int DEFAULT_BATCH_MAX_SIZE = 50;
    
    /** 最大等待时间配置键 */
    public static final String BATCH_MAX_WAIT_TIME_MS = "batch.max.wait.time.ms";
    
    /** 默认最大等待时间（毫秒） */
    public static final long DEFAULT_BATCH_MAX_WAIT_TIME_MS = 30000L;
}
