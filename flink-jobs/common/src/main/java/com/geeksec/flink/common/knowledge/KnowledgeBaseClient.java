package com.geeksec.flink.common.knowledge;

import java.io.IOException;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;

/**
 * 知识库客户端
 *
 * 为Flink作业提供知识库数据访问功能，支持缓存和定期刷新
 *
 * <AUTHOR>
 */
@Slf4j
public class KnowledgeBaseClient implements Serializable {

    private static final long serialVersionUID = 1L;

    private final String baseUrl;
    private final CloseableHttpClient httpClient;

    /** Jedis连接池 */
    private transient JedisPool jedisPool;

    /** Redis缓存TTL（30分钟） */
    private static final long REDIS_CACHE_TTL_MS = 30 * 60 * 1000;

    /** 缓存键前缀 */
    private static final String CACHE_KEY_PREFIX = "knowledge:base:";

    /** 默认知识库URL */
    private static final String DEFAULT_BASE_URL = "http://knowledge-base:8080/knowledge-base";

    /**
     * 默认构造函数，使用默认的知识库URL
     */
    public KnowledgeBaseClient() {
        this(DEFAULT_BASE_URL);
    }

    /**
     * 构造函数，指定知识库URL
     *
     * @param baseUrl 知识库服务URL
     */
    public KnowledgeBaseClient(String baseUrl) {
        this.baseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        this.httpClient = HttpClients.createDefault();

        // 初始化Redis缓存
        initRedisTemplate();

        log.info("知识库客户端初始化完成，服务地址: {}", this.baseUrl);
    }

    /**
     * 初始化Jedis连接池
     */
    private void initRedisTemplate() {
        try {
            String redisHost = System.getProperty("redis.host", "redis");
            int redisPort = Integer.parseInt(System.getProperty("redis.port", "6379"));
            String redisPassword = System.getProperty("redis.password");
            int redisTimeout = Integer.parseInt(System.getProperty("redis.timeout", "5000"));
            int redisDatabase = Integer.parseInt(System.getProperty("redis.database", "0"));

            // 配置Jedis连接池
            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(20); // 最大连接数
            poolConfig.setMaxIdle(10); // 最大空闲连接数
            poolConfig.setMinIdle(2); // 最小空闲连接数
            poolConfig.setTestOnBorrow(true);
            poolConfig.setTestOnReturn(true);
            poolConfig.setTestWhileIdle(true);

            // 创建Jedis连接池
            if (redisPassword != null && !redisPassword.trim().isEmpty()) {
                jedisPool = new JedisPool(poolConfig, redisHost, redisPort, redisTimeout, redisPassword, redisDatabase);
            } else {
                jedisPool = new JedisPool(poolConfig, redisHost, redisPort, redisTimeout, null, redisDatabase);
            }

            // 测试连接
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.ping();
            }

            log.info("Redis缓存初始化成功，连接: {}:{}/{}", redisHost, redisPort, redisDatabase);
        } catch (Exception e) {
            log.warn("Redis缓存初始化失败，将直接访问知识库API: {}", e.getMessage());
            jedisPool = null;
        }
    }

    /**
     * 根据威胁名称获取威胁情报
     */
    public Map<String, Object> getThreatIntelligenceByName(String threatName) {
        String cacheKey = "threat_intel_name_" + threatName;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/threat-intelligence/name/" + threatName;
            return executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
        });
    }



    /**
     * 根据检测器名称获取检测器配置
     */
    public Map<String, Object> getDetectorConfig(String detectorName) {
        String cacheKey = "detector_config_" + detectorName;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/detector-config/" + detectorName;
            return executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
        });
    }

    /**
     * 检查域名是否为恶意域名
     */
    public boolean isMaliciousDomain(String domain) {
        try {
            String url = baseUrl + "/api/v1/domain/check/" + domain;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            return result != null && Boolean.TRUE.equals(result.get("isMalicious"));
        } catch (Exception e) {
            log.warn("检查恶意域名失败: {}", domain, e);
            return false;
        }
    }

    /**
     * 检查域名是否为C2威胁域名
     */
    public boolean isC2ThreatDomain(String domain) {
        String cacheKey = "c2_domain_" + domain;
        return getCachedData(cacheKey, () -> {
            try {
                String url = baseUrl + "/api/v1/threat/c2/domain/check/" + domain;
                Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
                });
                return result != null && Boolean.TRUE.equals(result.get("isC2Threat"));
            } catch (Exception e) {
                log.warn("检查C2威胁域名失败: {}", domain, e);
                return false;
            }
        });
    }

    /**
     * 检查IP是否为C2威胁IP
     */
    public boolean isC2ThreatIp(String ip) {
        String cacheKey = "c2_ip_" + ip;
        return getCachedData(cacheKey, () -> {
            try {
                String url = baseUrl + "/api/v1/threat/c2/ip/check/" + ip;
                Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
                });
                return result != null && Boolean.TRUE.equals(result.get("isC2Threat"));
            } catch (Exception e) {
                log.warn("检查C2威胁IP失败: {}", ip, e);
                return false;
            }
        });
    }

    /**
     * 检查IP是否为IOC IP
     */
    public boolean isIocIp(String ip) {
        String cacheKey = "ioc_ip_" + ip;
        return getCachedData(cacheKey, () -> {
            try {
                String url = baseUrl + "/api/v1/threat/ioc/ip/check/" + ip;
                Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
                });
                return result != null && Boolean.TRUE.equals(result.get("isIoc"));
            } catch (Exception e) {
                log.warn("检查IOC IP失败: {}", ip, e);
                return false;
            }
        });
    }

    /**
     * 获取IP地理位置信息
     */
    public Map<String, Object> getGeoLocation(String ip) {
        String cacheKey = "geo_" + ip;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/geo/ip/" + ip;
            return executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
        });
    }

    // ==================== 域名相关API ====================

    /**
     * 检查域名是否为挖矿域名
     */
    public boolean isMiningDomain(String domain) {
        try {
            String url = baseUrl + "/api/v1/domain/mining/check/" + domain;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            return result != null && Boolean.TRUE.equals(result.get("isMining"));
        } catch (Exception e) {
            log.warn("检查挖矿域名失败: {}", domain, e);
            return false;
        }
    }

    /**
     * 检查域名是否为热门域名
     */
    public Integer getPopularDomainRank(String domain) {
        try {
            String url = baseUrl + "/api/v1/domain/popular/check/" + domain;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            if (result != null && Boolean.TRUE.equals(result.get("isPopular"))) {
                Object rank = result.get("rank");
                return rank instanceof Integer ? (Integer) rank : null;
            }
            return null;
        } catch (Exception e) {
            log.warn("检查热门域名失败: {}", domain, e);
            return null;
        }
    }

    /**
     * 检查域名是否为视频网站域名
     */
    public boolean isVideoDomain(String domain) {
        try {
            String url = baseUrl + "/api/v1/domain/video/check/" + domain;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            return result != null && Boolean.TRUE.equals(result.get("isVideo"));
        } catch (Exception e) {
            log.warn("检查视频网站域名失败: {}", domain, e);
            return false;
        }
    }

    /**
     * 检查顶级域名是否有效
     */
    public boolean isValidTld(String tld) {
        try {
            String url = baseUrl + "/api/v1/domain/tld/check/" + tld;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            return result != null && Boolean.TRUE.equals(result.get("isValid"));
        } catch (Exception e) {
            log.warn("检查顶级域名失败: {}", tld, e);
            return false;
        }
    }

    /**
     * 获取新顶级域名信息
     */
    public Map<String, Object> getNewGtldInfo(String gtld) {
        String cacheKey = "new_gtld_" + gtld;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/domain/new-gtld/check/" + gtld;
            return executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
        });
    }

    // ==================== 地理位置相关API ====================

    /**
     * 根据国家代码获取国家信息
     */
    public Map<String, Object> getCountryByCode(String code) {
        String cacheKey = "country_code_" + code;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/geo/country/code/" + code;
            return executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
        });
    }

    /**
     * 根据国家名称获取国家信息
     */
    public Map<String, Object> getCountryByName(String name) {
        String cacheKey = "country_name_" + name;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/geo/country/name/" + name;
            return executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
        });
    }

    /**
     * 根据国家获取公司后缀列表
     */
    public List<String> getCompanySuffixesByCountry(String country) {
        String cacheKey = "company_suffixes_" + country;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/geo/company-suffix/country/" + country;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            if (result != null && result.containsKey("suffixes")) {
                @SuppressWarnings("unchecked")
                List<String> suffixes = (List<String>) result.get("suffixes");
                return suffixes;
            }
            return Collections.emptyList();
        });
    }

    /**
     * 检查公司后缀是否有效
     */
    public List<String> getCountriesBySuffix(String suffix) {
        String cacheKey = "countries_by_suffix_" + suffix;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/geo/company-suffix/check/" + suffix;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            if (result != null && result.containsKey("countries")) {
                @SuppressWarnings("unchecked")
                List<String> countries = (List<String>) result.get("countries");
                return countries;
            }
            return Collections.emptyList();
        });
    }

    // ==================== 证书相关API ====================

    /**
     * 根据OID获取证书策略信息
     */
    public Map<String, Object> getOidInfo(String oid) {
        String cacheKey = "oid_" + oid;
        return getCachedData(cacheKey, () -> {
            String url = baseUrl + "/api/v1/certificate/oid/" + oid;
            return executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
        });
    }

    /**
     * 检查OID是否为已知的证书策略OID
     */
    public boolean isKnownOid(String oid) {
        try {
            String url = baseUrl + "/api/v1/certificate/oid/check/" + oid;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            return result != null && Boolean.TRUE.equals(result.get("isKnown"));
        } catch (Exception e) {
            log.warn("检查OID失败: {}", oid, e);
            return false;
        }
    }

    /**
     * 检查证书颁发机构是否为免费CA
     */
    public boolean isFreeCertificateAuthority(String issuer) {
        try {
            String url = baseUrl + "/api/v1/certificate/ca/check/" + issuer;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            return result != null && Boolean.TRUE.equals(result.get("isFree"));
        } catch (Exception e) {
            log.warn("检查免费CA失败: {}", issuer, e);
            return false;
        }
    }

    /**
     * 检查证书是否为可信CA证书
     *
     * @param sha1 证书SHA1哈希值
     * @return 是否为可信CA证书
     */
    public boolean isTrustedCaCertificate(String sha1) {
        try {
            String url = baseUrl + "/api/certificate/trusted-ca/check/" + sha1;
            Map<String, Object> result = executeGetRequest(url, new TypeReference<Map<String, Object>>() {
            });
            return result != null && Boolean.TRUE.equals(result.get("data"));
        } catch (Exception e) {
            log.warn("检查可信CA证书失败: {}", sha1, e);
            return false;
        }
    }

    /**
     * 从Redis缓存获取数据，如果缓存未命中则调用数据提供者获取数据并缓存
     *
     * @param cacheKey 缓存键
     * @param supplier 数据提供者
     * @param <T>      数据类型
     * @return 数据
     */
    @SuppressWarnings("unchecked")
    private <T> T getCachedData(String cacheKey, DataSupplier<T> supplier) {
        String fullCacheKey = CACHE_KEY_PREFIX + cacheKey;

        // 尝试从Redis获取缓存数据
        if (jedisPool != null) {
            try (Jedis jedis = jedisPool.getResource()) {
                String cachedJson = jedis.get(fullCacheKey);
                if (cachedJson != null) {
                    log.debug("Redis缓存命中: {}", cacheKey);
                    // 反序列化JSON数据
                    T cachedData = JSON.parseObject(cachedJson, new TypeReference<T>() {
                    });
                    return cachedData;
                }
            } catch (Exception e) {
                log.warn("Redis缓存读取失败: {}", e.getMessage());
            }
        }

        // 缓存未命中，从知识库API获取数据
        log.debug("缓存未命中，从知识库获取: {}", cacheKey);
        try {
            T freshData = supplier.get();

            // 将新数据存入Redis缓存
            if (freshData != null && jedisPool != null) {
                try (Jedis jedis = jedisPool.getResource()) {
                    // 序列化为JSON
                    String jsonData = JSON.toJSONString(freshData);
                    // 设置TTL（秒）
                    int ttlSeconds = (int) (REDIS_CACHE_TTL_MS / 1000);
                    jedis.setex(fullCacheKey, ttlSeconds, jsonData);
                    log.debug("数据已缓存到Redis: {}", cacheKey);
                } catch (Exception e) {
                    log.warn("Redis缓存写入失败: {}", e.getMessage());
                }
            }

            return freshData;
        } catch (Exception e) {
            log.error("获取知识库数据失败: {}", cacheKey, e);
            return null;
        }
    }

    /**
     * 执行GET请求
     */
    private <T> T executeGetRequest(String url, TypeReference<T> typeRef) throws IOException {
        HttpGet request = new HttpGet(url);
        request.setHeader("Accept", "application/json");

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            if (statusCode == 200) {
                return JSON.parseObject(responseBody, typeRef);
            } else if (statusCode == 404) {
                return null; // 数据不存在
            } else {
                throw new IOException("HTTP请求失败: " + statusCode + ", " + responseBody);
            }
        }
    }

    /**
     * 执行POST请求
     */
    private <T> T executePostRequest(String url, String requestBody, TypeReference<T> typeRef) throws IOException {
        HttpPost request = new HttpPost(url);
        request.setHeader("Accept", "application/json");
        request.setHeader("Content-Type", "application/json");
        request.setEntity(new StringEntity(requestBody, ContentType.APPLICATION_JSON));

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBodyStr = EntityUtils.toString(response.getEntity());

            if (statusCode == 200) {
                return JSON.parseObject(responseBodyStr, typeRef);
            } else {
                throw new IOException("HTTP请求失败: " + statusCode + ", " + responseBodyStr);
            }
        }
    }

    /**
     * 关闭客户端
     */
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
            if (jedisPool != null && !jedisPool.isClosed()) {
                jedisPool.close();
            }
        } catch (Exception e) {
            log.warn("关闭知识库客户端时发生异常", e);
        }
    }

    /**
     * 数据供应商接口
     */
    @FunctionalInterface
    private interface DataSupplier<T> {
        T get() throws Exception;
    }
}
