package com.geeksec.flink.common.serialization;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.types.Row;

import java.nio.charset.StandardCharsets;

/**
 * Kafka Row键序列化器
 * 用于将Flink Row中的指定字段作为Kafka消息的键
 * 
 * <AUTHOR>
 */
@Slf4j
public class KafkaRowKeySerializer implements SerializationSchema<Row> {
    
    private static final long serialVersionUID = 1L;
    
    private final String keyFieldName;
    private final int keyFieldIndex;
    private final boolean useFieldIndex;
    
    /**
     * 构造函数，使用字段名称
     * 
     * @param keyFieldName 作为键的字段名称
     */
    public KafkaRowKeySerializer(String keyFieldName) {
        this.keyFieldName = keyFieldName;
        this.keyFieldIndex = -1;
        this.useFieldIndex = false;
    }
    
    /**
     * 构造函数，使用字段索引
     * 
     * @param keyFieldIndex 作为键的字段索引
     */
    public KafkaRowKeySerializer(int keyFieldIndex) {
        this.keyFieldName = null;
        this.keyFieldIndex = keyFieldIndex;
        this.useFieldIndex = true;
    }
    
    @Override
    public byte[] serialize(Row row) {
        if (row == null) {
            log.warn("Row为null，返回空字节数组");
            return new byte[0];
        }
        
        Object keyValue;
        
        try {
            if (useFieldIndex) {
                // 使用字段索引
                if (keyFieldIndex < 0 || keyFieldIndex >= row.getArity()) {
                    log.error("字段索引超出范围: index={}, arity={}", keyFieldIndex, row.getArity());
                    return new byte[0];
                }
                keyValue = row.getField(keyFieldIndex);
            } else {
                // 使用字段名称（需要Row支持字段名称访问）
                keyValue = getFieldByName(row, keyFieldName);
            }
            
            if (keyValue == null) {
                log.warn("键字段值为null: {}", useFieldIndex ? "index=" + keyFieldIndex : "name=" + keyFieldName);
                return new byte[0];
            }
            
            String keyString = keyValue.toString();
            return keyString.getBytes(StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("序列化Row键失败: {}", e.getMessage(), e);
            return new byte[0];
        }
    }
    
    /**
     * 根据字段名称获取字段值（简单实现）
     * 注意：这是一个简化实现，实际使用中可能需要更复杂的逻辑
     * 
     * @param row Row对象
     * @param fieldName 字段名称
     * @return 字段值
     */
    private Object getFieldByName(Row row, String fieldName) {
        // 这里是一个简化实现，实际项目中可能需要根据具体的Row类型来实现
        // 例如，如果Row有字段名称信息，可以通过反射或其他方式获取
        
        // 对于常见的字段名称，可以使用硬编码的索引映射
        switch (fieldName.toLowerCase()) {
            case "session_id":
            case "sessionid":
                return row.getField(0); // 假设session_id在索引0
            case "id":
                return row.getField(0);
            default:
                log.warn("不支持的字段名称: {}", fieldName);
                return null;
        }
    }
}
