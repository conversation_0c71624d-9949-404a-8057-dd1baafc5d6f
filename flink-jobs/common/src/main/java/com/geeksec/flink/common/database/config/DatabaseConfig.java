package com.geeksec.flink.common.database.config;

import com.geeksec.flink.common.constants.FlinkConfigConstants;
import lombok.Data;
import org.apache.flink.api.java.utils.ParameterTool;

import java.io.Serializable;
import java.util.Properties;

/**
 * 数据库配置基类
 * 提供所有数据库连接的通用配置属性
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
public abstract class DatabaseConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主机地址 */
    protected String host;

    /** 端口号 */
    protected int port;

    /** 数据库名 */
    protected String database;

    /** 用户名 */
    protected String username;

    /** 密码 */
    protected String password;

    /** 连接超时时间（毫秒） */
    protected int connectionTimeout = 30000;

    /** Socket 超时时间（毫秒） */
    protected int socketTimeout = 30000;

    /** 最大连接数 */
    protected int maxConnections = 20;

    /** 最小空闲连接数 */
    protected int minIdleConnections = 2;

    /** 最大空闲连接数 */
    protected int maxIdleConnections = 10;

    /** 连接池最大等待时间（毫秒） */
    protected long maxWaitTime = 10000L;

    /** 是否启用SSL */
    protected boolean sslEnabled = false;

    /**
     * 从 ParameterTool 加载通用配置
     * 
     * @param parameterTool 参数工具
     * @param prefix 配置前缀
     */
    protected void loadCommonConfig(ParameterTool parameterTool, String prefix) {
        this.host = parameterTool.get(prefix + ".host", this.host);
        this.port = parameterTool.getInt(prefix + ".port", this.port);
        this.database = parameterTool.get(prefix + ".database", this.database);
        this.username = parameterTool.get(prefix + ".username", this.username);
        this.password = parameterTool.get(prefix + ".password", this.password);
        this.connectionTimeout = parameterTool.getInt(prefix + ".connection.timeout", this.connectionTimeout);
        this.socketTimeout = parameterTool.getInt(prefix + ".socket.timeout", this.socketTimeout);
        this.maxConnections = parameterTool.getInt(prefix + ".pool.max.connections", this.maxConnections);
        this.minIdleConnections = parameterTool.getInt(prefix + ".pool.min.idle", this.minIdleConnections);
        this.maxIdleConnections = parameterTool.getInt(prefix + ".pool.max.idle", this.maxIdleConnections);
        this.maxWaitTime = parameterTool.getLong(prefix + ".pool.max.wait", this.maxWaitTime);
        this.sslEnabled = parameterTool.getBoolean(prefix + ".ssl.enabled", this.sslEnabled);
    }

    /**
     * 转换为 Properties 对象
     * 
     * @param prefix 配置前缀
     * @return Properties 对象
     */
    public Properties toProperties(String prefix) {
        Properties props = new Properties();
        props.setProperty(prefix + ".host", host);
        props.setProperty(prefix + ".port", String.valueOf(port));
        props.setProperty(prefix + ".database", database);
        props.setProperty(prefix + ".username", username);
        if (password != null) {
            props.setProperty(prefix + ".password", password);
        }
        props.setProperty(prefix + ".connection.timeout", String.valueOf(connectionTimeout));
        props.setProperty(prefix + ".socket.timeout", String.valueOf(socketTimeout));
        props.setProperty(prefix + ".pool.max.connections", String.valueOf(maxConnections));
        props.setProperty(prefix + ".pool.min.idle", String.valueOf(minIdleConnections));
        props.setProperty(prefix + ".pool.max.idle", String.valueOf(maxIdleConnections));
        props.setProperty(prefix + ".pool.max.wait", String.valueOf(maxWaitTime));
        props.setProperty(prefix + ".ssl.enabled", String.valueOf(sslEnabled));
        return props;
    }

    /**
     * 验证配置的有效性
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return host != null && !host.trim().isEmpty() &&
               port > 0 && port <= 65535 &&
               database != null && !database.trim().isEmpty() &&
               username != null && !username.trim().isEmpty() &&
               connectionTimeout > 0 &&
               socketTimeout > 0 &&
               maxConnections > 0 &&
               minIdleConnections >= 0 &&
               maxIdleConnections >= minIdleConnections &&
               maxWaitTime > 0;
    }

    /**
     * 获取连接字符串
     * 
     * @return 连接字符串
     */
    public abstract String getConnectionUrl();

    /**
     * 获取驱动类名
     * 
     * @return 驱动类名
     */
    public abstract String getDriverClassName();
}
