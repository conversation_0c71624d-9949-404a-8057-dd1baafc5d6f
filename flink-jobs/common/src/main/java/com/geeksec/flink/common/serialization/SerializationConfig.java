package com.geeksec.flink.common.serialization;

import lombok.Builder;
import lombok.Data;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 序列化配置类
 * 定义序列化和反序列化的各种配置选项
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
public class SerializationConfig {

    // ==================== JSON 配置 ====================

    /** 是否格式化输出 JSON */
    @Builder.Default
    private boolean prettyPrint = false;

    /** 是否忽略空值 */
    @Builder.Default
    private boolean ignoreNullValues = true;

    /** 是否忽略未知属性 */
    @Builder.Default
    private boolean ignoreUnknownProperties = true;

    /** 是否允许空值 */
    @Builder.Default
    private boolean allowNullValues = true;

    /** 是否将日期写为时间戳 */
    @Builder.Default
    private boolean writeDatesAsTimestamps = false;

    /** 是否使用枚举的 toString 方法 */
    @Builder.Default
    private boolean writeEnumsUsingToString = true;

    /** 是否读取枚举时使用 toString 方法 */
    @Builder.Default
    private boolean readEnumsUsingToString = true;

    /** 是否接受空字符串作为 null 对象 */
    @Builder.Default
    private boolean acceptEmptyStringAsNullObject = true;

    /** 是否在空 Bean 时失败 */
    @Builder.Default
    private boolean failOnEmptyBeans = false;

    /** 是否在基本类型为 null 时失败 */
    @Builder.Default
    private boolean failOnNullForPrimitives = false;

    /** 是否在数字用于枚举时失败 */
    @Builder.Default
    private boolean failOnNumbersForEnums = false;

    // ==================== 字符编码配置 ====================

    /** 字符编码 */
    @Builder.Default
    private Charset charset = StandardCharsets.UTF_8;

    // ==================== Protobuf 配置 ====================

    /** 是否保留 Proto 字段名 */
    @Builder.Default
    private boolean preservingProtoFieldNames = true;

    /** 是否包含默认值字段 */
    @Builder.Default
    private boolean includingDefaultValueFields = false;

    /** 是否忽略未知字段 */
    @Builder.Default
    private boolean ignoringUnknownFields = true;

    /** 是否省略不重要的空白字符 */
    @Builder.Default
    private boolean omittingInsignificantWhitespace = true;

    // ==================== 性能配置 ====================

    /** 是否启用缓存 */
    @Builder.Default
    private boolean cacheEnabled = true;

    /** 缓存最大大小 */
    @Builder.Default
    private int cacheMaxSize = 1000;

    /** 缓存过期时间（毫秒） */
    @Builder.Default
    private long cacheExpireTimeMs = 300000L; // 5分钟

    // ==================== 错误处理配置 ====================

    /** 是否在序列化错误时返回错误信息 */
    @Builder.Default
    private boolean returnErrorOnSerializationFailure = true;

    /** 是否在反序列化错误时返回 null */
    @Builder.Default
    private boolean returnNullOnDeserializationFailure = false;

    /** 是否记录序列化错误 */
    @Builder.Default
    private boolean logSerializationErrors = true;

    /** 是否记录反序列化错误 */
    @Builder.Default
    private boolean logDeserializationErrors = true;

    // ==================== 预定义配置 ====================

    /**
     * 创建默认配置
     * 
     * @return 默认配置
     */
    public static SerializationConfig defaultConfig() {
        return SerializationConfig.builder().build();
    }

    /**
     * 创建严格模式配置
     * 
     * @return 严格模式配置
     */
    public static SerializationConfig strictConfig() {
        return SerializationConfig.builder()
                .ignoreUnknownProperties(false)
                .allowNullValues(false)
                .failOnEmptyBeans(true)
                .failOnNullForPrimitives(true)
                .failOnNumbersForEnums(true)
                .returnNullOnDeserializationFailure(false)
                .build();
    }

    /**
     * 创建宽松模式配置
     * 
     * @return 宽松模式配置
     */
    public static SerializationConfig lenientConfig() {
        return SerializationConfig.builder()
                .ignoreUnknownProperties(true)
                .allowNullValues(true)
                .failOnEmptyBeans(false)
                .failOnNullForPrimitives(false)
                .failOnNumbersForEnums(false)
                .returnNullOnDeserializationFailure(true)
                .build();
    }

    /**
     * 创建格式化输出配置
     * 
     * @return 格式化输出配置
     */
    public static SerializationConfig prettyConfig() {
        return SerializationConfig.builder()
                .prettyPrint(true)
                .ignoreNullValues(false)
                .build();
    }

    /**
     * 创建高性能配置
     * 
     * @return 高性能配置
     */
    public static SerializationConfig performanceConfig() {
        return SerializationConfig.builder()
                .prettyPrint(false)
                .ignoreNullValues(true)
                .cacheEnabled(true)
                .cacheMaxSize(5000)
                .logSerializationErrors(false)
                .logDeserializationErrors(false)
                .build();
    }

    /**
     * 创建调试模式配置
     * 
     * @return 调试模式配置
     */
    public static SerializationConfig debugConfig() {
        return SerializationConfig.builder()
                .prettyPrint(true)
                .ignoreNullValues(false)
                .logSerializationErrors(true)
                .logDeserializationErrors(true)
                .returnErrorOnSerializationFailure(true)
                .returnNullOnDeserializationFailure(false)
                .build();
    }

    /**
     * 创建 Kafka 序列化配置
     * 
     * @return Kafka 序列化配置
     */
    public static SerializationConfig kafkaConfig() {
        return SerializationConfig.builder()
                .prettyPrint(false)
                .ignoreNullValues(true)
                .ignoreUnknownProperties(true)
                .allowNullValues(true)
                .cacheEnabled(true)
                .returnErrorOnSerializationFailure(false)
                .returnNullOnDeserializationFailure(true)
                .build();
    }

    /**
     * 创建 Protobuf 配置
     * 
     * @return Protobuf 配置
     */
    public static SerializationConfig protobufConfig() {
        return SerializationConfig.builder()
                .preservingProtoFieldNames(true)
                .includingDefaultValueFields(false)
                .ignoringUnknownFields(true)
                .omittingInsignificantWhitespace(true)
                .cacheEnabled(true)
                .build();
    }

    // ==================== 配置验证 ====================

    /**
     * 验证配置的有效性
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        // 检查缓存配置
        if (cacheEnabled && cacheMaxSize <= 0) {
            return false;
        }
        
        if (cacheEnabled && cacheExpireTimeMs <= 0) {
            return false;
        }
        
        // 检查字符编码
        if (charset == null) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取配置摘要
     * 
     * @return 配置摘要
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("SerializationConfig{");
        sb.append("prettyPrint=").append(prettyPrint);
        sb.append(", ignoreNullValues=").append(ignoreNullValues);
        sb.append(", ignoreUnknownProperties=").append(ignoreUnknownProperties);
        sb.append(", charset=").append(charset.name());
        sb.append(", cacheEnabled=").append(cacheEnabled);
        if (cacheEnabled) {
            sb.append(", cacheMaxSize=").append(cacheMaxSize);
        }
        sb.append("}");
        return sb.toString();
    }

    // ==================== 配置复制和修改 ====================

    /**
     * 创建配置的副本
     * 
     * @return 配置副本
     */
    public SerializationConfig copy() {
        return SerializationConfig.builder()
                .prettyPrint(this.prettyPrint)
                .ignoreNullValues(this.ignoreNullValues)
                .ignoreUnknownProperties(this.ignoreUnknownProperties)
                .allowNullValues(this.allowNullValues)
                .writeDatesAsTimestamps(this.writeDatesAsTimestamps)
                .writeEnumsUsingToString(this.writeEnumsUsingToString)
                .readEnumsUsingToString(this.readEnumsUsingToString)
                .acceptEmptyStringAsNullObject(this.acceptEmptyStringAsNullObject)
                .failOnEmptyBeans(this.failOnEmptyBeans)
                .failOnNullForPrimitives(this.failOnNullForPrimitives)
                .failOnNumbersForEnums(this.failOnNumbersForEnums)
                .charset(this.charset)
                .preservingProtoFieldNames(this.preservingProtoFieldNames)
                .includingDefaultValueFields(this.includingDefaultValueFields)
                .ignoringUnknownFields(this.ignoringUnknownFields)
                .omittingInsignificantWhitespace(this.omittingInsignificantWhitespace)
                .cacheEnabled(this.cacheEnabled)
                .cacheMaxSize(this.cacheMaxSize)
                .cacheExpireTimeMs(this.cacheExpireTimeMs)
                .returnErrorOnSerializationFailure(this.returnErrorOnSerializationFailure)
                .returnNullOnDeserializationFailure(this.returnNullOnDeserializationFailure)
                .logSerializationErrors(this.logSerializationErrors)
                .logDeserializationErrors(this.logDeserializationErrors)
                .build();
    }

    /**
     * 基于当前配置创建格式化版本
     * 
     * @return 格式化配置
     */
    public SerializationConfig withPrettyPrint() {
        SerializationConfig config = copy();
        config.setPrettyPrint(true);
        config.setIgnoreNullValues(false);
        return config;
    }

    /**
     * 基于当前配置创建严格版本
     * 
     * @return 严格配置
     */
    public SerializationConfig withStrictMode() {
        SerializationConfig config = copy();
        config.setIgnoreUnknownProperties(false);
        config.setAllowNullValues(false);
        config.setFailOnEmptyBeans(true);
        config.setFailOnNullForPrimitives(true);
        config.setReturnNullOnDeserializationFailure(false);
        return config;
    }

    /**
     * 基于当前配置创建高性能版本
     * 
     * @return 高性能配置
     */
    public SerializationConfig withPerformanceMode() {
        SerializationConfig config = copy();
        config.setPrettyPrint(false);
        config.setIgnoreNullValues(true);
        config.setCacheEnabled(true);
        config.setCacheMaxSize(5000);
        config.setLogSerializationErrors(false);
        config.setLogDeserializationErrors(false);
        return config;
    }
}
