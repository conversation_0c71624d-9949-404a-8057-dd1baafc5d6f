package com.geeksec.flink.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.concurrent.TimeUnit;

/**
 * 时间工具类
 * 提供 Flink 作业中常用的时间处理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class TimeUtils {

    private TimeUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 常用格式化器 ====================

    /** 默认日期时间格式 */
    public static final String DEFAULT_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /** 默认日期格式 */
    public static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd";

    /** 默认时间格式 */
    public static final String DEFAULT_TIME_PATTERN = "HH:mm:ss";

    /** ISO 日期时间格式 */
    public static final String ISO_DATETIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss";

    /** 紧凑日期时间格式 */
    public static final String COMPACT_DATETIME_PATTERN = "yyyyMMddHHmmss";

    /** 默认时区 */
    public static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();

    /** 默认日期时间格式化器 */
    public static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = 
            DateTimeFormatter.ofPattern(DEFAULT_DATETIME_PATTERN);

    /** 默认日期格式化器 */
    public static final DateTimeFormatter DEFAULT_DATE_FORMATTER = 
            DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN);

    /** 默认时间格式化器 */
    public static final DateTimeFormatter DEFAULT_TIME_FORMATTER = 
            DateTimeFormatter.ofPattern(DEFAULT_TIME_PATTERN);

    /** ISO 日期时间格式化器 */
    public static final DateTimeFormatter ISO_DATETIME_FORMATTER = 
            DateTimeFormatter.ofPattern(ISO_DATETIME_PATTERN);

    /** 紧凑日期时间格式化器 */
    public static final DateTimeFormatter COMPACT_DATETIME_FORMATTER = 
            DateTimeFormatter.ofPattern(COMPACT_DATETIME_PATTERN);

    // ==================== 当前时间获取 ====================

    /**
     * 获取当前时间
     * 
     * @return 当前 LocalDateTime
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }

    /**
     * 获取当前日期
     * 
     * @return 当前 LocalDate
     */
    public static LocalDate today() {
        return LocalDate.now();
    }

    /**
     * 获取当前时间戳（秒）
     * 
     * @return 当前时间戳（秒）
     */
    public static long nowAsSeconds() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 获取当前时间戳（毫秒）
     * 
     * @return 当前时间戳（毫秒）
     */
    public static long nowAsMillis() {
        return System.currentTimeMillis();
    }

    // ==================== 时间格式化 ====================

    /**
     * 格式化当前时间为默认格式
     * 
     * @return 格式化后的时间字符串
     */
    public static String formatNow() {
        return format(now());
    }

    /**
     * 格式化时间为默认格式
     * 
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串
     */
    public static String format(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DEFAULT_DATETIME_FORMATTER) : null;
    }

    /**
     * 格式化时间为指定格式
     * 
     * @param dateTime 时间对象
     * @param pattern 格式模式
     * @return 格式化后的时间字符串
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || pattern == null) {
            return null;
        }
        try {
            return dateTime.format(DateTimeFormatter.ofPattern(pattern));
        } catch (Exception e) {
            log.warn("时间格式化失败: dateTime={}, pattern={}", dateTime, pattern, e);
            return null;
        }
    }

    /**
     * 格式化日期为默认格式
     * 
     * @param date 日期对象
     * @return 格式化后的日期字符串
     */
    public static String format(LocalDate date) {
        return date != null ? date.format(DEFAULT_DATE_FORMATTER) : null;
    }

    /**
     * 格式化时间戳为默认格式
     * 
     * @param timestamp 时间戳（秒）
     * @return 格式化后的时间字符串
     */
    public static String formatTimestamp(long timestamp) {
        return format(fromTimestamp(timestamp));
    }

    /**
     * 格式化时间戳为指定格式
     * 
     * @param timestamp 时间戳（秒）
     * @param pattern 格式模式
     * @return 格式化后的时间字符串
     */
    public static String formatTimestamp(long timestamp, String pattern) {
        return format(fromTimestamp(timestamp), pattern);
    }

    // ==================== 时间解析 ====================

    /**
     * 解析时间字符串为 LocalDateTime
     * 
     * @param timeStr 时间字符串
     * @return LocalDateTime 对象
     */
    public static LocalDateTime parse(String timeStr) {
        return parse(timeStr, DEFAULT_DATETIME_PATTERN);
    }

    /**
     * 解析时间字符串为 LocalDateTime
     * 
     * @param timeStr 时间字符串
     * @param pattern 格式模式
     * @return LocalDateTime 对象
     */
    public static LocalDateTime parse(String timeStr, String pattern) {
        if (timeStr == null || timeStr.trim().isEmpty() || pattern == null) {
            return null;
        }
        try {
            return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern(pattern));
        } catch (DateTimeParseException e) {
            log.warn("时间解析失败: timeStr={}, pattern={}", timeStr, pattern, e);
            return null;
        }
    }

    /**
     * 解析日期字符串为 LocalDate
     * 
     * @param dateStr 日期字符串
     * @return LocalDate 对象
     */
    public static LocalDate parseDate(String dateStr) {
        return parseDate(dateStr, DEFAULT_DATE_PATTERN);
    }

    /**
     * 解析日期字符串为 LocalDate
     * 
     * @param dateStr 日期字符串
     * @param pattern 格式模式
     * @return LocalDate 对象
     */
    public static LocalDate parseDate(String dateStr, String pattern) {
        if (dateStr == null || dateStr.trim().isEmpty() || pattern == null) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
        } catch (DateTimeParseException e) {
            log.warn("日期解析失败: dateStr={}, pattern={}", dateStr, pattern, e);
            return null;
        }
    }

    // ==================== 时间戳转换 ====================

    /**
     * LocalDateTime 转时间戳（秒）
     * 
     * @param dateTime 时间对象
     * @return 时间戳（秒）
     */
    public static long toTimestamp(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(DEFAULT_ZONE).toEpochSecond() : 0;
    }

    /**
     * LocalDateTime 转时间戳（毫秒）
     * 
     * @param dateTime 时间对象
     * @return 时间戳（毫秒）
     */
    public static long toTimestampMillis(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(DEFAULT_ZONE).toInstant().toEpochMilli() : 0;
    }

    /**
     * 时间戳（秒）转 LocalDateTime
     * 
     * @param timestamp 时间戳（秒）
     * @return LocalDateTime 对象
     */
    public static LocalDateTime fromTimestamp(long timestamp) {
        try {
            return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), DEFAULT_ZONE);
        } catch (Exception e) {
            log.warn("时间戳转换失败: timestamp={}", timestamp, e);
            return null;
        }
    }

    /**
     * 时间戳（毫秒）转 LocalDateTime
     * 
     * @param timestampMillis 时间戳（毫秒）
     * @return LocalDateTime 对象
     */
    public static LocalDateTime fromTimestampMillis(long timestampMillis) {
        try {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestampMillis), DEFAULT_ZONE);
        } catch (Exception e) {
            log.warn("时间戳转换失败: timestampMillis={}", timestampMillis, e);
            return null;
        }
    }

    // ==================== 时间计算 ====================

    /**
     * 计算两个时间之间的秒数差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 秒数差
     */
    public static long secondsBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return Duration.between(start, end).getSeconds();
    }

    /**
     * 计算两个时间之间的分钟差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 分钟差
     */
    public static long minutesBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return Duration.between(start, end).toMinutes();
    }

    /**
     * 计算两个时间之间的小时差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 小时差
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return Duration.between(start, end).toHours();
    }

    /**
     * 计算两个日期之间的天数差
     * 
     * @param start 开始日期
     * @param end 结束日期
     * @return 天数差
     */
    public static long daysBetween(LocalDate start, LocalDate end) {
        if (start == null || end == null) {
            return 0;
        }
        return Period.between(start, end).getDays();
    }

    // ==================== 时间判断 ====================

    /**
     * 判断时间是否在指定范围内
     * 
     * @param time 待判断时间
     * @param start 开始时间
     * @param end 结束时间
     * @return 是否在范围内
     */
    public static boolean isBetween(LocalDateTime time, LocalDateTime start, LocalDateTime end) {
        if (time == null || start == null || end == null) {
            return false;
        }
        return !time.isBefore(start) && !time.isAfter(end);
    }

    /**
     * 判断时间是否为今天
     * 
     * @param dateTime 时间对象
     * @return 是否为今天
     */
    public static boolean isToday(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        return dateTime.toLocalDate().equals(LocalDate.now());
    }

    /**
     * 判断时间是否为过去
     * 
     * @param dateTime 时间对象
     * @return 是否为过去
     */
    public static boolean isPast(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        return dateTime.isBefore(LocalDateTime.now());
    }

    /**
     * 判断时间是否为未来
     * 
     * @param dateTime 时间对象
     * @return 是否为未来
     */
    public static boolean isFuture(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        return dateTime.isAfter(LocalDateTime.now());
    }

    // ==================== 时间单位转换 ====================

    /**
     * 毫秒转秒
     * 
     * @param millis 毫秒
     * @return 秒
     */
    public static long millisToSeconds(long millis) {
        return TimeUnit.MILLISECONDS.toSeconds(millis);
    }

    /**
     * 秒转毫秒
     * 
     * @param seconds 秒
     * @return 毫秒
     */
    public static long secondsToMillis(long seconds) {
        return TimeUnit.SECONDS.toMillis(seconds);
    }

    /**
     * 分钟转秒
     * 
     * @param minutes 分钟
     * @return 秒
     */
    public static long minutesToSeconds(long minutes) {
        return TimeUnit.MINUTES.toSeconds(minutes);
    }

    /**
     * 小时转秒
     * 
     * @param hours 小时
     * @return 秒
     */
    public static long hoursToSeconds(long hours) {
        return TimeUnit.HOURS.toSeconds(hours);
    }

    /**
     * 天转秒
     * 
     * @param days 天
     * @return 秒
     */
    public static long daysToSeconds(long days) {
        return TimeUnit.DAYS.toSeconds(days);
    }
}
