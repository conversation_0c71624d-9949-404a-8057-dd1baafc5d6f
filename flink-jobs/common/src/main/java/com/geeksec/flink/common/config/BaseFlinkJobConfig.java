package com.geeksec.flink.common.config;

import com.geeksec.flink.common.constants.FlinkConfigConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;

import java.io.Serializable;

/**
 * Flink 作业基础配置类
 * 提供所有 Flink 作业共享的基础配置属性和方法
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Slf4j
public abstract class BaseFlinkJobConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 作业基础配置 ====================

    /** 作业名称 */
    protected String jobName;

    /** 作业并行度 */
    protected int jobParallelism = FlinkConfigConstants.DEFAULT_JOB_PARALLELISM;

    /** 检查点间隔（毫秒） */
    protected long checkpointInterval = FlinkConfigConstants.DEFAULT_CHECKPOINT_INTERVAL;

    /** 检查点超时（毫秒） */
    protected long checkpointTimeout = FlinkConfigConstants.DEFAULT_CHECKPOINT_TIMEOUT;

    /** 重启尝试次数 */
    protected int restartAttempts = FlinkConfigConstants.DEFAULT_RESTART_ATTEMPTS;

    /** 重启延迟（毫秒） */
    protected long restartDelay = FlinkConfigConstants.DEFAULT_RESTART_DELAY;

    // ==================== Kafka 配置 ====================

    /** Kafka Bootstrap Servers */
    protected String kafkaBootstrapServers = FlinkConfigConstants.DEFAULT_KAFKA_BOOTSTRAP_SERVERS;

    /** Consumer Group ID */
    protected String kafkaGroupId;

    /** 起始偏移量 */
    protected String startingOffsets = FlinkConfigConstants.DEFAULT_KAFKA_STARTING_OFFSETS;

    /** 自动提交 */
    protected boolean autoCommit = FlinkConfigConstants.DEFAULT_KAFKA_AUTO_COMMIT;

    /** 提交间隔（毫秒） */
    protected long commitInterval = FlinkConfigConstants.DEFAULT_KAFKA_COMMIT_INTERVAL;

    // ==================== 并行度配置 ====================

    /** Kafka 源并行度 */
    protected int kafkaSourceParallelism = FlinkConfigConstants.DEFAULT_PARALLELISM_KAFKA_SOURCE;

    /** 解析操作并行度 */
    protected int parsingParallelism = FlinkConfigConstants.DEFAULT_PARALLELISM_PARSING;

    /** 数据汇并行度 */
    protected int sinkParallelism = FlinkConfigConstants.DEFAULT_PARALLELISM_SINK;

    // ==================== 数据库配置 ====================

    /** PostgreSQL 主机 */
    protected String postgresqlHost = FlinkConfigConstants.DEFAULT_POSTGRESQL_HOST;

    /** PostgreSQL 端口 */
    protected int postgresqlPort = FlinkConfigConstants.DEFAULT_POSTGRESQL_PORT;

    /** PostgreSQL 数据库名 */
    protected String postgresqlDatabase = FlinkConfigConstants.DEFAULT_POSTGRESQL_DATABASE;

    /** PostgreSQL 用户名 */
    protected String postgresqlUsername = FlinkConfigConstants.DEFAULT_POSTGRESQL_USERNAME;

    /** PostgreSQL 密码 */
    protected String postgresqlPassword;

    /** Doris FE 节点 */
    protected String dorisFenodes = FlinkConfigConstants.DEFAULT_DORIS_FENODES;

    /** Doris 数据库名 */
    protected String dorisDatabase = FlinkConfigConstants.DEFAULT_DORIS_DATABASE;

    /** Doris 用户名 */
    protected String dorisUsername = FlinkConfigConstants.DEFAULT_DORIS_USERNAME;

    /** Doris 密码 */
    protected String dorisPassword = FlinkConfigConstants.DEFAULT_DORIS_PASSWORD;

    // ==================== Redis 配置 ====================

    /** Redis 主机 */
    protected String redisHost = FlinkConfigConstants.DEFAULT_REDIS_HOST;

    /** Redis 端口 */
    protected int redisPort = FlinkConfigConstants.DEFAULT_REDIS_PORT;

    /** Redis 密码 */
    protected String redisPassword;

    /** Redis 数据库索引 */
    protected int redisDatabase = FlinkConfigConstants.DEFAULT_REDIS_DATABASE;

    /** Redis 连接超时（毫秒） */
    protected int redisConnectionTimeout = FlinkConfigConstants.DEFAULT_REDIS_CONNECTION_TIMEOUT;

    /** Redis Socket 超时（毫秒） */
    protected int redisSocketTimeout = FlinkConfigConstants.DEFAULT_REDIS_SOCKET_TIMEOUT;

    // ==================== 监控配置 ====================

    /** 是否启用监控 */
    protected boolean monitoringEnabled = FlinkConfigConstants.DEFAULT_MONITORING_ENABLED;

    /** 指标间隔（毫秒） */
    protected long metricsInterval = FlinkConfigConstants.DEFAULT_METRICS_INTERVAL;

    /** 是否启用性能日志 */
    protected boolean performanceLogging = FlinkConfigConstants.DEFAULT_PERFORMANCE_LOGGING_ENABLED;

    /** 是否启用详细指标 */
    protected boolean detailedMetrics = FlinkConfigConstants.DEFAULT_DETAILED_METRICS_ENABLED;

    // ==================== 调试配置 ====================

    /** 是否启用调试模式 */
    protected boolean debugEnabled = FlinkConfigConstants.DEFAULT_DEBUG_ENABLED;

    /**
     * 从 ParameterTool 加载配置
     * 
     * @param parameterTool 参数工具
     */
    public void loadFromParameterTool(ParameterTool parameterTool) {
        // 作业基础配置
        this.jobName = parameterTool.get(FlinkConfigConstants.JOB_NAME, this.jobName);
        this.jobParallelism = parameterTool.getInt(FlinkConfigConstants.JOB_PARALLELISM, this.jobParallelism);
        this.checkpointInterval = parameterTool.getLong(FlinkConfigConstants.CHECKPOINT_INTERVAL, this.checkpointInterval);
        this.checkpointTimeout = parameterTool.getLong(FlinkConfigConstants.CHECKPOINT_TIMEOUT, this.checkpointTimeout);
        this.restartAttempts = parameterTool.getInt(FlinkConfigConstants.RESTART_ATTEMPTS, this.restartAttempts);
        this.restartDelay = parameterTool.getLong(FlinkConfigConstants.RESTART_DELAY, this.restartDelay);

        // Kafka 配置
        this.kafkaBootstrapServers = parameterTool.get(FlinkConfigConstants.KAFKA_BOOTSTRAP_SERVERS, this.kafkaBootstrapServers);
        this.kafkaGroupId = parameterTool.get(FlinkConfigConstants.KAFKA_GROUP_ID, this.kafkaGroupId);
        this.startingOffsets = parameterTool.get(FlinkConfigConstants.KAFKA_STARTING_OFFSETS, this.startingOffsets);
        this.autoCommit = parameterTool.getBoolean(FlinkConfigConstants.KAFKA_AUTO_COMMIT, this.autoCommit);
        this.commitInterval = parameterTool.getLong(FlinkConfigConstants.KAFKA_COMMIT_INTERVAL, this.commitInterval);

        // 并行度配置
        this.kafkaSourceParallelism = parameterTool.getInt(FlinkConfigConstants.PARALLELISM_KAFKA_SOURCE, this.kafkaSourceParallelism);
        this.parsingParallelism = parameterTool.getInt(FlinkConfigConstants.PARALLELISM_PARSING, this.parsingParallelism);
        this.sinkParallelism = parameterTool.getInt(FlinkConfigConstants.PARALLELISM_SINK, this.sinkParallelism);

        // 数据库配置
        this.postgresqlHost = parameterTool.get(FlinkConfigConstants.POSTGRESQL_HOST, this.postgresqlHost);
        this.postgresqlPort = parameterTool.getInt(FlinkConfigConstants.POSTGRESQL_PORT, this.postgresqlPort);
        this.postgresqlDatabase = parameterTool.get(FlinkConfigConstants.POSTGRESQL_DATABASE, this.postgresqlDatabase);
        this.postgresqlUsername = parameterTool.get(FlinkConfigConstants.POSTGRESQL_USERNAME, this.postgresqlUsername);
        this.postgresqlPassword = parameterTool.get(FlinkConfigConstants.POSTGRESQL_PASSWORD, this.postgresqlPassword);

        this.dorisFenodes = parameterTool.get(FlinkConfigConstants.DORIS_FENODES, this.dorisFenodes);
        this.dorisDatabase = parameterTool.get(FlinkConfigConstants.DORIS_DATABASE, this.dorisDatabase);
        this.dorisUsername = parameterTool.get(FlinkConfigConstants.DORIS_USERNAME, this.dorisUsername);
        this.dorisPassword = parameterTool.get(FlinkConfigConstants.DORIS_PASSWORD, this.dorisPassword);

        // Redis 配置
        this.redisHost = parameterTool.get(FlinkConfigConstants.REDIS_HOST, this.redisHost);
        this.redisPort = parameterTool.getInt(FlinkConfigConstants.REDIS_PORT, this.redisPort);
        this.redisPassword = parameterTool.get(FlinkConfigConstants.REDIS_PASSWORD, this.redisPassword);
        this.redisDatabase = parameterTool.getInt(FlinkConfigConstants.REDIS_DATABASE, this.redisDatabase);
        this.redisConnectionTimeout = parameterTool.getInt(FlinkConfigConstants.REDIS_CONNECTION_TIMEOUT, this.redisConnectionTimeout);
        this.redisSocketTimeout = parameterTool.getInt(FlinkConfigConstants.REDIS_SOCKET_TIMEOUT, this.redisSocketTimeout);

        // 监控配置
        this.monitoringEnabled = parameterTool.getBoolean(FlinkConfigConstants.MONITORING_ENABLED, this.monitoringEnabled);
        this.metricsInterval = parameterTool.getLong(FlinkConfigConstants.METRICS_INTERVAL, this.metricsInterval);
        this.performanceLogging = parameterTool.getBoolean(FlinkConfigConstants.PERFORMANCE_LOGGING_ENABLED, this.performanceLogging);
        this.detailedMetrics = parameterTool.getBoolean(FlinkConfigConstants.DETAILED_METRICS_ENABLED, this.detailedMetrics);

        // 调试配置
        this.debugEnabled = parameterTool.getBoolean(FlinkConfigConstants.DEBUG_ENABLED, this.debugEnabled);
    }

    /**
     * 创建配置实例
     * 
     * @return 配置实例
     */
    public static BaseFlinkJobConfig create() {
        return new BaseFlinkJobConfig() {};
    }

    /**
     * 从 ParameterTool 创建配置实例
     * 
     * @param parameterTool 参数工具
     * @return 配置实例
     */
    public static BaseFlinkJobConfig create(ParameterTool parameterTool) {
        BaseFlinkJobConfig config = create();
        config.loadFromParameterTool(parameterTool);
        return config;
    }

    /**
     * 验证配置的有效性
     * 
     * @return 配置验证结果
     */
    public ConfigValidationResult validate() {
        ConfigValidationResult result = new ConfigValidationResult();

        // 验证作业名称
        if (jobName == null || jobName.trim().isEmpty()) {
            result.addError("作业名称不能为空");
        }

        // 验证并行度配置
        if (jobParallelism <= 0) {
            result.addError("作业并行度必须大于 0，当前值: %d", jobParallelism);
        } else if (jobParallelism > FlinkConfigConstants.MAX_JOB_PARALLELISM) {
            result.addWarning("作业并行度 %d 超过推荐最大值 %d", jobParallelism, FlinkConfigConstants.MAX_JOB_PARALLELISM);
        }

        // 验证检查点配置
        if (checkpointInterval <= 0) {
            result.addError("检查点间隔必须大于 0，当前值: %d", checkpointInterval);
        }

        if (checkpointTimeout <= checkpointInterval) {
            result.addError("检查点超时时间 %d 应该大于检查点间隔 %d", checkpointTimeout, checkpointInterval);
        }

        // 验证重启策略配置
        if (restartAttempts < 0) {
            result.addError("重启尝试次数不能为负数，当前值: %d", restartAttempts);
        }

        if (restartDelay < 0) {
            result.addError("重启延迟不能为负数，当前值: %d", restartDelay);
        }

        // 验证 Kafka 配置
        if (kafkaBootstrapServers == null || kafkaBootstrapServers.trim().isEmpty()) {
            result.addError("Kafka Bootstrap Servers 不能为空");
        }

        // 验证数据库配置
        validateDatabaseConfig(result);

        return result;
    }

    /**
     * 验证数据库配置
     * 
     * @param result 验证结果
     */
    protected void validateDatabaseConfig(ConfigValidationResult result) {
        // 验证 PostgreSQL 配置
        if (postgresqlHost == null || postgresqlHost.trim().isEmpty()) {
            result.addError("PostgreSQL 主机不能为空");
        }

        if (postgresqlPort <= 0 || postgresqlPort > 65535) {
            result.addError("PostgreSQL 端口必须在 1-65535 范围内，当前值: %d", postgresqlPort);
        }

        if (postgresqlDatabase == null || postgresqlDatabase.trim().isEmpty()) {
            result.addError("PostgreSQL 数据库名不能为空");
        }

        if (postgresqlUsername == null || postgresqlUsername.trim().isEmpty()) {
            result.addError("PostgreSQL 用户名不能为空");
        }

        // 验证 Doris 配置
        if (dorisFenodes == null || dorisFenodes.trim().isEmpty()) {
            result.addError("Doris FE 节点不能为空");
        }

        if (dorisDatabase == null || dorisDatabase.trim().isEmpty()) {
            result.addError("Doris 数据库名不能为空");
        }

        if (dorisUsername == null || dorisUsername.trim().isEmpty()) {
            result.addError("Doris 用户名不能为空");
        }

        // 验证 Redis 配置
        if (redisHost == null || redisHost.trim().isEmpty()) {
            result.addError("Redis 主机不能为空");
        }

        if (redisPort <= 0 || redisPort > 65535) {
            result.addError("Redis 端口必须在 1-65535 范围内，当前值: %d", redisPort);
        }
    }

    /**
     * 打印配置信息
     */
    public void printConfig() {
        log.info("=== Flink 作业配置信息 ===");
        log.info("作业名称: {}", jobName);
        log.info("作业并行度: {}", jobParallelism);
        log.info("检查点间隔: {}ms", checkpointInterval);
        log.info("检查点超时: {}ms", checkpointTimeout);
        log.info("重启尝试次数: {}", restartAttempts);
        log.info("重启延迟: {}ms", restartDelay);
        log.info("Kafka Bootstrap Servers: {}", kafkaBootstrapServers);
        log.info("Kafka Group ID: {}", kafkaGroupId);
        log.info("PostgreSQL: {}:{}/{}", postgresqlHost, postgresqlPort, postgresqlDatabase);
        log.info("Doris: {}/{}", dorisFenodes, dorisDatabase);
        log.info("Redis: {}:{}/{}", redisHost, redisPort, redisDatabase);
        log.info("监控启用: {}", monitoringEnabled);
        log.info("调试模式: {}", debugEnabled);
        log.info("========================");
    }
}
