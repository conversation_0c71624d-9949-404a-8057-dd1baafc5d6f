package com.geeksec.flink.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 验证工具类
 * 提供 Flink 作业中常用的数据验证功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class ValidationUtils {

    private ValidationUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 常用正则表达式 ====================

    /** IPv4 地址正则表达式 */
    private static final Pattern IPV4_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

    /** IPv6 地址正则表达式 */
    private static final Pattern IPV6_PATTERN = Pattern.compile(
            "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$");

    /** MAC 地址正则表达式 */
    private static final Pattern MAC_PATTERN = Pattern.compile(
            "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$");

    /** 域名正则表达式 */
    private static final Pattern DOMAIN_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?(\\.([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?))*$");

    /** 邮箱正则表达式 */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    /** URL 正则表达式 */
    private static final Pattern URL_PATTERN = Pattern.compile(
            "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$");

    /** 手机号正则表达式（中国） */
    private static final Pattern PHONE_PATTERN = Pattern.compile(
            "^1[3-9]\\d{9}$");

    /** 身份证号正则表达式（中国） */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(
            "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");

    // ==================== 基础验证 ====================

    /**
     * 验证对象是否不为空
     * 
     * @param obj 对象
     * @return 是否不为空
     */
    public static boolean isNotNull(Object obj) {
        return obj != null;
    }

    /**
     * 验证字符串是否不为空且不为空白
     * 
     * @param str 字符串
     * @return 是否不为空白
     */
    public static boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }

    /**
     * 验证集合是否不为空
     * 
     * @param collection 集合
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return collection != null && !collection.isEmpty();
    }

    /**
     * 验证 Map 是否不为空
     * 
     * @param map 映射
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return map != null && !map.isEmpty();
    }

    /**
     * 验证数组是否不为空
     * 
     * @param array 数组
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Object[] array) {
        return array != null && array.length > 0;
    }

    // ==================== 网络地址验证 ====================

    /**
     * 验证是否为有效的 IPv4 地址
     * 
     * @param ip IP 地址字符串
     * @return 是否为有效 IPv4 地址
     */
    public static boolean isValidIPv4(String ip) {
        if (!isNotBlank(ip)) {
            return false;
        }
        
        if (!IPV4_PATTERN.matcher(ip).matches()) {
            return false;
        }
        
        try {
            InetAddress.getByName(ip);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 验证是否为有效的 IPv6 地址
     * 
     * @param ip IP 地址字符串
     * @return 是否为有效 IPv6 地址
     */
    public static boolean isValidIPv6(String ip) {
        if (!isNotBlank(ip)) {
            return false;
        }
        
        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr instanceof java.net.Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 验证是否为有效的 IP 地址（IPv4 或 IPv6）
     * 
     * @param ip IP 地址字符串
     * @return 是否为有效 IP 地址
     */
    public static boolean isValidIP(String ip) {
        return isValidIPv4(ip) || isValidIPv6(ip);
    }

    /**
     * 验证是否为有效的 MAC 地址
     * 
     * @param mac MAC 地址字符串
     * @return 是否为有效 MAC 地址
     */
    public static boolean isValidMAC(String mac) {
        return isNotBlank(mac) && MAC_PATTERN.matcher(mac).matches();
    }

    /**
     * 验证是否为有效的域名
     * 
     * @param domain 域名字符串
     * @return 是否为有效域名
     */
    public static boolean isValidDomain(String domain) {
        if (!isNotBlank(domain)) {
            return false;
        }
        
        if (domain.length() > 253) {
            return false;
        }
        
        return DOMAIN_PATTERN.matcher(domain).matches();
    }

    /**
     * 验证是否为有效的邮箱地址
     * 
     * @param email 邮箱地址字符串
     * @return 是否为有效邮箱
     */
    public static boolean isValidEmail(String email) {
        return isNotBlank(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证是否为有效的 URL
     * 
     * @param url URL 字符串
     * @return 是否为有效 URL
     */
    public static boolean isValidURL(String url) {
        return isNotBlank(url) && URL_PATTERN.matcher(url).matches();
    }

    // ==================== 端口验证 ====================

    /**
     * 验证是否为有效的端口号
     * 
     * @param port 端口号
     * @return 是否为有效端口号
     */
    public static boolean isValidPort(int port) {
        return port >= 1 && port <= 65535;
    }

    /**
     * 验证是否为有效的端口号字符串
     * 
     * @param portStr 端口号字符串
     * @return 是否为有效端口号
     */
    public static boolean isValidPort(String portStr) {
        if (!isNotBlank(portStr)) {
            return false;
        }
        
        try {
            int port = Integer.parseInt(portStr);
            return isValidPort(port);
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证是否为知名端口（1-1023）
     * 
     * @param port 端口号
     * @return 是否为知名端口
     */
    public static boolean isWellKnownPort(int port) {
        return port >= 1 && port <= 1023;
    }

    /**
     * 验证是否为注册端口（1024-49151）
     * 
     * @param port 端口号
     * @return 是否为注册端口
     */
    public static boolean isRegisteredPort(int port) {
        return port >= 1024 && port <= 49151;
    }

    /**
     * 验证是否为动态端口（49152-65535）
     * 
     * @param port 端口号
     * @return 是否为动态端口
     */
    public static boolean isDynamicPort(int port) {
        return port >= 49152 && port <= 65535;
    }

    // ==================== 数值验证 ====================

    /**
     * 验证数值是否在指定范围内
     * 
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @return 是否在范围内
     */
    public static boolean isInRange(Number value, Number min, Number max) {
        if (value == null || min == null || max == null) {
            return false;
        }
        
        double val = value.doubleValue();
        double minVal = min.doubleValue();
        double maxVal = max.doubleValue();
        
        return val >= minVal && val <= maxVal;
    }

    /**
     * 验证是否为正数
     * 
     * @param value 数值
     * @return 是否为正数
     */
    public static boolean isPositive(Number value) {
        return value != null && value.doubleValue() > 0;
    }

    /**
     * 验证是否为非负数
     * 
     * @param value 数值
     * @return 是否为非负数
     */
    public static boolean isNonNegative(Number value) {
        return value != null && value.doubleValue() >= 0;
    }

    /**
     * 验证是否为负数
     * 
     * @param value 数值
     * @return 是否为负数
     */
    public static boolean isNegative(Number value) {
        return value != null && value.doubleValue() < 0;
    }

    // ==================== 时间验证 ====================

    /**
     * 验证时间是否在指定范围内
     * 
     * @param time 时间
     * @param start 开始时间
     * @param end 结束时间
     * @return 是否在范围内
     */
    public static boolean isTimeInRange(LocalDateTime time, LocalDateTime start, LocalDateTime end) {
        if (time == null || start == null || end == null) {
            return false;
        }
        
        return !time.isBefore(start) && !time.isAfter(end);
    }

    /**
     * 验证时间是否为过去
     * 
     * @param time 时间
     * @return 是否为过去
     */
    public static boolean isPastTime(LocalDateTime time) {
        return time != null && time.isBefore(LocalDateTime.now());
    }

    /**
     * 验证时间是否为未来
     * 
     * @param time 时间
     * @return 是否为未来
     */
    public static boolean isFutureTime(LocalDateTime time) {
        return time != null && time.isAfter(LocalDateTime.now());
    }

    // ==================== 字符串格式验证 ====================

    /**
     * 验证是否为有效的手机号（中国）
     * 
     * @param phone 手机号字符串
     * @return 是否为有效手机号
     */
    public static boolean isValidPhone(String phone) {
        return isNotBlank(phone) && PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 验证是否为有效的身份证号（中国）
     * 
     * @param idCard 身份证号字符串
     * @return 是否为有效身份证号
     */
    public static boolean isValidIdCard(String idCard) {
        return isNotBlank(idCard) && ID_CARD_PATTERN.matcher(idCard).matches();
    }

    /**
     * 验证字符串长度是否在指定范围内
     * 
     * @param str 字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否在范围内
     */
    public static boolean isLengthInRange(String str, int minLength, int maxLength) {
        if (str == null) {
            return minLength <= 0;
        }
        
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证字符串是否只包含字母
     * 
     * @param str 字符串
     * @return 是否只包含字母
     */
    public static boolean isAlpha(String str) {
        if (!isNotBlank(str)) {
            return false;
        }
        
        return str.chars().allMatch(Character::isLetter);
    }

    /**
     * 验证字符串是否只包含数字
     * 
     * @param str 字符串
     * @return 是否只包含数字
     */
    public static boolean isNumeric(String str) {
        if (!isNotBlank(str)) {
            return false;
        }
        
        return str.chars().allMatch(Character::isDigit);
    }

    /**
     * 验证字符串是否只包含字母和数字
     * 
     * @param str 字符串
     * @return 是否只包含字母和数字
     */
    public static boolean isAlphanumeric(String str) {
        if (!isNotBlank(str)) {
            return false;
        }
        
        return str.chars().allMatch(Character::isLetterOrDigit);
    }

    // ==================== Row 验证 ====================

    /**
     * 验证 Row 是否包含指定字段
     * 
     * @param row Row 对象
     * @param fieldName 字段名
     * @return 是否包含字段
     */
    public static boolean hasField(Row row, String fieldName) {
        if (row == null || !isNotBlank(fieldName)) {
            return false;
        }
        
        try {
            String[] fieldNames = row.getFieldNames(true);
            if (fieldNames == null) {
                return false;
            }
            
            for (String name : fieldNames) {
                if (fieldName.equals(name)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.debug("检查字段是否存在时发生错误: fieldName={}", fieldName, e);
            return false;
        }
    }

    /**
     * 验证 Row 中的字段值是否不为空
     * 
     * @param row Row 对象
     * @param fieldName 字段名
     * @return 字段值是否不为空
     */
    public static boolean isFieldNotNull(Row row, String fieldName) {
        if (!hasField(row, fieldName)) {
            return false;
        }
        
        try {
            Object value = row.getFieldAs(fieldName);
            return value != null;
        } catch (Exception e) {
            log.debug("获取字段值时发生错误: fieldName={}", fieldName, e);
            return false;
        }
    }

    /**
     * 验证 Row 中的字符串字段是否不为空白
     * 
     * @param row Row 对象
     * @param fieldName 字段名
     * @return 字符串字段是否不为空白
     */
    public static boolean isStringFieldNotBlank(Row row, String fieldName) {
        if (!isFieldNotNull(row, fieldName)) {
            return false;
        }
        
        try {
            Object value = row.getFieldAs(fieldName);
            return value instanceof String && isNotBlank((String) value);
        } catch (Exception e) {
            log.debug("验证字符串字段时发生错误: fieldName={}", fieldName, e);
            return false;
        }
    }

    // ==================== 组合验证 ====================

    /**
     * 验证所有条件是否都为真
     * 
     * @param conditions 条件数组
     * @return 是否所有条件都为真
     */
    public static boolean allTrue(boolean... conditions) {
        if (conditions == null || conditions.length == 0) {
            return true;
        }
        
        for (boolean condition : conditions) {
            if (!condition) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证是否至少有一个条件为真
     * 
     * @param conditions 条件数组
     * @return 是否至少有一个条件为真
     */
    public static boolean anyTrue(boolean... conditions) {
        if (conditions == null || conditions.length == 0) {
            return false;
        }
        
        for (boolean condition : conditions) {
            if (condition) {
                return true;
            }
        }
        return false;
    }
}
