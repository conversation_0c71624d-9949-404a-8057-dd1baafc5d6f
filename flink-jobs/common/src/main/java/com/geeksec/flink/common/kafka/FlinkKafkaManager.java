package com.geeksec.flink.common.kafka;

import com.geeksec.flink.common.kafka.config.KafkaConfig;
import com.geeksec.flink.common.kafka.serialization.FieldKeySerializationSchema;
import com.geeksec.flink.common.kafka.serialization.JsonSerializationSchema;
import com.geeksec.flink.common.kafka.sink.KafkaSinkFactory;
import com.geeksec.flink.common.kafka.source.KafkaSourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import org.apache.flink.types.Row;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * Flink Kafka 管理器
 * 提供 Kafka 源和汇的统一管理和配置
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class FlinkKafkaManager {

    private final KafkaConfig config;

    /**
     * 构造函数
     * 
     * @param config Kafka 配置
     */
    public FlinkKafkaManager(KafkaConfig config) {
        this.config = config;
        log.info("Flink Kafka 管理器已初始化: {}", config);
    }

    // ==================== 源管理方法 ====================

    /**
     * 创建字符串数据流
     * 
     * @param env Flink 执行环境
     * @param topics 主题列表
     * @return 字符串数据流
     */
    public DataStream<String> createStringStream(StreamExecutionEnvironment env, List<String> topics) {
        return createStringStream(env, topics, "latest");
    }

    /**
     * 创建字符串数据流（指定偏移量策略）
     * 
     * @param env Flink 执行环境
     * @param topics 主题列表
     * @param offsetStrategy 偏移量策略
     * @return 字符串数据流
     */
    public DataStream<String> createStringStream(StreamExecutionEnvironment env, List<String> topics, String offsetStrategy) {
        OffsetsInitializer offsetsInitializer = KafkaSourceFactory.getOffsetsInitializer(offsetStrategy);
        KafkaSource<String> source = KafkaSourceFactory.createStringSource(config, topics, offsetsInitializer);
        
        return env.fromSource(source,
                org.apache.flink.api.common.eventtime.WatermarkStrategy.noWatermarks(),
                "Kafka String Source: " + String.join(",", topics))
                .setParallelism(getKafkaSourceParallelism());
    }

    /**
     * 创建字符串数据流（单个主题）
     * 
     * @param env Flink 执行环境
     * @param topic 主题名称
     * @return 字符串数据流
     */
    public DataStream<String> createStringStream(StreamExecutionEnvironment env, String topic) {
        return createStringStream(env, Arrays.asList(topic));
    }

    /**
     * 创建自定义类型数据流
     * 
     * @param env Flink 执行环境
     * @param topics 主题列表
     * @param deserializer 反序列化器
     * @param <T> 数据类型
     * @return 自定义类型数据流
     */
    public <T> DataStream<T> createStream(StreamExecutionEnvironment env, List<String> topics, 
                                          DeserializationSchema<T> deserializer) {
        return createStream(env, topics, deserializer, "latest");
    }

    /**
     * 创建自定义类型数据流（指定偏移量策略）
     * 
     * @param env Flink 执行环境
     * @param topics 主题列表
     * @param deserializer 反序列化器
     * @param offsetStrategy 偏移量策略
     * @param <T> 数据类型
     * @return 自定义类型数据流
     */
    public <T> DataStream<T> createStream(StreamExecutionEnvironment env, List<String> topics, 
                                          DeserializationSchema<T> deserializer, String offsetStrategy) {
        OffsetsInitializer offsetsInitializer = KafkaSourceFactory.getOffsetsInitializer(offsetStrategy);
        KafkaSource<T> source = KafkaSourceFactory.createSource(config, topics, deserializer, offsetsInitializer);
        
        return env.fromSource(source,
                org.apache.flink.api.common.eventtime.WatermarkStrategy.noWatermarks(),
                "Kafka Custom Source: " + String.join(",", topics))
                .setParallelism(getKafkaSourceParallelism());
    }

    /**
     * 创建自定义类型数据流（单个主题）
     * 
     * @param env Flink 执行环境
     * @param topic 主题名称
     * @param deserializer 反序列化器
     * @param <T> 数据类型
     * @return 自定义类型数据流
     */
    public <T> DataStream<T> createStream(StreamExecutionEnvironment env, String topic, 
                                          DeserializationSchema<T> deserializer) {
        return createStream(env, Arrays.asList(topic), deserializer);
    }

    // ==================== 汇管理方法 ====================

    /**
     * 创建字符串 Kafka 汇
     * 
     * @param topic 主题名称
     * @return Kafka 汇
     */
    public KafkaSink<String> createStringSink(String topic) {
        return createStringSink(topic, "at_least_once");
    }

    /**
     * 创建字符串 Kafka 汇（指定投递保证）
     * 
     * @param topic 主题名称
     * @param deliveryGuarantee 投递保证
     * @return Kafka 汇
     */
    public KafkaSink<String> createStringSink(String topic, String deliveryGuarantee) {
        DeliveryGuarantee guarantee = KafkaSinkFactory.getDeliveryGuarantee(deliveryGuarantee);
        return KafkaSinkFactory.createStringSink(config, topic, guarantee);
    }

    /**
     * 创建 JSON Kafka 汇
     * 
     * @param topic 主题名称
     * @param <T> 数据类型
     * @return Kafka 汇
     */
    public <T> KafkaSink<T> createJsonSink(String topic) {
        return createJsonSink(topic, "at_least_once");
    }

    /**
     * 创建 JSON Kafka 汇（指定投递保证）
     * 
     * @param topic 主题名称
     * @param deliveryGuarantee 投递保证
     * @param <T> 数据类型
     * @return Kafka 汇
     */
    public <T> KafkaSink<T> createJsonSink(String topic, String deliveryGuarantee) {
        DeliveryGuarantee guarantee = KafkaSinkFactory.getDeliveryGuarantee(deliveryGuarantee);
        JsonSerializationSchema<T> serializer = new JsonSerializationSchema<>();
        return KafkaSinkFactory.createSink(config, topic, serializer, guarantee);
    }

    /**
     * 创建带键的 JSON Kafka 汇（Row 类型，使用会话 ID 作为键）
     * 
     * @param topic 主题名称
     * @param sessionIdField 会话 ID 字段名或索引
     * @return Kafka 汇
     */
    public KafkaSink<Row> createJsonSinkWithSessionKey(String topic, String sessionIdField) {
        return createJsonSinkWithSessionKey(topic, sessionIdField, "at_least_once");
    }

    /**
     * 创建带键的 JSON Kafka 汇（Row 类型，使用会话 ID 作为键，指定投递保证）
     * 
     * @param topic 主题名称
     * @param sessionIdField 会话 ID 字段名或索引
     * @param deliveryGuarantee 投递保证
     * @return Kafka 汇
     */
    public KafkaSink<Row> createJsonSinkWithSessionKey(String topic, String sessionIdField, String deliveryGuarantee) {
        DeliveryGuarantee guarantee = KafkaSinkFactory.getDeliveryGuarantee(deliveryGuarantee);
        FieldKeySerializationSchema<Row> keySerializer = FieldKeySerializationSchema.forRowSessionId(sessionIdField);
        JsonSerializationSchema<Row> valueSerializer = new JsonSerializationSchema<>();
        return KafkaSinkFactory.createSinkWithKey(config, topic, keySerializer, valueSerializer, guarantee);
    }

    /**
     * 创建自定义序列化器的 Kafka 汇
     * 
     * @param topic 主题名称
     * @param serializer 序列化器
     * @param <T> 数据类型
     * @return Kafka 汇
     */
    public <T> KafkaSink<T> createSink(String topic, SerializationSchema<T> serializer) {
        return createSink(topic, serializer, "at_least_once");
    }

    /**
     * 创建自定义序列化器的 Kafka 汇（指定投递保证）
     * 
     * @param topic 主题名称
     * @param serializer 序列化器
     * @param deliveryGuarantee 投递保证
     * @param <T> 数据类型
     * @return Kafka 汇
     */
    public <T> KafkaSink<T> createSink(String topic, SerializationSchema<T> serializer, String deliveryGuarantee) {
        DeliveryGuarantee guarantee = KafkaSinkFactory.getDeliveryGuarantee(deliveryGuarantee);
        return KafkaSinkFactory.createSink(config, topic, serializer, guarantee);
    }

    /**
     * 创建带自定义键的 Kafka 汇
     * 
     * @param topic 主题名称
     * @param keyExtractor 键提取函数
     * @param <T> 数据类型
     * @return Kafka 汇
     */
    public <T> KafkaSink<T> createJsonSinkWithKey(String topic, Function<T, String> keyExtractor) {
        return createJsonSinkWithKey(topic, keyExtractor, "at_least_once");
    }

    /**
     * 创建带自定义键的 Kafka 汇（指定投递保证）
     * 
     * @param topic 主题名称
     * @param keyExtractor 键提取函数
     * @param deliveryGuarantee 投递保证
     * @param <T> 数据类型
     * @return Kafka 汇
     */
    public <T> KafkaSink<T> createJsonSinkWithKey(String topic, Function<T, String> keyExtractor, String deliveryGuarantee) {
        DeliveryGuarantee guarantee = KafkaSinkFactory.getDeliveryGuarantee(deliveryGuarantee);
        FieldKeySerializationSchema<T> keySerializer = FieldKeySerializationSchema.forField(keyExtractor, "default");
        JsonSerializationSchema<T> valueSerializer = new JsonSerializationSchema<>();
        return KafkaSinkFactory.createSinkWithKey(config, topic, keySerializer, valueSerializer, guarantee);
    }

    // ==================== 便捷方法 ====================

    /**
     * 将数据流发送到 Kafka（字符串类型）
     * 
     * @param stream 数据流
     * @param topic 主题名称
     * @param sinkName 汇名称
     */
    public void sinkToKafka(DataStream<String> stream, String topic, String sinkName) {
        sinkToKafka(stream, topic, sinkName, getSinkParallelism());
    }

    /**
     * 将数据流发送到 Kafka（字符串类型，指定并行度）
     * 
     * @param stream 数据流
     * @param topic 主题名称
     * @param sinkName 汇名称
     * @param parallelism 并行度
     */
    public void sinkToKafka(DataStream<String> stream, String topic, String sinkName, int parallelism) {
        KafkaSink<String> sink = createStringSink(topic);
        stream.sinkTo(sink)
              .name(sinkName)
              .setParallelism(parallelism);
    }

    /**
     * 将数据流发送到 Kafka（JSON 类型）
     * 
     * @param stream 数据流
     * @param topic 主题名称
     * @param sinkName 汇名称
     * @param <T> 数据类型
     */
    public <T> void sinkJsonToKafka(DataStream<T> stream, String topic, String sinkName) {
        sinkJsonToKafka(stream, topic, sinkName, getSinkParallelism());
    }

    /**
     * 将数据流发送到 Kafka（JSON 类型，指定并行度）
     * 
     * @param stream 数据流
     * @param topic 主题名称
     * @param sinkName 汇名称
     * @param parallelism 并行度
     * @param <T> 数据类型
     */
    public <T> void sinkJsonToKafka(DataStream<T> stream, String topic, String sinkName, int parallelism) {
        KafkaSink<T> sink = createJsonSink(topic);
        stream.sinkTo(sink)
              .name(sinkName)
              .setParallelism(parallelism);
    }

    /**
     * 获取 Kafka 配置
     * 
     * @return Kafka 配置
     */
    public KafkaConfig getConfig() {
        return config;
    }

    // 添加缺失的方法
    private int getKafkaSourceParallelism() {
        return 2; // 默认值，可以从配置中获取
    }

    private int getSinkParallelism() {
        return 2; // 默认值，可以从配置中获取
    }
}
