package com.geeksec.flink.common.metrics;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.metrics.Histogram;
import org.apache.flink.metrics.MetricGroup;

import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控器
 * 提供系统性能指标的监控和报告功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class PerformanceMonitor {

    /** 内存管理 Bean */
    private final MemoryMXBean memoryMXBean;
    
    /** GC 管理 Bean */
    private final java.util.List<GarbageCollectorMXBean> gcMXBeans;
    
    /** 调度执行器 */
    private final ScheduledExecutorService scheduler;
    
    /** 是否已启动 */
    private volatile boolean started = false;
    
    /** 监控间隔（秒） */
    private final long monitoringIntervalSeconds;
    
    // ==================== 性能指标 ====================
    
    /** 堆内存使用量 */
    private Gauge<Long> heapMemoryUsed;
    
    /** 堆内存最大值 */
    private Gauge<Long> heapMemoryMax;
    
    /** 堆内存使用率 */
    private Gauge<Double> heapMemoryUsagePercent;
    
    /** 非堆内存使用量 */
    private Gauge<Long> nonHeapMemoryUsed;
    
    /** 非堆内存最大值 */
    private Gauge<Long> nonHeapMemoryMax;
    
    /** GC 次数 */
    private Counter gcCount;
    
    /** GC 时间 */
    private Counter gcTime;
    
    /** 处理延迟 */
    private Histogram processingLatency;
    
    /** 上次 GC 统计 */
    private final AtomicLong lastGcCount = new AtomicLong(0);
    private final AtomicLong lastGcTime = new AtomicLong(0);

    /**
     * 构造函数
     * 
     * @param monitoringIntervalSeconds 监控间隔（秒）
     */
    public PerformanceMonitor(long monitoringIntervalSeconds) {
        this.monitoringIntervalSeconds = monitoringIntervalSeconds;
        this.memoryMXBean = ManagementFactory.getMemoryMXBean();
        this.gcMXBeans = ManagementFactory.getGarbageCollectorMXBeans();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "performance-monitor");
            t.setDaemon(true);
            return t;
        });
    }

    /**
     * 默认构造函数（30秒监控间隔）
     */
    public PerformanceMonitor() {
        this(30);
    }

    /**
     * 初始化性能监控指标
     * 
     * @param metricGroup 指标分组
     */
    public void initializeMetrics(MetricGroup metricGroup) {
        MetricGroup performanceGroup = metricGroup.addGroup(MetricConstants.GROUP_PERFORMANCE);
        
        // 内存指标
        this.heapMemoryUsed = MetricUtils.createGauge(performanceGroup, 
                MetricConstants.GAUGE_HEAP_MEMORY_USED, this::getHeapMemoryUsed);
        
        this.heapMemoryMax = MetricUtils.createGauge(performanceGroup, 
                "heap_memory_max_bytes", this::getHeapMemoryMax);
        
        this.heapMemoryUsagePercent = MetricUtils.createGauge(performanceGroup, 
                MetricConstants.GAUGE_MEMORY_USAGE, this::getHeapMemoryUsagePercent);
        
        this.nonHeapMemoryUsed = MetricUtils.createGauge(performanceGroup, 
                MetricConstants.GAUGE_NON_HEAP_MEMORY_USED, this::getNonHeapMemoryUsed);
        
        this.nonHeapMemoryMax = MetricUtils.createGauge(performanceGroup, 
                "non_heap_memory_max_bytes", this::getNonHeapMemoryMax);
        
        // GC 指标
        this.gcCount = MetricUtils.createCounter(performanceGroup, MetricConstants.COUNTER_GC_COUNT);
        this.gcTime = MetricUtils.createCounter(performanceGroup, MetricConstants.COUNTER_GC_TIME);
        
        // 处理延迟指标
        this.processingLatency = MetricUtils.createHistogram(performanceGroup, 
                MetricConstants.HISTOGRAM_PROCESSING_LATENCY);
        
        log.info("性能监控指标初始化完成，监控间隔: {}秒", monitoringIntervalSeconds);
    }

    /**
     * 启动性能监控
     */
    public void start() {
        if (started) {
            log.warn("性能监控已经启动");
            return;
        }
        
        // 初始化 GC 统计
        initializeGcStats();
        
        // 启动定期监控任务
        scheduler.scheduleAtFixedRate(this::collectMetrics, 
                monitoringIntervalSeconds, monitoringIntervalSeconds, TimeUnit.SECONDS);
        
        started = true;
        log.info("性能监控已启动");
    }

    /**
     * 停止性能监控
     */
    public void stop() {
        if (!started) {
            return;
        }
        
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        started = false;
        log.info("性能监控已停止");
    }

    /**
     * 记录处理延迟
     * 
     * @param latencyMs 延迟时间（毫秒）
     */
    public void recordProcessingLatency(long latencyMs) {
        if (processingLatency != null) {
            MetricUtils.safeUpdate(processingLatency, latencyMs);
        }
    }

    /**
     * 测量并记录操作执行时间
     * 
     * @param operation 操作名称
     * @param runnable 可执行操作
     */
    public void measureAndRecord(String operation, Runnable runnable) {
        long startTime = System.currentTimeMillis();
        try {
            runnable.run();
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            recordProcessingLatency(duration);
            log.debug("操作 {} 执行时间: {}ms", operation, duration);
        }
    }

    /**
     * 测量并记录操作执行时间（返回结果）
     * 
     * @param operation 操作名称
     * @param supplier 供应商
     * @param <T> 返回类型
     * @return 执行结果
     */
    public <T> T measureAndRecord(String operation, java.util.function.Supplier<T> supplier) {
        long startTime = System.currentTimeMillis();
        try {
            return supplier.get();
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            recordProcessingLatency(duration);
            log.debug("操作 {} 执行时间: {}ms", operation, duration);
        }
    }

    // ==================== 内存指标获取 ====================

    /**
     * 获取堆内存使用量
     * 
     * @return 堆内存使用量（字节）
     */
    private Long getHeapMemoryUsed() {
        try {
            MemoryUsage heapMemory = memoryMXBean.getHeapMemoryUsage();
            return heapMemory.getUsed();
        } catch (Exception e) {
            log.debug("获取堆内存使用量失败", e);
            return 0L;
        }
    }

    /**
     * 获取堆内存最大值
     * 
     * @return 堆内存最大值（字节）
     */
    private Long getHeapMemoryMax() {
        try {
            MemoryUsage heapMemory = memoryMXBean.getHeapMemoryUsage();
            return heapMemory.getMax();
        } catch (Exception e) {
            log.debug("获取堆内存最大值失败", e);
            return 0L;
        }
    }

    /**
     * 获取堆内存使用率
     * 
     * @return 堆内存使用率（百分比）
     */
    private Double getHeapMemoryUsagePercent() {
        try {
            MemoryUsage heapMemory = memoryMXBean.getHeapMemoryUsage();
            long used = heapMemory.getUsed();
            long max = heapMemory.getMax();
            if (max > 0) {
                return (double) used / max * 100.0;
            }
        } catch (Exception e) {
            log.debug("获取堆内存使用率失败", e);
        }
        return 0.0;
    }

    /**
     * 获取非堆内存使用量
     * 
     * @return 非堆内存使用量（字节）
     */
    private Long getNonHeapMemoryUsed() {
        try {
            MemoryUsage nonHeapMemory = memoryMXBean.getNonHeapMemoryUsage();
            return nonHeapMemory.getUsed();
        } catch (Exception e) {
            log.debug("获取非堆内存使用量失败", e);
            return 0L;
        }
    }

    /**
     * 获取非堆内存最大值
     * 
     * @return 非堆内存最大值（字节）
     */
    private Long getNonHeapMemoryMax() {
        try {
            MemoryUsage nonHeapMemory = memoryMXBean.getNonHeapMemoryUsage();
            return nonHeapMemory.getMax();
        } catch (Exception e) {
            log.debug("获取非堆内存最大值失败", e);
            return 0L;
        }
    }

    // ==================== GC 指标处理 ====================

    /**
     * 初始化 GC 统计
     */
    private void initializeGcStats() {
        long totalGcCount = 0;
        long totalGcTime = 0;
        
        for (GarbageCollectorMXBean gcBean : gcMXBeans) {
            totalGcCount += gcBean.getCollectionCount();
            totalGcTime += gcBean.getCollectionTime();
        }
        
        lastGcCount.set(totalGcCount);
        lastGcTime.set(totalGcTime);
    }

    /**
     * 收集指标
     */
    private void collectMetrics() {
        try {
            collectGcMetrics();
            logPerformanceStats();
        } catch (Exception e) {
            log.warn("收集性能指标失败", e);
        }
    }

    /**
     * 收集 GC 指标
     */
    private void collectGcMetrics() {
        long totalGcCount = 0;
        long totalGcTime = 0;
        
        for (GarbageCollectorMXBean gcBean : gcMXBeans) {
            totalGcCount += gcBean.getCollectionCount();
            totalGcTime += gcBean.getCollectionTime();
        }
        
        long gcCountDelta = totalGcCount - lastGcCount.get();
        long gcTimeDelta = totalGcTime - lastGcTime.get();
        
        if (gcCountDelta > 0) {
            MetricUtils.safeIncrement(gcCount, gcCountDelta);
        }
        
        if (gcTimeDelta > 0) {
            MetricUtils.safeIncrement(gcTime, gcTimeDelta);
        }
        
        lastGcCount.set(totalGcCount);
        lastGcTime.set(totalGcTime);
    }

    /**
     * 记录性能统计日志
     */
    private void logPerformanceStats() {
        if (log.isDebugEnabled()) {
            long heapUsed = getHeapMemoryUsed();
            long heapMax = getHeapMemoryMax();
            double heapUsagePercent = getHeapMemoryUsagePercent();
            long nonHeapUsed = getNonHeapMemoryUsed();
            
            log.debug("性能统计 - 堆内存: {}/{}MB ({}%), 非堆内存: {}MB", 
                    heapUsed / 1024 / 1024, 
                    heapMax / 1024 / 1024, 
                    String.format("%.2f", heapUsagePercent),
                    nonHeapUsed / 1024 / 1024);
        }
    }

    // ==================== 健康检查 ====================

    /**
     * 检查系统健康状态
     * 
     * @return 健康状态
     */
    public HealthStatus checkHealth() {
        HealthStatus.Builder builder = HealthStatus.builder();
        
        // 检查内存使用率
        double heapUsagePercent = getHeapMemoryUsagePercent();
        if (heapUsagePercent > 90.0) {
            builder.addIssue("堆内存使用率过高: " + String.format("%.2f%%", heapUsagePercent));
        } else if (heapUsagePercent > 80.0) {
            builder.addWarning("堆内存使用率较高: " + String.format("%.2f%%", heapUsagePercent));
        }
        
        // 检查 GC 频率
        long currentGcCount = lastGcCount.get();
        if (currentGcCount > 0) {
            // 这里可以添加更复杂的 GC 频率检查逻辑
            builder.addInfo("GC 总次数: " + currentGcCount);
        }
        
        return builder.build();
    }

    /**
     * 健康状态类
     */
    public static class HealthStatus {
        private final boolean healthy;
        private final java.util.List<String> issues;
        private final java.util.List<String> warnings;
        private final java.util.List<String> infos;

        private HealthStatus(boolean healthy, java.util.List<String> issues, 
                           java.util.List<String> warnings, java.util.List<String> infos) {
            this.healthy = healthy;
            this.issues = issues;
            this.warnings = warnings;
            this.infos = infos;
        }

        public boolean isHealthy() { return healthy; }
        public java.util.List<String> getIssues() { return issues; }
        public java.util.List<String> getWarnings() { return warnings; }
        public java.util.List<String> getInfos() { return infos; }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private final java.util.List<String> issues = new java.util.ArrayList<>();
            private final java.util.List<String> warnings = new java.util.ArrayList<>();
            private final java.util.List<String> infos = new java.util.ArrayList<>();

            public Builder addIssue(String issue) {
                issues.add(issue);
                return this;
            }

            public Builder addWarning(String warning) {
                warnings.add(warning);
                return this;
            }

            public Builder addInfo(String info) {
                infos.add(info);
                return this;
            }

            public HealthStatus build() {
                return new HealthStatus(issues.isEmpty(), 
                        new java.util.ArrayList<>(issues),
                        new java.util.ArrayList<>(warnings),
                        new java.util.ArrayList<>(infos));
            }
        }
    }
}
