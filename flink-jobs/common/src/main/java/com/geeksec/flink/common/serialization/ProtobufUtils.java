package com.geeksec.flink.common.serialization;

import com.geeksec.flink.common.exception.ErrorCode;
import com.geeksec.flink.common.exception.FlinkJobException;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Protobuf 序列化工具类
 * 提供 Protobuf 消息的序列化、反序列化和转换功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class ProtobufUtils {

    private ProtobufUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 解析器缓存 ====================

    /** Parser 缓存 */
    private static final ConcurrentHashMap<Class<?>, Parser<?>> PARSER_CACHE = new ConcurrentHashMap<>();

    /** JsonFormat.Printer 缓存 */
    private static final ConcurrentHashMap<String, JsonFormat.Printer> PRINTER_CACHE = new ConcurrentHashMap<>();

    /** JsonFormat.Parser 缓存 */
    private static final ConcurrentHashMap<String, JsonFormat.Parser> JSON_PARSER_CACHE = new ConcurrentHashMap<>();

    // ==================== 二进制序列化 ====================

    /**
     * 将 Protobuf 消息序列化为字节数组
     * 
     * @param message Protobuf 消息
     * @return 字节数组
     */
    public static byte[] serialize(Message message) {
        if (message == null) {
            return new byte[0];
        }
        
        try {
            return message.toByteArray();
        } catch (Exception e) {
            throw new FlinkJobException(ErrorCode.OBJECT_SERIALIZATION_FAILED, e, 
                    "Protobuf 消息序列化失败: %s", message.getClass().getSimpleName());
        }
    }

    /**
     * 将字节数组反序列化为 Protobuf 消息
     * 
     * @param bytes 字节数组
     * @param messageClass 消息类型
     * @param <T> 消息类型参数
     * @return Protobuf 消息
     * @throws FlinkJobException 反序列化失败时抛出
     */
    @SuppressWarnings("unchecked")
    public static <T extends Message> T deserialize(byte[] bytes, Class<T> messageClass) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        
        try {
            Parser<T> parser = (Parser<T>) getParser(messageClass);
            return parser.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            throw new FlinkJobException(ErrorCode.OBJECT_DESERIALIZATION_FAILED, e, 
                    "Protobuf 消息反序列化失败: %s", messageClass.getSimpleName());
        }
    }

    /**
     * 安全地将字节数组反序列化为 Protobuf 消息
     * 
     * @param bytes 字节数组
     * @param messageClass 消息类型
     * @param defaultValue 默认值
     * @param <T> 消息类型参数
     * @return Protobuf 消息或默认值
     */
    public static <T extends Message> T deserializeSafe(byte[] bytes, Class<T> messageClass, T defaultValue) {
        try {
            return deserialize(bytes, messageClass);
        } catch (Exception e) {
            log.debug("Protobuf 反序列化失败，使用默认值: class={}", messageClass.getSimpleName(), e);
            return defaultValue;
        }
    }

    // ==================== JSON 转换 ====================

    /**
     * 将 Protobuf 消息转换为 JSON 字符串
     * 
     * @param message Protobuf 消息
     * @return JSON 字符串
     * @throws FlinkJobException 转换失败时抛出
     */
    public static String toJson(Message message) {
        if (message == null) {
            return null;
        }
        
        try {
            JsonFormat.Printer printer = getJsonPrinter("default");
            return printer.print(message);
        } catch (InvalidProtocolBufferException e) {
            throw new FlinkJobException(ErrorCode.JSON_SERIALIZATION_FAILED, e, 
                    "Protobuf 消息转换为 JSON 失败: %s", message.getClass().getSimpleName());
        }
    }

    /**
     * 将 Protobuf 消息转换为格式化的 JSON 字符串
     * 
     * @param message Protobuf 消息
     * @return 格式化的 JSON 字符串
     * @throws FlinkJobException 转换失败时抛出
     */
    public static String toPrettyJson(Message message) {
        if (message == null) {
            return null;
        }
        
        try {
            JsonFormat.Printer printer = getJsonPrinter("pretty");
            return printer.print(message);
        } catch (InvalidProtocolBufferException e) {
            throw new FlinkJobException(ErrorCode.JSON_SERIALIZATION_FAILED, e, 
                    "Protobuf 消息转换为格式化 JSON 失败: %s", message.getClass().getSimpleName());
        }
    }

    /**
     * 将 JSON 字符串转换为 Protobuf 消息
     * 
     * @param json JSON 字符串
     * @param messageClass 消息类型
     * @param <T> 消息类型参数
     * @return Protobuf 消息
     * @throws FlinkJobException 转换失败时抛出
     */
    public static <T extends Message> T fromJson(String json, Class<T> messageClass) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            JsonFormat.Parser parser = getJsonParser("default");
            Message.Builder builder = getDefaultInstance(messageClass).toBuilder();
            parser.merge(json, builder);
            @SuppressWarnings("unchecked")
            T result = (T) builder.build();
            return result;
        } catch (InvalidProtocolBufferException e) {
            throw new FlinkJobException(ErrorCode.JSON_DESERIALIZATION_FAILED, e, 
                    "JSON 转换为 Protobuf 消息失败: %s", messageClass.getSimpleName());
        }
    }

    /**
     * 安全地将 JSON 字符串转换为 Protobuf 消息
     * 
     * @param json JSON 字符串
     * @param messageClass 消息类型
     * @param defaultValue 默认值
     * @param <T> 消息类型参数
     * @return Protobuf 消息或默认值
     */
    public static <T extends Message> T fromJsonSafe(String json, Class<T> messageClass, T defaultValue) {
        try {
            return fromJson(json, messageClass);
        } catch (Exception e) {
            log.debug("JSON 转换为 Protobuf 失败，使用默认值: json={}, class={}", 
                    json, messageClass.getSimpleName(), e);
            return defaultValue;
        }
    }

    // ==================== 消息操作 ====================

    /**
     * 深拷贝 Protobuf 消息
     * 
     * @param message 原消息
     * @param <T> 消息类型参数
     * @return 深拷贝后的消息
     */
    @SuppressWarnings("unchecked")
    public static <T extends Message> T deepCopy(T message) {
        if (message == null) {
            return null;
        }
        
        return (T) message.toBuilder().build();
    }

    /**
     * 合并两个 Protobuf 消息
     * 
     * @param base 基础消息
     * @param overlay 覆盖消息
     * @param <T> 消息类型参数
     * @return 合并后的消息
     */
    @SuppressWarnings("unchecked")
    public static <T extends Message> T merge(T base, T overlay) {
        if (base == null) {
            return overlay;
        }
        if (overlay == null) {
            return base;
        }
        
        return (T) base.toBuilder().mergeFrom(overlay).build();
    }

    /**
     * 检查 Protobuf 消息是否为空（所有字段都是默认值）
     * 
     * @param message Protobuf 消息
     * @return 是否为空
     */
    public static boolean isEmpty(Message message) {
        if (message == null) {
            return true;
        }
        
        return message.equals(message.getDefaultInstanceForType());
    }

    /**
     * 获取 Protobuf 消息的大小（字节数）
     * 
     * @param message Protobuf 消息
     * @return 消息大小
     */
    public static int getSerializedSize(Message message) {
        if (message == null) {
            return 0;
        }
        
        return message.getSerializedSize();
    }

    // ==================== 缓存和工具方法 ====================

    /**
     * 获取消息类型的 Parser
     * 
     * @param messageClass 消息类型
     * @param <T> 消息类型参数
     * @return Parser 实例
     */
    @SuppressWarnings("unchecked")
    private static <T extends Message> Parser<T> getParser(Class<T> messageClass) {
        return (Parser<T>) PARSER_CACHE.computeIfAbsent(messageClass, clazz -> {
            try {
                Method parserMethod = clazz.getMethod("parser");
                return (Parser<?>) parserMethod.invoke(null);
            } catch (Exception e) {
                throw new FlinkJobException(ErrorCode.CONFIGURATION_ERROR, e, 
                        "无法获取 Protobuf Parser: %s", clazz.getSimpleName());
            }
        });
    }

    /**
     * 获取消息类型的默认实例
     * 
     * @param messageClass 消息类型
     * @param <T> 消息类型参数
     * @return 默认实例
     */
    @SuppressWarnings("unchecked")
    private static <T extends Message> T getDefaultInstance(Class<T> messageClass) {
        try {
            Method getDefaultInstanceMethod = messageClass.getMethod("getDefaultInstance");
            return (T) getDefaultInstanceMethod.invoke(null);
        } catch (Exception e) {
            throw new FlinkJobException(ErrorCode.CONFIGURATION_ERROR, e, 
                    "无法获取 Protobuf 默认实例: %s", messageClass.getSimpleName());
        }
    }

    /**
     * 获取 JSON Printer
     * 
     * @param config 配置键
     * @return JsonFormat.Printer 实例
     */
    private static JsonFormat.Printer getJsonPrinter(String config) {
        return PRINTER_CACHE.computeIfAbsent(config, key -> {
            JsonFormat.Printer printer = JsonFormat.printer()
                    .omittingInsignificantWhitespace()
                    .preservingProtoFieldNames()
                    .includingDefaultValueFields();
            
            if ("pretty".equals(key)) {
                // 注意：JsonFormat.Printer 没有直接的格式化选项
                // 如果需要格式化，可以在外部处理
                return printer;
            }
            
            return printer;
        });
    }

    /**
     * 获取 JSON Parser
     * 
     * @param config 配置键
     * @return JsonFormat.Parser 实例
     */
    private static JsonFormat.Parser getJsonParser(String config) {
        return JSON_PARSER_CACHE.computeIfAbsent(config, key -> 
                JsonFormat.parser()
                        .ignoringUnknownFields()
        );
    }

    /**
     * 清空缓存
     */
    public static void clearCache() {
        PARSER_CACHE.clear();
        PRINTER_CACHE.clear();
        JSON_PARSER_CACHE.clear();
        log.info("Protobuf 工具类缓存已清空");
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public static String getCacheStats() {
        return String.format("Protobuf 缓存统计 - Parser: %d, Printer: %d, JsonParser: %d",
                PARSER_CACHE.size(), PRINTER_CACHE.size(), JSON_PARSER_CACHE.size());
    }
}
