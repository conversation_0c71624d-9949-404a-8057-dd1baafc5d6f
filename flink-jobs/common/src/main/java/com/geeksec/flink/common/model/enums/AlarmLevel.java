package com.geeksec.flink.common.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 告警级别枚举
 * 定义系统中所有告警的严重程度级别
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum AlarmLevel {
    
    /** 低级告警 */
    LOW(1, "低级", "Low", "#52C41A"),
    
    /** 中级告警 */
    MEDIUM(2, "中级", "Medium", "#FAAD14"),
    
    /** 高级告警 */
    HIGH(3, "高级", "High", "#FA8C16"),
    
    /** 严重告警 */
    CRITICAL(4, "严重", "Critical", "#F5222D");

    /** 级别代码 */
    private final int code;
    
    /** 中文显示名称 */
    private final String displayName;
    
    /** 英文显示名称 */
    private final String englishName;
    
    /** 颜色代码 */
    private final String colorCode;

    /** 代码映射 */
    private static final Map<Integer, AlarmLevel> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(AlarmLevel::getCode, Function.identity()));

    /** 显示名称映射 */
    private static final Map<String, AlarmLevel> DISPLAY_NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(AlarmLevel::getDisplayName, Function.identity()));

    /** 英文名称映射 */
    private static final Map<String, AlarmLevel> ENGLISH_NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(level -> level.getEnglishName().toLowerCase(), Function.identity()));

    /**
     * 构造函数
     * 
     * @param code 级别代码
     * @param displayName 中文显示名称
     * @param englishName 英文显示名称
     * @param colorCode 颜色代码
     */
    AlarmLevel(int code, String displayName, String englishName, String colorCode) {
        this.code = code;
        this.displayName = displayName;
        this.englishName = englishName;
        this.colorCode = colorCode;
    }

    /**
     * 根据代码获取告警级别
     * 
     * @param code 级别代码
     * @return 告警级别，如果不存在则返回 null
     */
    public static AlarmLevel fromCode(int code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据显示名称获取告警级别
     * 
     * @param displayName 显示名称
     * @return 告警级别，如果不存在则返回 null
     */
    public static AlarmLevel fromDisplayName(String displayName) {
        return DISPLAY_NAME_MAP.get(displayName);
    }

    /**
     * 根据英文名称获取告警级别（不区分大小写）
     * 
     * @param englishName 英文名称
     * @return 告警级别，如果不存在则返回 null
     */
    public static AlarmLevel fromEnglishName(String englishName) {
        if (englishName == null) {
            return null;
        }
        return ENGLISH_NAME_MAP.get(englishName.toLowerCase());
    }

    /**
     * 根据字符串获取告警级别（支持多种格式）
     * 
     * @param value 字符串值
     * @return 告警级别，如果不存在则返回 LOW
     */
    public static AlarmLevel fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return LOW;
        }

        // 尝试按英文名称匹配
        AlarmLevel level = fromEnglishName(value);
        if (level != null) {
            return level;
        }

        // 尝试按显示名称匹配
        level = fromDisplayName(value);
        if (level != null) {
            return level;
        }

        // 尝试按代码匹配
        try {
            int code = Integer.parseInt(value);
            level = fromCode(code);
            if (level != null) {
                return level;
            }
        } catch (NumberFormatException ignored) {
            // 忽略数字解析异常
        }

        // 默认返回低级
        return LOW;
    }

    /**
     * 判断是否为高级别告警（高级或严重）
     * 
     * @return 是否为高级别告警
     */
    public boolean isHighLevel() {
        return this == HIGH || this == CRITICAL;
    }

    /**
     * 判断是否为严重告警
     * 
     * @return 是否为严重告警
     */
    public boolean isCritical() {
        return this == CRITICAL;
    }

    /**
     * 比较告警级别的严重程度
     * 
     * @param other 另一个告警级别
     * @return 如果当前级别更严重返回正数，相等返回0，更轻微返回负数
     */
    public int compareTo(AlarmLevel other) {
        return Integer.compare(this.code, other.code);
    }

    /**
     * 判断当前级别是否比另一个级别更严重
     * 
     * @param other 另一个告警级别
     * @return 是否更严重
     */
    public boolean isMoreSevereThan(AlarmLevel other) {
        return this.code > other.code;
    }

    /**
     * 判断当前级别是否比另一个级别更轻微
     * 
     * @param other 另一个告警级别
     * @return 是否更轻微
     */
    public boolean isLessSevereThan(AlarmLevel other) {
        return this.code < other.code;
    }

    /**
     * 获取下一个更严重的级别
     * 
     * @return 下一个更严重的级别，如果已经是最严重则返回当前级别
     */
    public AlarmLevel getNextSevereLevel() {
        switch (this) {
            case LOW:
                return MEDIUM;
            case MEDIUM:
                return HIGH;
            case HIGH:
                return CRITICAL;
            case CRITICAL:
            default:
                return this;
        }
    }

    /**
     * 获取下一个更轻微的级别
     * 
     * @return 下一个更轻微的级别，如果已经是最轻微则返回当前级别
     */
    public AlarmLevel getNextMildLevel() {
        switch (this) {
            case CRITICAL:
                return HIGH;
            case HIGH:
                return MEDIUM;
            case MEDIUM:
                return LOW;
            case LOW:
            default:
                return this;
        }
    }

    @Override
    public String toString() {
        return displayName;
    }
}
