package com.geeksec.flink.common.exception;

/**
 * Kafka 异常
 * 用于 Kafka 相关操作的异常情况
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class KafkaException extends FlinkJobException {

    private static final long serialVersionUID = 1L;

    /** Kafka 集群地址 */
    private final String bootstrapServers;
    
    /** 主题名称 */
    private final String topic;
    
    /** 分区号 */
    private final Integer partition;
    
    /** 偏移量 */
    private final Long offset;

    /**
     * 默认构造函数
     */
    public KafkaException() {
        super(ErrorCode.KAFKA_CONNECTION_FAILED);
        this.bootstrapServers = null;
        this.topic = null;
        this.partition = null;
        this.offset = null;
    }

    /**
     * 基于错误码的构造函数
     * 
     * @param errorCode 错误码
     */
    public KafkaException(ErrorCode errorCode) {
        super(errorCode);
        this.bootstrapServers = null;
        this.topic = null;
        this.partition = null;
        this.offset = null;
    }

    /**
     * 基于错误码和参数的构造函数
     * 
     * @param errorCode 错误码
     * @param args 格式化参数
     */
    public KafkaException(ErrorCode errorCode, Object... args) {
        super(errorCode, args);
        this.bootstrapServers = null;
        this.topic = null;
        this.partition = null;
        this.offset = null;
    }

    /**
     * 基于错误码和原因的构造函数
     * 
     * @param errorCode 错误码
     * @param cause 原因异常
     */
    public KafkaException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
        this.bootstrapServers = null;
        this.topic = null;
        this.partition = null;
        this.offset = null;
    }

    /**
     * 完整构造函数
     * 
     * @param errorCode 错误码
     * @param cause 原因异常
     * @param bootstrapServers Kafka 集群地址
     * @param topic 主题名称
     * @param partition 分区号
     * @param offset 偏移量
     * @param args 格式化参数
     */
    public KafkaException(ErrorCode errorCode, Throwable cause, String bootstrapServers, 
                         String topic, Integer partition, Long offset, Object... args) {
        super(errorCode, cause, args);
        this.bootstrapServers = bootstrapServers;
        this.topic = topic;
        this.partition = partition;
        this.offset = offset;
    }

    // ==================== Getter 方法 ====================

    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public String getTopic() {
        return topic;
    }

    public Integer getPartition() {
        return partition;
    }

    public Long getOffset() {
        return offset;
    }

    // ==================== 便捷工厂方法 ====================

    /**
     * 创建 Kafka 连接失败异常
     * 
     * @param bootstrapServers Kafka 集群地址
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException connectionFailed(String bootstrapServers, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_CONNECTION_FAILED, cause, 
                bootstrapServers, null, null, null, 
                "连接 Kafka 集群失败：%s", bootstrapServers);
    }

    /**
     * 创建 Kafka 生产者异常
     * 
     * @param topic 主题名称
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException producerError(String topic, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_PRODUCER_ERROR, cause,
                null, topic, null, null,
                "Kafka 生产者错误：主题=%s", topic);
    }

    /**
     * 创建 Kafka 消费者异常
     * 
     * @param topic 主题名称
     * @param partition 分区号
     * @param offset 偏移量
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException consumerError(String topic, Integer partition, Long offset, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_CONSUMER_ERROR, cause,
                null, topic, partition, offset,
                "Kafka 消费者错误：主题=%s，分区=%d，偏移量=%d", topic, partition, offset);
    }

    /**
     * 创建主题不存在异常
     * 
     * @param topic 主题名称
     * @return Kafka 异常
     */
    public static KafkaException topicNotFound(String topic) {
        return new KafkaException(ErrorCode.KAFKA_TOPIC_NOT_FOUND, null,
                null, topic, null, null,
                "Kafka 主题不存在：%s", topic);
    }

    /**
     * 创建序列化异常
     * 
     * @param topic 主题名称
     * @param dataType 数据类型
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException serializationError(String topic, String dataType, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_SERIALIZATION_ERROR, cause,
                null, topic, null, null,
                "Kafka 序列化失败：主题=%s，数据类型=%s", topic, dataType);
    }

    /**
     * 创建反序列化异常
     * 
     * @param topic 主题名称
     * @param partition 分区号
     * @param offset 偏移量
     * @param dataType 数据类型
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException deserializationError(String topic, Integer partition, Long offset, 
                                                     String dataType, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_DESERIALIZATION_ERROR, cause,
                null, topic, partition, offset,
                "Kafka 反序列化失败：主题=%s，分区=%d，偏移量=%d，数据类型=%s", 
                topic, partition, offset, dataType);
    }

    /**
     * 创建偏移量提交失败异常
     * 
     * @param topic 主题名称
     * @param partition 分区号
     * @param offset 偏移量
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException offsetCommitFailed(String topic, Integer partition, Long offset, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_OFFSET_COMMIT_FAILED, cause,
                null, topic, partition, offset,
                "Kafka 偏移量提交失败：主题=%s，分区=%d，偏移量=%d", topic, partition, offset);
    }

    /**
     * 创建分区分配失败异常
     * 
     * @param topic 主题名称
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException partitionAssignmentFailed(String topic, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_PARTITION_ASSIGNMENT_FAILED, cause,
                null, topic, null, null,
                "Kafka 分区分配失败：主题=%s", topic);
    }

    /**
     * 创建消息发送失败异常
     * 
     * @param topic 主题名称
     * @param partition 分区号
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException messageSendFailed(String topic, Integer partition, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_MESSAGE_SEND_FAILED, cause,
                null, topic, partition, null,
                "Kafka 消息发送失败：主题=%s，分区=%d", topic, partition);
    }

    /**
     * 创建消息消费失败异常
     * 
     * @param topic 主题名称
     * @param partition 分区号
     * @param offset 偏移量
     * @param cause 原因异常
     * @return Kafka 异常
     */
    public static KafkaException messageConsumeFailed(String topic, Integer partition, Long offset, Throwable cause) {
        return new KafkaException(ErrorCode.KAFKA_MESSAGE_CONSUME_FAILED, cause,
                null, topic, partition, offset,
                "Kafka 消息消费失败：主题=%s，分区=%d，偏移量=%d", topic, partition, offset);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(super.toString());
        
        if (bootstrapServers != null) {
            sb.append(" | 集群: ").append(bootstrapServers);
        }
        if (topic != null) {
            sb.append(" | 主题: ").append(topic);
        }
        if (partition != null) {
            sb.append(" | 分区: ").append(partition);
        }
        if (offset != null) {
            sb.append(" | 偏移量: ").append(offset);
        }
        
        return sb.toString();
    }
}
