package com.geeksec.certificate.analyzer.integration;

import com.geeksec.certificate.analyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificate.analyzer.pipeline.CertificateProcessingPipeline;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.flink.common.constants.ConfigConstants;
import com.geeksec.certificate.analyzer.model.cert.CertificateLabel;
import java.util.Set;
import java.util.HashSet;
import java.util.Map;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 证书处理集成测试
 * 测试完整的证书处理流程
 * 
 * <AUTHOR>
 */
@Slf4j
class CertificateProcessingIntegrationTest {

    private StreamExecutionEnvironment testEnv;
    private ParameterTool testConfig;

    @BeforeEach
    void setUp() {
        log.info("初始化集成测试环境");
        
        // 创建完整的测试配置
        Map<String, String> configMap = new HashMap<>();
        
        // Kafka配置
        configMap.put(ConfigConstants.KAFKA_TOPIC_CERTIFICATE_FILES, "test-certfile");
        configMap.put(ConfigConstants.KAFKA_TOPIC_SYSTEM_CERTIFICATES, "test-certfile-system");

        // 分析器配置
        configMap.put(ConfigConstants.CERTIFICATE_ANALYZER_PARALLELISM, "2");
        configMap.put(ConfigConstants.CERTIFICATE_ANALYZER_BUFFER_SIZE, "500");
        configMap.put(ConfigConstants.CERTIFICATE_ANALYZER_TIMEOUT_MS, "10000");
        configMap.put(ConfigConstants.CERTIFICATE_ANALYZER_DEBUG_ENABLED, "true");

        // 输出配置
        configMap.put(ConfigConstants.CERTIFICATE_OUTPUT_POSTGRESQL_ENABLED, "false");
        configMap.put(ConfigConstants.CERTIFICATE_OUTPUT_NEBULA_ENABLED, "false");
        
        testConfig = ParameterTool.fromMap(configMap);
        
        // 创建测试执行环境
        testEnv = StreamExecutionEnvironment.getExecutionEnvironment();
        testEnv.setParallelism(1);
        testEnv.getConfig().setGlobalJobParameters(testConfig);
    }

    @Test
    @DisplayName("测试完整证书处理流程")
    void testCompleteCertificateProcessingFlow() {
        log.info("测试完整证书处理流程");
        
        try {
            // 创建测试证书数据流
            DataStream<X509Certificate> certificateStream = createTestCertificateStream();
            
            // 构建处理流水线
            CertificateProcessingPipeline.PipelineResult pipelineResult =
                CertificateProcessingPipeline.build(certificateStream, testConfig);
            
            // 验证流水线结果
            assertNotNull(pipelineResult);
            assertNotNull(pipelineResult.getProcessedStream());
            
            log.info("完整证书处理流程测试通过");
            
        } catch (Exception e) {
            log.error("证书处理流程测试失败: {}", e.getMessage(), e);
            fail("证书处理流程测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试多证书批量处理")
    void testBatchCertificateProcessing() {
        log.info("测试多证书批量处理");
        
        try {
            // 创建多个测试证书
            List<X509Certificate> testCertificates = createMultipleTestCertificates(5);
            DataStream<X509Certificate> certificateStream = testEnv.fromCollection(testCertificates);
            
            // 构建处理流水线
            CertificateProcessingPipeline.PipelineResult pipelineResult =
                CertificateProcessingPipeline.build(certificateStream, testConfig);
            
            // 验证批量处理结果
            assertNotNull(pipelineResult);
            assertNotNull(pipelineResult.getProcessedStream());
            
            log.info("多证书批量处理测试通过");
            
        } catch (Exception e) {
            log.error("批量处理测试失败: {}", e.getMessage(), e);
            fail("批量处理测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试配置驱动的处理流程")
    void testConfigurationDrivenProcessing() {
        log.info("测试配置驱动的处理流程");
        
        try {
            // 验证配置正确加载
            assertEquals("test-certfile", CertificateAnalyzerConfig.getCertificateFilesTopic());
            assertEquals("test-certfile-system", CertificateAnalyzerConfig.getSystemCertificatesTopic());
            assertEquals(2, CertificateAnalyzerConfig.getAnalyzerParallelism());
            assertTrue(CertificateAnalyzerConfig.isDebugEnabled());
            
            // 创建证书流并处理
            DataStream<X509Certificate> certificateStream = createTestCertificateStream();
            CertificateProcessingPipeline.PipelineResult result =
                CertificateProcessingPipeline.build(certificateStream, testConfig);
            
            assertNotNull(result);
            
            log.info("配置驱动的处理流程测试通过");
            
        } catch (Exception e) {
            log.error("配置驱动处理测试失败: {}", e.getMessage(), e);
            fail("配置驱动处理测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试错误处理和恢复")
    void testErrorHandlingAndRecovery() {
        log.info("测试错误处理和恢复");
        
        try {
            // 创建包含异常数据的证书流
            List<X509Certificate> testCertificates = new ArrayList<>();
            
            // 正常证书
            testCertificates.add(createValidTestCertificate("normal-cert-001"));
            
            // 异常证书（某些字段为空）
            X509Certificate invalidCert = createValidTestCertificate("invalid-cert-001");
            invalidCert.setWellFormed(false);
            testCertificates.add(invalidCert);
            
            // 另一个正常证书
            testCertificates.add(createValidTestCertificate("normal-cert-002"));
            
            DataStream<X509Certificate> certificateStream = testEnv.fromCollection(testCertificates);
            
            // 处理应该不抛出异常，即使有无效证书
            assertDoesNotThrow(() -> {
                CertificateProcessingPipeline.PipelineResult result =
                CertificateProcessingPipeline.build(certificateStream, testConfig);
                assertNotNull(result);
            });
            
            log.info("错误处理和恢复测试通过");
            
        } catch (Exception e) {
            log.error("错误处理测试失败: {}", e.getMessage(), e);
            fail("错误处理测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试性能配置影响")
    void testPerformanceConfigurationImpact() {
        log.info("测试性能配置影响");
        
        try {
            // 测试不同并行度配置
            Map<String, String> highPerfConfig = new HashMap<>(testConfig.toMap());
            highPerfConfig.put("certificate.analyzer.parallelism", "4");
            highPerfConfig.put("certificate.analyzer.buffer.size", "1000");
            
            ParameterTool perfConfig = ParameterTool.fromMap(highPerfConfig);
            
            // 创建高性能配置的执行环境
            StreamExecutionEnvironment perfEnv = StreamExecutionEnvironment.getExecutionEnvironment();
            perfEnv.setParallelism(2);
            perfEnv.getConfig().setGlobalJobParameters(perfConfig);
            
            // 创建证书流
            DataStream<X509Certificate> certificateStream = createTestCertificateStream();
            
            // 验证高性能配置不影响处理流程
            assertDoesNotThrow(() -> {
                CertificateProcessingPipeline.PipelineResult result =
                CertificateProcessingPipeline.build(certificateStream, perfConfig);
                assertNotNull(result);
            });
            
            log.info("性能配置影响测试通过");
            
        } catch (Exception e) {
            log.error("性能配置测试失败: {}", e.getMessage(), e);
            fail("性能配置测试失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试证书数据流
     */
    private DataStream<X509Certificate> createTestCertificateStream() {
        X509Certificate testCert = createValidTestCertificate("integration-test-cert-001");
        return testEnv.fromElements(testCert);
    }

    /**
     * 创建多个测试证书
     */
    private List<X509Certificate> createMultipleTestCertificates(int count) {
        List<X509Certificate> certificates = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            certificates.add(createValidTestCertificate("batch-test-cert-" + String.format("%03d", i + 1)));
        }
        return certificates;
    }

    /**
     * 创建有效的测试证书
     */
    private X509Certificate createValidTestCertificate(String certId) {
        X509Certificate cert = new X509Certificate();
        cert.setCertId(certId);
        cert.setSha1("sha1-" + certId.hashCode());
        
        // 创建Subject和Issuer的Map结构
        Map<String, String> subjectMap = new HashMap<>();
        subjectMap.put("CN", certId + ".example.com");
        subjectMap.put("O", "Test Organization");
        subjectMap.put("C", "US");
        cert.setSubject(subjectMap);
        
        Map<String, String> issuerMap = new HashMap<>();
        issuerMap.put("CN", "Test CA");
        issuerMap.put("O", "Test CA Organization");
        issuerMap.put("C", "US");
        cert.setIssuer(issuerMap);
        
        cert.setWellFormed(true);
        cert.setCreateTime(LocalDateTime.now());
        
        // 创建标签集合
        Set<CertificateLabel> labels = new HashSet<>();
        CertificateLabel testLabel = new CertificateLabel();
        testLabel.setName("Integration Test Certificate");
        labels.add(testLabel);
        cert.setLabels(labels);
        
        return cert;
    }
}
