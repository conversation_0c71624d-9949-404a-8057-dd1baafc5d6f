package com.geeksec.nta.certificate.util;

import com.geeksec.certificate.analyzer.util.cert.CertificateNameOidSorter;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 证书字段排序工具类测试
 * 
 * <AUTHOR>
 */
public class NameOidSortTest {

    /**
     * 测试按键排序Map
     */
    @Test
    public void testSortMapByKey() {
        // 准备测试数据
        Map<String, String> map = new HashMap<>();
        map.put("ST", "California");
        map.put("CN", "Value8");  // 注意：这里使用Value8，因为如果有重复键，后面的会覆盖前面的
        map.put("EMAIL_ADDRESS", "<EMAIL>");
        map.put("POSTAL_CODE", "94105");
        map.put("OU", "Tech Solutions");
        map.put("POSTAL_ADDRESS", "Future St");
        map.put("L", "San Francisco");
        map.put("O", "comodo");
        map.put("STREET_ADDRESS", "Innovation Drive");
        
        // 执行排序
        String sortedString = CertificateNameOidSorter.sortMapByKey(map);
        
        // 验证结果
        assertTrue(sortedString.startsWith("CN=Value8"));
        
        // 验证顺序是否符合预期
        String[] parts = sortedString.split(", ");
        assertEquals("CN=Value8", parts[0]);  // CN应该是第一个
        
        // 验证所有字段都在结果中
        assertTrue(sortedString.contains("ST=California"));
        assertTrue(sortedString.contains("L=San Francisco"));
        assertTrue(sortedString.contains("O=comodo"));
        assertTrue(sortedString.contains("OU=Tech Solutions"));
        assertTrue(sortedString.contains("STREET_ADDRESS=Innovation Drive"));
        assertTrue(sortedString.contains("POSTAL_ADDRESS=Future St"));
        assertTrue(sortedString.contains("POSTAL_CODE=94105"));
        assertTrue(sortedString.contains("EMAIL_ADDRESS=<EMAIL>"));
    }
    
    /**
     * 测试计算排序后字符串的MD5哈希值
     */
    @Test
    public void testGetMD5ForSortedMap() {
        // 准备测试数据
        Map<String, String> map = new HashMap<>();
        map.put("CN", "example.com");
        map.put("O", "Example Org");
        
        // 手动计算预期的MD5
        String sortedString = "CN=example.com, O=Example Org";
        String expectedMd5 = DigestUtils.md5Hex(sortedString);
        
        // 使用工具类计算MD5
        String actualMd5 = CertificateNameOidSorter.getMD5ForSortedMap(map);
        
        // 验证结果
        assertEquals(expectedMd5, actualMd5);
    }
    
    /**
     * 测试OID排序
     */
    @Test
    public void testOidSorting() {
        // 准备测试数据 - 包含OID
        Map<String, String> map = new HashMap<>();
        map.put("*******", "example.com");  // CN的OID
        map.put("********", "Example Org"); // O的OID
        
        // 执行排序
        String sortedString = CertificateNameOidSorter.sortMapByKey(map);
        
        // 验证结果 - 数字较小的OID应该排在前面
        assertTrue(sortedString.startsWith("*******="));
    }
}
