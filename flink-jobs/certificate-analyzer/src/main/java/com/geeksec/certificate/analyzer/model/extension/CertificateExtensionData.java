package com.geeksec.certificate.analyzer.model.extension;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 证书扩展数据模型类
 * 用于保存解析后的X.509证书扩展信息
 *
 * <AUTHOR>
 * @since 2025/01/17
 */
@Data
public class CertificateExtensionData {

    /**
     * 密钥用途
     */
    @JSONField(name = "key_usage")
    private String keyUsage;

    /**
     * 扩展密钥用途
     */
    @JSONField(name = "extended_key_usage")
    private List<String> extendedKeyUsage = new ArrayList<>();

    /**
     * 基本约束
     */
    @JSONField(name = "basic_constraints")
    private String basicConstraints;

    /**
     * 授权密钥标识符
     */
    @JSONField(name = "authority_key_identifier")
    private String authorityKeyIdentifier;

    /**
     * 主题密钥标识符
     */
    @JSONField(name = "subject_key_identifier")
    private String subjectKeyIdentifier;

    /**
     * CRL分发点
     */
    @JSONField(name = "crl_distribution_points")
    private List<String> crlDistributionPoints = new ArrayList<>();

    /**
     * 授权信息访问
     */
    @JSONField(name = "authority_info_access")
    private List<String> authorityInfoAccess = new ArrayList<>();

    /**
     * 主题信息访问
     */
    @JSONField(name = "subject_info_access")
    private List<String> subjectInfoAccess = new ArrayList<>();

    /**
     * 证书策略
     */
    @JSONField(name = "cert_policies")
    private List<String> certPolicies = new ArrayList<>();

    // Getter methods for private fields
    public List<String> getAuthorityInfoAccess() {
        return authorityInfoAccess;
    }

    public List<String> getSubjectInfoAccess() {
        return subjectInfoAccess;
    }

    public List<String> getCertPolicies() {
        return certPolicies;
    }

    /**
     * 是否为CA证书
     */
    @JSONField(name = "is_ca")
    private Boolean ca = false;

    /**
     * 路径长度约束
     */
    @JSONField(name = "path_len_constraint")
    private Integer pathLenConstraint = null;

    /**
     * 主题备用名称
     */
    @JSONField(name = "subject_alt_name")
    private List<String> subjectAltName = new ArrayList<>();

    /**
     * 最新CRL
     */
    @JSONField(name = "freshest_crl")
    private List<String> freshestCrl = new ArrayList<>();

    /**
     * OCSP URI列表
     */
    @JSONField(name = "ocsp_uris")
    private List<String> ocspUris = new ArrayList<>();

    /**
     * CA颁发者URI列表
     */
    @JSONField(name = "ca_issuers_uris")
    private List<String> caIssuersUris = new ArrayList<>();

    /**
     * 证书策略OID列表
     */
    @JSONField(name = "certificate_policies_oids")
    private List<String> certificatePoliciesOids = new ArrayList<>();

    /**
     * OCSP无检查标记
     */
    @JSONField(name = "ocsp_no_check")
    private Boolean ocspNoCheck = false;

    /**
     * 预证书毒化标记
     */
    @JSONField(name = "precert_poison")
    private Boolean precertPoison = false;

    /**
     * TLS特性列表
     */
    @JSONField(name = "tls_features")
    private List<String> tlsFeatures = new ArrayList<>();

    /**
     * 禁止任何策略跳过证书数
     */
    @JSONField(name = "inhibit_any_policy_skip_certs")
    private Integer inhibitAnyPolicySkipCerts = null;

    /**
     * 其他关键OID列表
     */
    @JSONField(name = "other_critical_oids")
    private List<String> otherCriticalOids = new ArrayList<>();

    /**
     * 其他非关键OID列表
     */
    @JSONField(name = "other_non_critical_oids")
    private List<String> otherNonCriticalOids = new ArrayList<>();

    /**
     * 完整的扩展映射JSON字符串
     */
    @JSONField(name = "extension_map")
    private String extensionMap = null;

    /**
     * 其他扩展信息
     */
    @JSONField(name = "miscellaneous_extensions")
    private Map<String, Object> miscellaneousExtensions = new HashMap<>();
}
