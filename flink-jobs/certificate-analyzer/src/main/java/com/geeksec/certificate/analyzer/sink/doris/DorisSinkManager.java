package com.geeksec.certificate.analyzer.sink.doris;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;

import com.geeksec.certificate.analyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.flink.common.sink.FlinkDorisSinkManager;

import lombok.extern.slf4j.Slf4j;

/**
 * Doris证书输出管理器
 * 负责将证书分析结果写入Doris dim_cert表
 *
 * <AUTHOR>
 * @since 2024/12/16
 */
@Slf4j
public class DorisSinkManager {

    /**
     * 添加证书Doris输出
     *
     * @param certificateStream 证书数据流
     * @param config 配置参数
     */
    public static void addDorisCertificateOutput(DataStream<X509Certificate> certificateStream, ParameterTool config) {
        log.info("开始添加证书Doris输出");

        // 将证书对象转换为Row对象
        DataStream<Row> rowStream = certificateStream
                .map(CertificateToDorisRowConverter::convertToRow)
                .name("证书数据转换为Row");

        // 配置Doris Sink
        String tableName = config.get("doris.certificate.table", "dim_cert");
        int parallelism = config.getInt("doris.certificate.parallelism", 
                CertificateAnalyzerConfig.getAnalyzerParallelism());

        rowStream.sinkTo(FlinkDorisSinkManager.buildDorisSink(tableName, config))
                .name("证书数据Doris输出")
                .setParallelism(parallelism);

        log.info("已添加证书Doris输出，表名: {}, 并行度: {}", tableName, parallelism);
    }

    /**
     * 添加威胁证书Doris输出
     * 专门处理高威胁评分的证书，可能需要特殊处理
     *
     * @param certificateStream 证书数据流
     * @param config 配置参数
     */
    public static void addThreatCertificateDorisSink(DataStream<X509Certificate> certificateStream, ParameterTool config) {
        log.info("开始添加威胁证书Doris输出");

        // 过滤出威胁证书
        DataStream<X509Certificate> threatCertStream = certificateStream
                .filter(cert -> cert.getThreatScore() > 60 || 
                               cert.getLabels().stream().anyMatch(label -> 
                                   label.toString().contains("Threat") || 
                                   label.toString().contains("Malicious")))
                .name("威胁证书过滤");

        // 转换为Row并写入Doris
        DataStream<Row> threatRowStream = threatCertStream
                .map(CertificateToDorisRowConverter::convertToRow)
                .name("威胁证书数据转换为Row");

        String tableName = config.get("doris.threat.certificate.table", "dim_cert");
        int parallelism = config.getInt("doris.threat.certificate.parallelism", 2);

        threatRowStream.sinkTo(FlinkDorisSinkManager.buildDorisSink(tableName, config))
                .name("威胁证书数据Doris输出")
                .setParallelism(parallelism);

        log.info("已添加威胁证书Doris输出，表名: {}, 并行度: {}", tableName, parallelism);
    }
}
