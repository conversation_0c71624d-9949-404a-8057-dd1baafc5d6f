package com.geeksec.certificate.analyzer.sink.redis;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.flink.common.database.redis.RedisConnectionManager;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

/**
 * 证书Redis缓存输出
 * 负责将威胁证书的关键信息写入Redis缓存，供其他系统快速查询
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2024/9/12
 */
@Slf4j
public class RedisCertificateSink extends RichSinkFunction<X509Certificate> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("RedisCertificateSink初始化完成");
    }

    @Override
    public void close() throws Exception {
        super.close();
        log.info("RedisCertificateSink关闭完成");
    }

    @Override
    public void invoke(X509Certificate cert, Context context) throws Exception {
        try (Jedis jedis = RedisConnectionManager.getJedis()) {
            jedis.select(8);

            // 获取证书标签和威胁标签
            Set<CertificateLabel> labels = cert.getLabels();
            List<String> threatTags = getThreatLabels(labels);
            int threatScore = cert.getThreatScore();
            List<String> associatedDomains = cert.getCertificateDomains();

            // 只有威胁评分大于80且包含威胁标签的证书才写入Redis
            if (!threatTags.isEmpty() && threatScore > 80) {
                JSONObject simpleJson = new JSONObject();
                String sha1 = cert.getCorrectedAsn1Sha1();
                if (sha1 == null || sha1.isEmpty()) {
                    sha1 = cert.getDerSha1();
                }

                simpleJson.put("ASN1SHA1", sha1);
                simpleJson.put("ThreatTags", threatTags);
                simpleJson.put("ThreatScore", threatScore);
                simpleJson.put("AssociateDomain", associatedDomains);

                String simpleCertInfo = simpleJson.toJSONString();
                String key = "pushDataCert_" + sha1;

                // 设置24小时过期时间
                jedis.setex(key, 86400, simpleCertInfo);
                log.debug("威胁证书信息已写入Redis: {}", sha1);
            }
        } catch (Exception e) {
            log.error("证书简单信息写入redis失败，error: {}", e.toString());
        }
    }

    /**
     * 从证书标签中提取威胁标签
     *
     * @param labels 证书标签集合
     * @return 威胁标签列表
     */
    private List<String> getThreatLabels(Set<CertificateLabel> labels) {
        List<String> threatLabels = new ArrayList<>();
        for (CertificateLabel label : labels) {
            String labelStr = String.valueOf(label.getId());
            // 这里可以根据实际的威胁标签映射规则进行判断
            // 暂时简单判断包含威胁相关的标签ID
            if (isThreatLabel(labelStr)) {
                threatLabels.add(labelStr);
            }
        }
        return threatLabels;
    }

    /**
     * 判断是否为威胁标签
     *
     * @param labelStr 标签字符串
     * @return 是否为威胁标签
     */
    private boolean isThreatLabel(String labelStr) {
        // 这里应该根据实际的威胁标签映射表进行判断
        // 暂时使用简单的规则
        return labelStr.contains("Threat") || labelStr.contains("Malicious") ||
               labelStr.contains("APT") || labelStr.contains("Suspicious");
    }
}
