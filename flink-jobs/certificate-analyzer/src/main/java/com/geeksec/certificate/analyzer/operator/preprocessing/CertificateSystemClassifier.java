package com.geeksec.certificate.analyzer.operator.preprocessing;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.operator.analysis.scoring.CertificateRiskScorer;

import com.geeksec.certificate.analyzer.sink.minio.CertificateStorageService;
import com.geeksec.certificate.analyzer.util.KnowledgeBaseUtils;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书系统分类器
 *
 * 负责对证书进行初步分类和标记，区分系统证书和普通证书
 * 使用知识库服务获取证书标签评分数据
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateSystemClassifier extends ProcessFunction<X509Certificate, X509Certificate> {

    /** 系统证书输出标签 */
    public static final OutputTag<X509Certificate> SYS_CERT = new OutputTag<>("sys_cert",
            TypeInformation.of(X509Certificate.class));

    /** 需要标签处理的用户证书输出标签 */
    public static final OutputTag<X509Certificate> NEED_LABEL_PROCESSING = new OutputTag<>("need_label_processing",
            TypeInformation.of(X509Certificate.class));

    /** 知识库客户端 */
    private transient KnowledgeBaseClient knowledgeBaseClient;

    private static final List<String> ANDROID_LIST = Arrays.asList("6ba0b098e171ef5aadfe4815807710f4bd6f0b28",
            "b49082dd450cbe8b5bb166d3e2a40826cded42cf", "53a2b04bca6bd645e6398a8ec40dd2bf77c3a290",
            "58e8abb0361533fb80f79b1b6d29d3ff8d5f00f0", "55a6723ecbf2eccdc3237470199d2abe11e381d1",
            "d69b561148f01c77c54578c10926df5b856976ad", "d067c11351010caad0c76a65373116264f5371a2",
            "093c61f38b8bdc7d55df7538020500e125f5c836", "1f24c630cda418ef2069ffad4fdd5f463a1b69aa",
            "ec2c834072af269510ff0ef203ee3170f6789dca");

    private static final List<String> FIREFOX_LIST = Arrays.asList("4464937bad1ac056cae4c172646e1b84077a1952",
            "1cf68d2fe7dcae50940428fd340cd8f29a7879ff", "3360474313ed55cb3521ec092618bf4550892226",
            "981962b7ef60bb3281bb507cbaeef00cea5a5e55", "4bd1505c8e2d7a58b907b1a3c69d404e0a4ce295",
            "f379fdd0198851f2f93237353e2188077a4c718e");

    private static final List<String> WITHDRAW_LIST = Arrays.asList("038c7319e6878d5228ee81d47714b6540eae9929",
            "ae93b49e5897dc337a936634c665ddca33418613", "40ee4e8c0a6d30f429a6f3676bb81de78558ed6a",
            "c832f5800b96e93fe996570230fafb1ff5e3ac93", "31984f032b97e5303907725627ee61f880d94b65",
            "a45dd09d84aa68175e43185c81764e15b1a37971", "6a67b0820c809d51e087117fc24f5b4325cdace9",
            "9da3c52f29a9faffb67277ce9197bea31cb617e2", "43226b2688bf40d765ade1ac94c9466163723c8b",
            "76dd1a1045e2e900d9c425973135291fda7842cf");

    // 微软网关加密， Microsoft Server Gated Crypto
    private static final List<String> MICROSOFT_GATED_LIST = Arrays.asList("37b67b371b59654c597d0ea0910c2f04b56e0ca2",
            "fb9086b87d54dd91ef3b0d8a449d36367184120b", "e5b7e109d7ee5a31cdd027646fb718ff1279f3b4",
            "5e3643ea34d505d6776a4beb60fd9f7e62584385");

    // Netscape 网关加密: Key Agreement
    private static final List<String> NETSCAPE_KEY_AGREEMENT_LIST = Arrays.asList(
            "f69fa405bedee6d9594428dc803cf6bbff9e64ef", "820cce80ebe1d059a057c51a0ad1b2e3e97544d7",
            "3aa96151e2f500f9a5bf1045fa5d278169e41a07",
            "542f5c654bf176e531a1e8e5710fd91ae04d8e71", "b22921678772c513f05723c7ed4293a04bcb70ee",
            "6eae470586bc3d471ff92662d09e18474b527d45", "b6f7a80a5dce0d01c53fcdb35bd4de97835a770c",
            "26a204a8bec9722e2d344e5743456119d8767560");

    @Override
    public void open(Configuration parameters) throws Exception {
        // 创建知识库客户端实例
        knowledgeBaseClient = KnowledgeBaseUtils.createInstance(parameters);

        log.info("Certificate analyzer initialized with lazy loading mode");
    }

    @Override
    public void close() throws Exception {
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
    }

    @Override
    public void processElement(X509Certificate cert, ProcessFunction<X509Certificate, X509Certificate>.Context context,
            Collector<X509Certificate> collector) throws Exception {
        // 提前标签处理，避免系统证书过滤
        addLabelBeforeSplit(cert);

        try {
            List<String> labels = new ArrayList<>();

            // 校验当前证书是否是系统白名单证书，是系统证书就直接往下写入
            // 使用CertificateStorageService检查是否是系统证书
            boolean isSysCert = CertificateStorageService.certificateExists(cert.getDerSha1());

            if (isSysCert) {
                getCaAttr(cert, labels);
                cert.setTrusted(true);
                labels.add("WhiteCert");
                cert.setLabels(CertificateRiskScorer.tagsToTagId(labels));
                // 将字符串标签转换为标签ID列表
                List<Integer> labelIds = convertStringLabelsToIds(labels);
                cert.setThreatScore(getBlackScore(labelIds));
                cert.setTrustScore(getWhiteScore(labelIds));
                cert.setMethod(false);
                cert.setImportTime(System.currentTimeMillis() / 1000);
                context.output(SYS_CERT, cert);
            } else {
                cert.setMethod(false);
                cert.setTrusted(false);
                context.output(NEED_LABEL_PROCESSING, cert);
            }
        } catch (Exception e) {
            log.error("处理证书失败，error--->{}", e.toString());
        }
    }

    private void addLabelBeforeSplit(X509Certificate cert) {
        List<String> labels = cert.getLabels();
        String SHA1 = cert.getASN1SHA1();
        if (ANDROID_LIST.contains(SHA1)) {
            labels.add("Android Trust");
        }

        if (FIREFOX_LIST.contains(SHA1)) {
            labels.add("Firefox Trust");
        }

        if (WITHDRAW_LIST.contains(SHA1)) {
            labels.add("Withdraw Cert");
        }

        if (MICROSOFT_GATED_LIST.contains(SHA1)) {
            labels.add("Microsoft Server Gated Crypto");
        }

        if (NETSCAPE_KEY_AGREEMENT_LIST.contains(SHA1)) {
            labels.add("Key Agreement");
        }
        cert.setLabels(labels);
    }

    /**
     * 计算黑名单评分（按需查询）
     */
    private int getBlackScore(List<Integer> labelIds) {
        int score = 0;

        for (Integer labelId : labelIds) {
            try {
                // 按需从知识库查询黑名单评分
                Integer labelScore = knowledgeBaseClient.getCertificateBlackScoreByLabelId(labelId);
                score += labelScore;
            } catch (Exception e) {
                log.warn("查询黑名单评分失败，标签ID: {}", labelId, e);
            }
        }

        // 超过100取100
        return Math.min(score, 100);
    }

    /**
     * 计算白名单评分（按需查询）
     */
    private int getWhiteScore(List<Integer> labelIds) {
        int score = 0;

        for (Integer labelId : labelIds) {
            try {
                // 按需从知识库查询白名单评分
                Integer labelScore = knowledgeBaseClient.getCertificateWhiteScoreByLabelId(labelId);
                score += labelScore;
            } catch (Exception e) {
                log.warn("查询白名单评分失败，标签ID: {}", labelId, e);
            }
        }

        // 超过100取100
        return Math.min(score, 100);
    }

    /**
     * 将字符串标签转换为标签ID列表
     */
    private List<Integer> convertStringLabelsToIds(List<String> stringLabels) {
        List<Integer> labelIds = new ArrayList<>();

        for (String labelName : stringLabels) {
            try {
                // 根据标签名称查找对应的CertificateLabel枚举
                CertificateLabel label = findLabelByName(labelName);
                if (label != null) {
                    labelIds.add(label.getId());
                } else {
                    log.warn("未找到标签名称对应的枚举: {}", labelName);
                }
            } catch (Exception e) {
                log.warn("转换标签名称到ID失败: {}", labelName, e);
            }
        }

        return labelIds;
    }

    /**
     * 根据标签名称查找CertificateLabel枚举
     */
    private CertificateLabel findLabelByName(String labelName) {
        for (CertificateLabel label : CertificateLabel.values()) {
            if (label.getDisplayName().equals(labelName)) {
                return label;
            }
        }
        return null;
    }

    // 标记CA，根CA，公开CA
    private static void getCaAttr(X509Certificate cert, List<String> labels) {
        Object extRaw = cert.getExtension();
        Map<String, Object> extension = (extRaw instanceof Map) ? (Map<String, Object>) extRaw : new HashMap<>();
        String basicConstraints = String.valueOf(extension.getOrDefault(CertificateConstants.EXTENSION_BASIC_CONSTRAINTS, ""));

        // 提取证书的 Subject Key ID
        String subjectKeyId = String.valueOf(extension.getOrDefault(CertificateConstants.EXTENSION_SUBJECT_KEY_IDENTIFIER, ""));

        // 提取证书的 Authority Key ID
        String issuerKeyId = String.valueOf(extension.getOrDefault(CertificateConstants.EXTENSION_AUTHORITY_KEY_IDENTIFIER, ""));
        if ("null".equals(issuerKeyId)) {
            issuerKeyId = "";
        }
        if (basicConstraints.contains(CertificateConstants.CA_TRUE)) {
            labels.add("Public CA");
            if (cert.getSubjectId().equals(cert.getIssuerId()) || (!"".equals(subjectKeyId) && subjectKeyId.equals(issuerKeyId))) {
                labels.add("Root CA");
            } else {
                labels.add("CA");
            }
        }
    }
}
