package com.geeksec.certificate.analyzer.repository.redis;

import java.util.List;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.flink.common.database.redis.RedisConnectionManager;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Protocol;
import redis.clients.jedis.util.SafeEncoder;

/**
 * 证书Redis布隆过滤器操作类
 *
 * TODO: 需要重构以移除对FileUtil和config.properties文件的依赖
 * 建议改为通过配置管理器或环境变量获取Redis配置信息
 *
 * <AUTHOR>
 * @date 2024/10/15
 */
@Slf4j
public class CertificateRedisBloomFilter {

    // 使用RedisConnectionManager统一管理Redis连接

    // 布隆过滤器相关配置
    private static final String CERT_BLOOM_FILTER_KEY = "cert_bloom_filter";
    private static final double ERROR_RATE = 0.001; // 0.1%的误判率
    private static final long EXPECTED_INSERTIONS = 10_000_000; // 预期插入1000万个元素

    // Redis数据库编号
    private static final int BLOOM_FILTER_DB = 15;
    private static final int EXACT_KEYS_DB = 15;

    // 精确键的过期时间（秒）
    private static final int EXACT_KEY_EXPIRY = 600;

    /**
     * 获取Redis连接
     * 使用RedisConnectionManager统一管理
     */
    public static Jedis getJedis() {
        return RedisConnectionManager.getJedis();
    }

    /**
     * 初始化布隆过滤器
     */
    public static void initBloomFilter() {
        try (Jedis jedis = getJedis()) {
        try {
            jedis.select(BLOOM_FILTER_DB);

            // 检查布隆过滤器是否已存在
            if (!jedis.exists(CERT_BLOOM_FILTER_KEY)) {
                // 创建布隆过滤器
                Object response = jedis.sendCommand(
                        Protocol.Command.valueOf("BF.RESERVE"),
                        SafeEncoder.encode(CERT_BLOOM_FILTER_KEY),
                        SafeEncoder.encode(String.valueOf(ERROR_RATE)),
                        SafeEncoder.encode(String.valueOf(EXPECTED_INSERTIONS)));

                log.info("创建Redis布隆过滤器: {}, 响应: {}", CERT_BLOOM_FILTER_KEY, response);
            } else {
                log.info("Redis布隆过滤器已存在: {}", CERT_BLOOM_FILTER_KEY);
            }
        } catch (Exception e) {
            log.error("初始化Redis布隆过滤器失败", e);
        }
    }

    /**
     * 使用布隆过滤器检查证书是否存在
     * 
     * @param cert 证书对象
     * @return 如果证书可能存在返回true，否则返回false
     */
    public static boolean checkBloomFilter(X509Certificate cert) {
        try (Jedis jedis = getJedis()) {
        String certHash = cert.getDerSha1();
        try {
            jedis.select(BLOOM_FILTER_DB);

            // 检查布隆过滤器中是否可能存在
            Object response = jedis.sendCommand(
                    Protocol.Command.valueOf("BF.EXISTS"),
                    SafeEncoder.encode(CERT_BLOOM_FILTER_KEY),
                    SafeEncoder.encode(certHash));

            // 如果返回1，表示可能存在
            return response != null && Integer.parseInt(response.toString()) == 1;
        } catch (Exception e) {
            log.error("检查布隆过滤器失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 将证书哈希添加到布隆过滤器
     * 
     * @param cert 证书对象
     * @return 添加成功返回true，否则返回false
     */
    public static boolean addToBloomFilter(X509Certificate cert) {
        try (Jedis jedis = getJedis()) {
        String certHash = cert.getDerSha1();
        try {
            jedis.select(BLOOM_FILTER_DB);

            // 添加到布隆过滤器
            Object response = jedis.sendCommand(
                    Protocol.Command.valueOf("BF.ADD"),
                    SafeEncoder.encode(CERT_BLOOM_FILTER_KEY),
                    SafeEncoder.encode(certHash));

            // 如果返回1，表示添加成功
            return response != null && Integer.parseInt(response.toString()) == 1;
        } catch (Exception e) {
            log.error("添加到布隆过滤器失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 批量检查证书哈希是否存在于布隆过滤器中
     * 
     * @param certHashes 证书哈希列表
     * @return 布尔值列表，表示每个哈希是否可能存在
     */
    public static boolean[] batchCheckBloomFilter(List<String> certHashes) {
        try (Jedis jedis = getJedis()) {
        if (certHashes == null || certHashes.isEmpty()) {
            return new boolean[0];
        }

        try {
            jedis.select(BLOOM_FILTER_DB);

            // 准备命令参数
            byte[][] args = new byte[certHashes.size() + 1][];
            args[0] = SafeEncoder.encode(CERT_BLOOM_FILTER_KEY);
            for (int i = 0; i < certHashes.size(); i++) {
                args[i + 1] = SafeEncoder.encode(certHashes.get(i));
            }

            // 执行批量检查
            Object response = jedis.sendCommand(Protocol.Command.valueOf("BF.MEXISTS"), args);

            // 解析响应
            List<Object> results = (List<Object>) response;
            boolean[] exists = new boolean[results.size()];
            for (int i = 0; i < results.size(); i++) {
                exists[i] = Integer.parseInt(results.get(i).toString()) == 1;
            }

            return exists;
        } catch (Exception e) {
            log.error("批量检查布隆过滤器失败: {}", e.getMessage());
            return new boolean[certHashes.size()];
        }
    }

    /**
     * 批量添加证书哈希到布隆过滤器
     * 
     * @param certHashes 证书哈希列表
     * @return 布尔值列表，表示每个哈希是否添加成功
     */
    public static boolean[] batchAddToBloomFilter(List<String> certHashes) {
        try (Jedis jedis = getJedis()) {
        if (certHashes == null || certHashes.isEmpty()) {
            return new boolean[0];
        }

        try {
            jedis.select(BLOOM_FILTER_DB);

            // 准备命令参数
            byte[][] args = new byte[certHashes.size() + 1][];
            args[0] = SafeEncoder.encode(CERT_BLOOM_FILTER_KEY);
            for (int i = 0; i < certHashes.size(); i++) {
                args[i + 1] = SafeEncoder.encode(certHashes.get(i));
            }

            // 执行批量添加
            Object response = jedis.sendCommand(Protocol.Command.valueOf("BF.MADD"), args);

            // 解析响应
            List<Object> results = (List<Object>) response;
            boolean[] added = new boolean[results.size()];
            for (int i = 0; i < results.size(); i++) {
                added[i] = Integer.parseInt(results.get(i).toString()) == 1;
            }

            return added;
        } catch (Exception e) {
            log.error("批量添加到布隆过滤器失败: {}", e.getMessage());
            return new boolean[certHashes.size()];
        }
    }

    /**
     * 使用精确键检查证书是否存在
     * 
     * @param cert 证书对象
     * @return 如果证书存在返回true，否则返回false
     */
    public static boolean checkExactKey(X509Certificate cert) {
        try (Jedis jedis = getJedis()) {
        String key = cert.getDerSha1() + "-" + cert.getSource();
        try {
            jedis.select(EXACT_KEYS_DB);
            return jedis.exists(key);
        } catch (Exception e) {
            log.error("检查精确键失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 将证书添加到精确键
     * 
     * @param cert 证书对象
     */
    public static void addExactKey(X509Certificate cert) {
        try (Jedis jedis = getJedis()) {
        String key = cert.getDerSha1() + "-" + cert.getSource();
        try {
            jedis.select(EXACT_KEYS_DB);
            jedis.setex(key, EXACT_KEY_EXPIRY, "1");
        } catch (Exception e) {
            log.error("添加精确键失败: {}", e.getMessage());
        }
    }

    /**
     * 使用布隆过滤器和精确键进行证书去重
     * 
     * @param cert 证书对象
     * @return 如果是重复证书返回true，否则返回false
     */
    public static boolean checkCertDeduplication(X509Certificate cert) {
        // 首先检查布隆过滤器
        boolean mightExist = checkBloomFilter(cert);

        if (!mightExist) {
            // 证书肯定不存在，添加到布隆过滤器
            addToBloomFilter(cert);
            // 添加精确键
            addExactKey(cert);
            return false;
        } else {
            // 证书可能存在，检查精确键
            boolean exactExists = checkExactKey(cert);

            if (!exactExists) {
                // 布隆过滤器误判，添加精确键
                addExactKey(cert);
                return false;
            } else {
                // 确认是重复的
                return true;
            }
        }
    }
}
